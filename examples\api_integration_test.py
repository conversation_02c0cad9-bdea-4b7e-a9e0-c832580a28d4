#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API集成测试脚本
测试完整的API端到端功能
"""

import requests
import json
import time
from typing import Dict, Any


class APIIntegrationTester:
    """API集成测试器"""

    def __init__(self, base_url: str = "http://localhost:8088"):
        """
        初始化测试器

        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        print("\n1. 测试健康检查接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 健康检查成功")
            print(f"   状态: {data['status']}")
            print(f"   服务: {data['service']}")
            print(f"   版本: {data['version']}")

            return True

        except Exception as e:
            print(f"❌ 健康检查失败: {str(e)}")
            return False

    def test_service_status(self) -> bool:
        """测试服务状态接口"""
        print("\n2. 测试服务状态接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/api/v1/service-status")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 服务状态获取成功")
            print(f"   状态: {data['status']}")

            # 显示合规性服务状态
            compliance_service = data.get("compliance_service", {})
            service_info = compliance_service.get("service_info", {})
            print(f"   服务名称: {service_info.get('service_name', 'N/A')}")
            print(f"   版本: {service_info.get('version', 'N/A')}")

            # 显示健康状态
            health_status = compliance_service.get("health_status", {})
            print(f"   组件健康状态:")
            for component, healthy in health_status.items():
                status_text = "✅" if healthy else "❌"
                print(f"     {component}: {status_text}")

            return True

        except Exception as e:
            print(f"❌ 服务状态获取失败: {str(e)}")
            return False

    def test_enums_endpoint(self) -> bool:
        """测试枚举值接口"""
        print("\n3. 测试枚举值接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/api/v1/enums")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 枚举值获取成功")

            enum_data = data.get("data", {})
            for enum_name, values in enum_data.items():
                print(f"   {enum_name}: {len(values)}个值")

            return True

        except Exception as e:
            print(f"❌ 枚举值获取失败: {str(e)}")
            return False

    def test_processing_capability(self) -> bool:
        """测试处理能力接口"""
        print("\n4. 测试处理能力接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/api/v1/processing-capability")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 处理能力获取成功")

            # 显示文件处理器信息
            file_processor = data.get("file_processor", {})
            print(f"   文件处理器:")
            print(
                f"     MarkItDown可用: {file_processor.get('markitdown_available', False)}"
            )
            print(f"     支持格式: {file_processor.get('supported_formats', [])}")

            return True

        except Exception as e:
            print(f"❌ 处理能力获取失败: {str(e)}")
            return False

    def test_processing_metrics(self) -> bool:
        """测试处理指标接口"""
        print("\n5. 测试处理指标接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/api/v1/processing-metrics")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 处理指标获取成功")
            print(f"   消息: {data.get('message', 'N/A')}")

            # 显示流水线指标
            metrics_data = data.get("data", {})
            pipeline_metrics = metrics_data.get("pipeline_metrics", {})
            print(f"   流水线指标:")
            print(f"     总请求数: {pipeline_metrics.get('total_requests', 0)}")
            print(f"     成功率: {pipeline_metrics.get('success_rate', 0):.1%}")

            return True

        except Exception as e:
            print(f"❌ 处理指标获取失败: {str(e)}")
            return False

    def test_file_validation(self) -> bool:
        """测试文件验证接口"""
        print("\n6. 测试文件验证接口")
        print("-" * 40)

        try:
            # 创建测试文件信息
            file_info = {
                "filename": "test_document.docx",
                "extension": ".docx",
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 1024 * 1024,  # 1MB
                "url": "http://example.com/test_document.docx",
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/validate-file", json=file_info
            )
            response.raise_for_status()

            data = response.json()
            print(f"✅ 文件验证成功")

            # 显示处理能力
            capability = data.get("processing_capability", {})
            print(f"   可处理: {capability.get('can_process', False)}")
            print(f"   首选方法: {capability.get('preferred_method', 'N/A')}")
            print(f"   估算时间: {capability.get('estimated_time', 0):.1f}秒")

            warnings = capability.get("warnings", [])
            if warnings:
                print(f"   警告: {', '.join(warnings)}")

            return True

        except Exception as e:
            print(f"❌ 文件验证失败: {str(e)}")
            return False

    def test_sensitive_word_stats(self) -> bool:
        """测试敏感词统计接口"""
        print("\n7. 测试敏感词统计接口")
        print("-" * 40)

        try:
            response = self.session.get(f"{self.base_url}/api/v1/sensitive-word-stats")
            response.raise_for_status()

            data = response.json()
            print(f"✅ 敏感词统计获取成功")

            # 显示健康状态
            health = data.get("health", False)
            print(f"   健康状态: {'✅' if health else '❌'}")

            # 显示服务信息
            service_info = data.get("service_info", {})
            print(f"   基础URL: {service_info.get('base_url', 'N/A')}")
            print(f"   超时时间: {service_info.get('timeout', 0)}秒")

            return True

        except Exception as e:
            print(f"❌ 敏感词统计获取失败: {str(e)}")
            return False

    def test_compliance_check_mock(self) -> bool:
        """测试合规性检查接口（模拟请求）"""
        print("\n8. 测试合规性检查接口（模拟）")
        print("-" * 40)

        try:
            # 创建模拟请求
            request_data = {
                "procurement_project_type": "货物类",  # 修正枚举值
                "project_category": "政府采购",
                "bidding_procurement_method": "公开招标",
                "bidding_doc": {
                    "filename": "mock_document.docx",
                    "extension": ".docx",
                    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "size": 1024 * 1024,  # 1MB
                    "url": "http://example.com/mock_document.docx",
                },
            }

            print(f"   注意: 这是一个模拟请求，使用无效的URL")
            print(f"   文件名: {request_data['bidding_doc']['filename']}")
            print(f"   项目类型: {request_data['procurement_project_type']}")

            # 注意：这个请求会失败，因为URL无效，但可以测试API的错误处理
            response = self.session.post(
                f"{self.base_url}/api/v1/check-compliance", json=request_data
            )

            if response.status_code == 200:
                data = response.json()
                print(f"✅ 合规性检查成功（意外）")
                print(f"   敏感词数量: {len(data.get('sensitiveWordsArr', []))}")
                print(f"   检查结果数量: {len(data.get('checkResultArr', []))}")
                return True
            else:
                print(f"⚠️  合规性检查失败（预期）: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data.get('message', 'N/A')}")
                except:
                    print(f"   响应内容: {response.text[:200]}...")
                return True  # 预期的失败也算测试通过

        except Exception as e:
            print(f"❌ 合规性检查测试异常: {str(e)}")
            return False

    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("API集成测试开始")
        print("=" * 60)

        tests = [
            ("健康检查", self.test_health_check),
            ("服务状态", self.test_service_status),
            ("枚举值", self.test_enums_endpoint),
            ("处理能力", self.test_processing_capability),
            ("处理指标", self.test_processing_metrics),
            ("文件验证", self.test_file_validation),
            ("敏感词统计", self.test_sensitive_word_stats),
            ("合规性检查", self.test_compliance_check_mock),
        ]

        results = {}
        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ 测试 '{test_name}' 异常: {str(e)}")
                results[test_name] = False

        # 显示测试总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed / total:.1%}")

        print("\n详细结果:")
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")

        return results


def main():
    """主函数"""
    print("招标文件合规性检查助手 - API集成测试")
    print("=" * 60)
    print("注意: 请确保服务已启动在 http://localhost:8088")
    print("启动命令: python main.py")
    print()

    # 等待用户确认
    input("按回车键开始测试...")

    # 创建测试器并运行测试
    tester = APIIntegrationTester()
    results = tester.run_all_tests()

    # 根据测试结果返回退出码
    all_passed = all(results.values())
    exit_code = 0 if all_passed else 1

    print(f"\n测试完成，退出码: {exit_code}")
    return exit_code


if __name__ == "__main__":
    exit(main())
