#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试覆盖率检查脚本
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def install_coverage():
    """安装coverage包"""
    try:
        import coverage

        print("✅ coverage包已安装")
        return True
    except ImportError:
        print("📦 安装coverage包...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "coverage"])
            print("✅ coverage包安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ coverage包安装失败: {e}")
            return False


def run_coverage_analysis():
    """运行测试覆盖率分析"""

    project_root = Path(__file__).parent.parent
    os.chdir(project_root)

    print("=" * 60)
    print("开始运行测试覆盖率分析")
    print("=" * 60)

    try:
        # 1. 清理之前的覆盖率数据
        print("🧹 清理之前的覆盖率数据...")
        subprocess.run([sys.executable, "-m", "coverage", "erase"], check=False)

        # 2. 运行测试并收集覆盖率数据
        print("🧪 运行测试并收集覆盖率数据...")

        # 获取所有测试文件
        test_files = []
        tests_dir = project_root / "tests"

        for test_file in tests_dir.glob("test_*.py"):
            if test_file.name not in [
                "test_load_performance.py",
                "test_with_real_file.py",
            ]:
                test_files.append(str(test_file))

        print(f"发现 {len(test_files)} 个测试文件")

        # 运行每个测试文件
        for test_file in test_files:
            print(f"  运行: {os.path.basename(test_file)}")
            try:
                result = subprocess.run(
                    [
                        sys.executable,
                        "-m",
                        "coverage",
                        "run",
                        "--append",
                        "--source=app",
                        test_file,
                    ],
                    capture_output=True,
                    text=True,
                    timeout=60,
                )

                if result.returncode != 0:
                    print(f"    ⚠️  测试失败: {result.stderr}")
                else:
                    print(f"    ✅ 测试通过")

            except subprocess.TimeoutExpired:
                print(f"    ⏰ 测试超时")
            except Exception as e:
                print(f"    ❌ 测试异常: {e}")

        # 3. 生成覆盖率报告
        print("\n📊 生成覆盖率报告...")

        # 生成终端报告
        result = subprocess.run(
            [
                sys.executable,
                "-m",
                "coverage",
                "report",
                "--show-missing",
                "--skip-covered",
            ],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            print("覆盖率报告:")
            print(result.stdout)
        else:
            print(f"生成报告失败: {result.stderr}")

        # 4. 生成HTML报告
        print("🌐 生成HTML覆盖率报告...")
        html_result = subprocess.run(
            [sys.executable, "-m", "coverage", "html", "--directory=coverage_html"],
            capture_output=True,
            text=True,
        )

        if html_result.returncode == 0:
            print("✅ HTML报告已生成到 coverage_html/ 目录")
        else:
            print(f"HTML报告生成失败: {html_result.stderr}")

        # 5. 获取总体覆盖率
        json_result = subprocess.run(
            [sys.executable, "-m", "coverage", "json"], capture_output=True, text=True
        )

        if json_result.returncode == 0:
            try:
                coverage_data = json.loads(json_result.stdout)
                total_coverage = coverage_data.get("totals", {}).get(
                    "percent_covered", 0
                )

                print(f"\n📈 总体覆盖率: {total_coverage:.1f}%")

                if total_coverage >= 85:
                    print("🎉 覆盖率达到85%以上的目标!")
                    return True
                else:
                    print(
                        f"⚠️  覆盖率未达到85%目标，还需提高 {85 - total_coverage:.1f}%"
                    )
                    return False

            except json.JSONDecodeError:
                print("❌ 无法解析覆盖率JSON数据")
                return False
        else:
            print(f"获取覆盖率数据失败: {json_result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 覆盖率分析失败: {e}")
        return False


def analyze_missing_coverage():
    """分析缺失的测试覆盖"""

    print("\n" + "=" * 60)
    print("分析缺失的测试覆盖")
    print("=" * 60)

    try:
        # 获取详细的覆盖率报告
        result = subprocess.run(
            [sys.executable, "-m", "coverage", "report", "--show-missing"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            lines = result.stdout.split("\n")

            print("需要补充测试的模块:")
            for line in lines:
                if (
                    line.strip()
                    and not line.startswith("Name")
                    and not line.startswith("---")
                ):
                    parts = line.split()
                    if len(parts) >= 4:
                        name = parts[0]
                        coverage = parts[3].rstrip("%")

                        try:
                            coverage_pct = float(coverage)
                            if coverage_pct < 85:
                                missing = parts[4] if len(parts) > 4 else ""
                                print(f"  📁 {name}: {coverage}% 覆盖率 {missing}")
                        except ValueError:
                            continue

        return True

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def main():
    """主函数"""

    print("🚀 开始测试覆盖率检查")

    # 1. 安装coverage包
    if not install_coverage():
        return False

    # 2. 运行覆盖率分析
    success = run_coverage_analysis()

    # 3. 分析缺失的覆盖
    analyze_missing_coverage()

    # 4. 输出结果
    print("\n" + "=" * 60)
    print("覆盖率检查完成")
    print("=" * 60)

    if success:
        print("✅ 测试覆盖率达标")
    else:
        print("⚠️  测试覆盖率需要改进")
        print("💡 建议:")
        print("  1. 查看 coverage_html/index.html 了解详细覆盖情况")
        print("  2. 为覆盖率低的模块补充单元测试")
        print("  3. 重点关注核心业务逻辑的测试覆盖")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
