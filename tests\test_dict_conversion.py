#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dict转换问题
"""

import time
import sys

sys.path.append(".")

from app.models.schemas import FileInfo
from app.models.enums import FileExtension, MimeType


def test_fileinfo_dict_conversion():
    """测试FileInfo的dict转换"""
    print("测试FileInfo的dict转换")
    print("=" * 50)

    # 创建FileInfo对象
    print("创建FileInfo对象...")
    start_time = time.time()

    file_info = FileInfo(
        filename="test.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=1024,
        url="http://example.com/test.docx",
    )

    elapsed = time.time() - start_time
    print(f"FileInfo创建耗时: {elapsed:.3f}秒")

    # 测试dict()转换
    print("测试dict()转换...")
    start_time = time.time()

    try:
        file_dict = file_info.dict()
        elapsed = time.time() - start_time
        print(f"dict()转换耗时: {elapsed:.3f}秒")
        print(f"转换结果: {file_dict}")
        return True
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"dict()转换失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")
        return False


def test_alternative_serialization():
    """测试替代的序列化方法"""
    print("\n测试替代的序列化方法")
    print("=" * 50)

    # 创建FileInfo对象
    file_info = FileInfo(
        filename="test.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=1024,
        url="http://example.com/test.docx",
    )

    # 测试model_dump()
    print("测试model_dump()...")
    start_time = time.time()

    try:
        file_dump = file_info.model_dump()
        elapsed = time.time() - start_time
        print(f"model_dump()耗时: {elapsed:.3f}秒")
        print(f"转换结果: {file_dump}")
        return True
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"model_dump()失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")

        # 尝试手动序列化
        print("尝试手动序列化...")
        start_time = time.time()

        try:
            manual_dict = {
                "filename": file_info.filename,
                "extension": file_info.extension.value,
                "mime_type": file_info.mime_type.value,
                "size": file_info.size,
                "url": str(file_info.url),
            }
            elapsed = time.time() - start_time
            print(f"手动序列化耗时: {elapsed:.3f}秒")
            print(f"转换结果: {manual_dict}")
            return True
        except Exception as e2:
            elapsed = time.time() - start_time
            print(f"手动序列化失败，耗时: {elapsed:.3f}秒，错误: {str(e2)}")
            return False


def main():
    """主函数"""
    print("FileInfo序列化问题测试")
    print("=" * 60)

    # 测试dict转换
    dict_ok = test_fileinfo_dict_conversion()

    # 测试替代方法
    alt_ok = test_alternative_serialization()

    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if not dict_ok:
        print("🔍 问题确认：file_info.dict() 方法有问题")
        print("   这可能是导致API端点超时的原因")

        if alt_ok:
            print("✅ 替代序列化方法正常")
            print("   建议：在API端点中使用替代方法")
        else:
            print("❌ 所有序列化方法都有问题")
    else:
        print("✅ FileInfo序列化正常")
        print("   问题可能在其他地方")


if __name__ == "__main__":
    main()
