# 招标文件合规性检查助手 - Docker Compose配置
version: '3.8'

services:
  # 主应用服务
  compliance-checker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: compliance-checker
    ports:
      - "8088:8088"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./data:/app/data  # 可选：持久化数据目录
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    networks:
      - compliance-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: compliance-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - compliance-checker
    restart: unless-stopped
    networks:
      - compliance-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  compliance-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes: {}