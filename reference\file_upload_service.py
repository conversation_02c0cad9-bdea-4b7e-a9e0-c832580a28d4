#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传服务模块
提供文件上传到MinIO的功能
"""

import requests
from typing import Optional, Dict, Any
from utils.log_cfg import log
import time


class FileUploadService:
    """文件上传服务类"""

    # def __init__(self, base_url: str = "http://172.18.10.70:4003"):
    def __init__(self, base_url: str = "http://172.18.7.27:4003"):
        """
        初始化文件上传服务

        Args:
            base_url: 文件服务器的基础URL
        """
        self.base_url = base_url
        self.presign_url_endpoint = (
            f"{base_url}/xinecaiFile/budget/minio-file/put-presign-url"
        )
        self.complete_endpoint = (
            f"{base_url}/xinecaiFile/budget/minio-file/put-presign-url/complete"
        )

    def get_presigned_url(
        self, object_name: str, file_size: int
    ) -> Optional[Dict[str, Any]]:
        """
        获取预签名上传URL

        Args:
            object_name: 对象名称（文件名）
            file_size: 文件大小（字节）

        Returns:
            包含预签名URL和上传ID的字典，失败时返回None
        """
        try:
            url = f"{self.presign_url_endpoint}?fileSize={file_size}&objectName={object_name}"

            log.info(f"获取预签名URL: {url}")
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            result = response.json()
            log.info(f"获取预签名URL成功: {result}")

            # 提取上传URL和上传ID
            # 根据实际API响应格式调整
            if "result" in result:
                data = result["result"]
                return {
                    "upload_url": data.get("presignedUrl"),
                    "upload_id": data.get("key"),  # 使用key作为upload_id
                    "object_name": object_name,
                }
            elif "data" in result:
                data = result["data"]
                return {
                    "upload_url": data.get("uploadUrl"),
                    "upload_id": data.get("uploadId"),
                    "object_name": object_name,
                }
            else:
                log.error(f"预签名URL响应格式异常: {result}")
                return None

        except Exception as e:
            log.error(f"获取预签名URL失败: {e}")
            return None

    def upload_file_content(
        self, upload_url: str, file_content: bytes, content_type: str
    ) -> bool:
        """
        上传文件内容到预签名URL

        Args:
            upload_url: 预签名上传URL
            file_content: 文件内容（字节）
            content_type: 文件MIME类型

        Returns:
            上传是否成功
        """
        try:
            headers = {"Content-Type": content_type}

            # 根据文件大小动态设置超时时间
            file_size_mb = len(file_content) / (1024 * 1024)
            if file_size_mb > 50:
                timeout = 600  # 10分钟，适用于超大文件（>50MB）
            elif file_size_mb > 20:
                timeout = 300  # 5分钟，适用于大文件（20-50MB）
            elif file_size_mb > 5:
                timeout = 180  # 3分钟，适用于中等文件（5-20MB）
            else:
                timeout = 120  # 2分钟，适用于小文件（<5MB）

            log.info(
                f"开始上传文件，大小: {len(file_content)} 字节 ({file_size_mb:.1f}MB)，超时时间: {timeout}秒"
            )
            response = requests.put(
                upload_url, headers=headers, data=file_content, timeout=timeout
            )
            response.raise_for_status()

            log.info("文件内容上传成功")
            return True

        except Exception as e:
            log.error(f"文件内容上传失败: {e}")
            return False

    def complete_upload(self, upload_id: str) -> bool:
        """
        完成文件上传

        Args:
            upload_id: 上传ID

        Returns:
            完成是否成功
        """
        try:
            url = f"{self.complete_endpoint}/{upload_id}"

            log.info(f"完成文件上传: {url}")
            response = requests.put(url, timeout=30)
            response.raise_for_status()

            log.info("文件上传完成")
            return True

        except Exception as e:
            log.error(f"完成文件上传失败: {e}")
            return False

    def upload_file(
        self,
        file_content: bytes,
        object_name: str,
        content_type: str = "application/octet-stream",
    ) -> tuple[bool, str]:
        """
        完整的文件上传流程

        Args:
            file_content: 文件内容（字节）
            object_name: 对象名称（文件名）
            content_type: 文件MIME类型

        Returns:
            (上传是否成功, upload_id)
        """
        try:
            file_size = len(file_content)
            log.info(f"开始上传文件: {object_name}, 大小: {file_size} 字节")

            # 1. 获取预签名URL
            presign_result = self.get_presigned_url(object_name, file_size)
            if not presign_result:
                log.error("获取预签名URL失败")
                return False, ""

            upload_url = presign_result["upload_url"]
            upload_id = presign_result["upload_id"]

            # 2. 上传文件内容
            if not self.upload_file_content(upload_url, file_content, content_type):
                log.error("上传文件内容失败")
                return False, ""

            # 3. 完成上传
            if not self.complete_upload(upload_id):
                log.error("完成上传失败")
                return False, ""

            log.info(f"文件上传成功: {object_name}")
            return True, upload_id

        except Exception as e:
            log.error(f"文件上传过程中发生异常: {e}")
            return False, ""

    def upload_file_with_retry(
        self,
        file_content: bytes,
        object_name: str,
        content_type: str = "application/octet-stream",
        max_retries: int = 3,
    ) -> tuple[bool, str]:
        """
        带重试机制的文件上传

        Args:
            file_content: 文件内容（字节）
            object_name: 对象名称（文件名）
            content_type: 文件MIME类型
            max_retries: 最大重试次数

        Returns:
            (上传是否成功, upload_id)
        """
        for attempt in range(max_retries):
            try:
                log.info(f"文件上传尝试 {attempt + 1}/{max_retries}: {object_name}")

                success, upload_id = self.upload_file(
                    file_content, object_name, content_type
                )
                if success:
                    return True, upload_id

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间：2s, 4s, 6s
                    log.info(f"上传失败，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except Exception as e:
                log.error(f"上传尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    log.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

        log.error(f"文件上传最终失败: {object_name}")
        return False, ""


def get_content_type(file_ext: str) -> str:
    """
    根据文件扩展名获取MIME类型

    Args:
        file_ext: 文件扩展名（如 .pdf, .docx）

    Returns:
        MIME类型字符串
    """
    content_type_map = {
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".zip": "application/zip",
        ".rar": "application/x-rar-compressed",
        ".7z": "application/x-7z-compressed",
        ".txt": "text/plain",
        ".json": "application/json",
        ".xml": "application/xml",
    }

    return content_type_map.get(file_ext.lower(), "application/octet-stream")


# 创建全局文件上传服务实例
file_upload_service = FileUploadService()


def upload_document_file(
    file_content: bytes, source_id: str, file_type: str, file_ext: str
) -> tuple[bool, str]:
    """
    上传文档文件的便捷函数

    Args:
        file_content: 文件内容（字节）
        source_id: 文档的唯一标识符
        file_type: 文件类型（"招标文件" 或 "合同文件"）
        file_ext: 文件扩展名

    Returns:
        (上传是否成功, upload_id)
    """
    try:
        # 构建文件名
        object_name = f"{file_type}_{source_id}{file_ext}"

        # 获取MIME类型
        content_type = get_content_type(file_ext)

        # 上传文件
        return file_upload_service.upload_file_with_retry(
            file_content=file_content,
            object_name=object_name,
            content_type=content_type,
            max_retries=3,
        )

    except Exception as e:
        log.error(f"上传文档文件失败: {e}")
        return False, ""


def upload_attachment_file(
    file_content: bytes, source_id: str, original_filename: str, file_ext: str
) -> tuple[bool, str]:
    """
    上传附件文件的便捷函数（用于所有附件，不仅限于招标/合同文件）

    Args:
        file_content: 文件内容（字节）
        source_id: 文档的唯一标识符（保留参数以保持兼容性，但不使用）
        original_filename: 原始文件名（来自source_appendix.text）
        file_ext: 文件扩展名

    Returns:
        (上传是否成功, upload_id)
    """
    try:
        # 直接使用原始文件名，不添加source_id后缀
        # 如果原始文件名已包含扩展名，直接使用；否则添加扩展名
        if original_filename.endswith(file_ext):
            object_name = original_filename
        else:
            object_name = f"{original_filename}{file_ext}"

        # 获取MIME类型
        content_type = get_content_type(file_ext)

        # 上传文件
        return file_upload_service.upload_file_with_retry(
            file_content=file_content,
            object_name=object_name,
            content_type=content_type,
            max_retries=3,
        )

    except Exception as e:
        log.error(f"上传附件文件失败: {e}")
        return False, ""
