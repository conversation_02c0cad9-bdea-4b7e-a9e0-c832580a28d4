# -*- coding: utf-8 -*-
"""
合规性检查主业务流程控制器
集成所有组件实现完整的业务流程
"""

import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.core.config import settings
from app.core.logger import (
    log,
    performance_logger,
    performance_monitor,
    TimingContext,
    log_function_call,
)
from app.core.exceptions import (
    BusinessLogicError,
    FileProcessingError,
    AIModelError,
    ExternalServiceError,
    FallbackManager,
)
from app.models.schemas import (
    ComplianceCheckRequest,
    ComplianceCheckResponse,
    ProjectInfo,
    SensitiveWordItem,
    CheckResultItem,
)

# 导入所有服务组件
from app.services.ai_model_service import ai_model_service
from app.services.sensitive_word_service import sensitive_word_service
from app.services.result_processor import result_processor

# 导入文件处理器（优先使用优化版本）
try:
    from app.services.file_processor_v2 import (
        optimized_file_processor as file_processor,
    )
except ImportError:
    from app.services.file_processor import file_processor


class ComplianceCheckPipeline:
    """合规性检查流水线"""

    def __init__(self):
        """初始化流水线"""
        self.fallback_manager = FallbackManager()
        self.pipeline_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "fallback_requests": 0,
            "average_processing_time": 0.0,
        }

    @log_function_call
    def validate_prerequisites(self, request_id: str = "") -> Dict[str, bool]:
        """
        验证所有服务的前置条件

        Args:
            request_id: 请求ID

        Returns:
            Dict[str, bool]: 各服务的健康状态
        """
        log.info(f"开始验证服务前置条件 | ID: {request_id}")

        health_status = {
            "file_processor": True,  # 文件处理器总是可用
            "ai_model": False,
            "sensitive_word": False,
            "result_processor": True,  # 结果处理器总是可用
        }

        try:
            # 检查AI模型服务
            ai_info = ai_model_service.get_model_info()
            health_status["ai_model"] = ai_info.get("client_initialized", False)

            # 检查敏感词服务
            health_status["sensitive_word"] = sensitive_word_service.check_health(
                request_id
            )

            log.info(f"服务健康检查完成 | ID: {request_id} | 状态: {health_status}")

        except Exception as e:
            log.error(f"服务健康检查异常 | ID: {request_id} | 错误: {str(e)}")

        return health_status

    @log_function_call
    def process_file_stage(self, file_info, request_id: str = "") -> Optional[str]:
        """
        文件处理阶段

        Args:
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            Optional[str]: 处理后的文件内容，失败时返回None
        """
        start_time = time.time()
        try:
            with TimingContext("文件处理阶段", request_id):
                log.info(
                    f"开始文件处理阶段 | ID: {request_id} | 文件: {file_info.filename}"
                )

                # 检查文件处理能力
                capability = file_processor.validate_processing_capability(file_info)

                if not capability["can_process"]:
                    raise FileProcessingError(
                        f"文件无法处理: {', '.join(capability['warnings'])}",
                        filename=file_info.filename,
                        file_type=file_info.extension.value,
                    )

                # 处理文件
                content = file_processor.process_file(file_info, request_id)

                log.info(
                    f"文件处理完成 | ID: {request_id} | 内容长度: {len(content)} 字符"
                )

                # 记录处理统计
                performance_logger.log_file_processing(
                    request_id,
                    file_info.filename,
                    file_info.size,
                    0,  # 时间由TimingContext记录
                )

                # 记录性能监控
                duration = time.time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "file_processing", duration, True
                )

                return content

        except FileProcessingError as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "file_processing", duration, False
            )
            raise
        except Exception as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "file_processing", duration, False
            )
            raise FileProcessingError(
                f"文件处理阶段失败: {str(e)}",
                filename=file_info.filename,
                original_error=e,
            )

    @log_function_call
    def ai_compliance_check_stage(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> List[CheckResultItem]:
        """
        AI合规性检查阶段

        Args:
            content: 文件内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            List[CheckResultItem]: 检查结果列表
        """
        start_time = time.time()
        try:
            with TimingContext("AI合规性检查阶段", request_id):
                log.info(f"开始AI合规性检查 | ID: {request_id}")

                # 调用AI模型服务
                ai_result = ai_model_service.check_compliance(
                    content, project_info, request_id
                )

                log.info(
                    f"AI合规性检查完成 | ID: {request_id} | "
                    f"发现问题: {len(ai_result.data.checkResultArr)}个"
                )

                # 记录成功的性能监控
                duration = time.time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "ai_model_call", duration, True
                )

                return ai_result.data.checkResultArr

        except AIModelError as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "ai_model_call", duration, False
            )
            raise
        except Exception as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "ai_model_call", duration, False
            )
            raise AIModelError(
                f"AI合规性检查阶段失败: {str(e)}",
                model_name=settings.model_name,
                original_error=e,
            )

    @log_function_call
    def sensitive_word_check_stage(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> List[SensitiveWordItem]:
        """
        敏感词检测阶段

        Args:
            content: 文件内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            List[SensitiveWordItem]: 敏感词列表
        """
        start_time = time.time()
        try:
            with TimingContext("敏感词检测阶段", request_id):
                log.info(f"开始敏感词检测 | ID: {request_id}")

                # 使用带降级机制的检测，设置60秒超时
                sensitive_words = sensitive_word_service.detect_with_fallback(
                    content, project_info, request_id, timeout=60.0
                )

                log.info(
                    f"敏感词检测完成 | ID: {request_id} | "
                    f"发现敏感词: {len(sensitive_words)}个"
                )

                # 记录成功的性能监控
                duration = time.time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "sensitive_word", duration, True
                )

                return sensitive_words

        except ExternalServiceError as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "sensitive_word", duration, False
            )
            raise
        except Exception as e:
            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "sensitive_word", duration, False
            )
            raise ExternalServiceError(
                f"敏感词检测阶段失败: {str(e)}",
                service_name="sensitive_word_api",
                original_error=e,
            )

    @log_function_call
    def result_aggregation_stage(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str = "",
    ) -> ComplianceCheckResponse:
        """
        结果聚合阶段

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 最终结果
        """
        start_time = time.time()
        try:
            with TimingContext("结果聚合阶段", request_id):
                log.info(f"开始结果聚合 | ID: {request_id}")

                # 使用带降级机制的结果处理
                final_result = result_processor.process_with_fallback(
                    sensitive_words, check_results, request_id
                )

                log.info(
                    f"结果聚合完成 | ID: {request_id} | "
                    f"敏感词: {len(final_result.data.sensitiveWordsArr)}个 | "
                    f"检查结果: {len(final_result.data.checkResultArr)}个"
                )

                # 记录成功的性能监控
                duration = time.time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "result_aggregation", duration, True
                )

                return final_result

        except Exception as e:
            log.error(f"结果聚合阶段失败 | ID: {request_id} | 错误: {str(e)}")

            # 记录失败的性能监控
            duration = time.time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "result_aggregation", duration, False
            )

            # 结果聚合失败时返回空结果
            return result_processor.create_empty_response("结果聚合失败")

    @log_function_call
    def execute_pipeline(
        self,
        request: ComplianceCheckRequest,
        request_id: str = "",
        pipeline_timeout: float = 300.0,
    ) -> ComplianceCheckResponse:
        """
        执行完整的合规性检查流水线（带超时处理）

        Args:
            request: 合规性检查请求
            request_id: 请求ID
            pipeline_timeout: 流水线总超时时间（秒），默认5分钟

        Returns:
            ComplianceCheckResponse: 检查结果
        """
        start_time = time.time()
        pipeline_success = False
        fallback_used = False

        try:
            with TimingContext("完整合规性检查流水线", request_id):
                # 检查超时的辅助函数
                def check_timeout(stage_name: str):
                    elapsed = time.time() - start_time
                    if elapsed > pipeline_timeout:
                        raise TimeoutError(
                            f"合规性检查流水线超时: {elapsed:.1f}秒 > {pipeline_timeout}秒 "
                            f"(阶段: {stage_name})"
                        )
                    elif elapsed > pipeline_timeout * 0.8:  # 80%时发出警告
                        log.warning(
                            f"合规性检查流水线接近超时: {elapsed:.1f}秒 / {pipeline_timeout}秒 "
                            f"(阶段: {stage_name}) | ID: {request_id}"
                        )

                log.info(
                    f"开始执行合规性检查流水线 | ID: {request_id} | "
                    f"文件: {request.bidding_doc.filename}"
                )

                # 更新统计
                self.pipeline_stats["total_requests"] += 1

                # 1. 验证服务前置条件
                check_timeout("服务前置条件验证")
                log.info(f"步骤1: 开始验证服务前置条件 | ID: {request_id}")
                health_status = self.validate_prerequisites(request_id)
                log.info(
                    f"步骤1: 服务前置条件验证完成 | ID: {request_id} | 状态: {health_status}"
                )

                # 2. 获取项目信息
                check_timeout("项目信息获取")
                log.info(f"步骤2: 开始获取项目信息 | ID: {request_id}")
                project_info = request.get_project_info()
                log.info(f"步骤2: 项目信息获取完成 | ID: {request_id}")

                # 3. 文件处理阶段
                check_timeout("文件处理")
                log.info(f"步骤3: 开始文件处理阶段 | ID: {request_id}")
                try:
                    file_content = self.process_file_stage(
                        request.bidding_doc, request_id
                    )
                    log.info(
                        f"步骤3: 文件处理完成 | ID: {request_id} | 内容长度: {len(file_content) if file_content else 0}"
                    )
                except FileProcessingError as e:
                    log.error(
                        f"步骤3: 文件处理失败，无法继续 | ID: {request_id} | 错误: {str(e)}"
                    )
                    raise  # 文件处理失败无法降级，直接抛出异常

                # 4. AI合规性检查阶段
                check_timeout("AI合规性检查")
                log.info(f"步骤4: 开始AI合规性检查阶段 | ID: {request_id}")
                check_results = []
                if health_status["ai_model"]:
                    try:
                        check_results = self.ai_compliance_check_stage(
                            file_content, project_info, request_id
                        )
                        log.info(
                            f"步骤4: AI合规性检查完成 | ID: {request_id} | 结果数量: {len(check_results)}"
                        )
                    except AIModelError as e:
                        log.warning(
                            f"步骤4: AI合规性检查异常 | ID: {request_id} | 错误: {str(e)}"
                        )
                        if self.fallback_manager.should_fallback(e):
                            log.warning(f"AI检查失败，使用降级策略 | ID: {request_id}")
                            fallback_used = True
                        else:
                            raise
                else:
                    log.warning(f"AI模型服务不可用，跳过AI检查 | ID: {request_id}")
                    fallback_used = True

                # 5. 敏感词检测阶段
                check_timeout("敏感词检测")
                sensitive_words = []
                if health_status["sensitive_word"]:
                    try:
                        sensitive_words = self.sensitive_word_check_stage(
                            file_content, project_info, request_id
                        )
                    except ExternalServiceError as e:
                        if self.fallback_manager.should_fallback(e):
                            log.warning(
                                f"敏感词检测失败，使用降级策略 | ID: {request_id}"
                            )
                            fallback_used = True
                        else:
                            # 敏感词检测失败不影响主流程
                            log.warning(f"敏感词检测失败，继续处理 | ID: {request_id}")
                            fallback_used = True
                else:
                    log.warning(f"敏感词服务不可用，跳过敏感词检测 | ID: {request_id}")
                    fallback_used = True

                # 6. 结果聚合阶段
                check_timeout("结果聚合")
                final_result = self.result_aggregation_stage(
                    sensitive_words, check_results, request_id
                )

                # 7. 添加流水线元数据
                processing_time = time.time() - start_time
                self._add_pipeline_metadata(
                    final_result, processing_time, fallback_used, request_id
                )

                # 8. 记录总体流水线性能监控
                performance_monitor.record_stage_performance(
                    request_id, "total_pipeline", processing_time, True
                )

                pipeline_success = True
                log.info(
                    f"合规性检查流水线完成 | ID: {request_id} | "
                    f"耗时: {processing_time:.3f}秒 | 降级: {fallback_used}"
                )

                return final_result

        except TimeoutError as e:
            processing_time = time.time() - start_time
            log.error(
                f"合规性检查流水线超时 | ID: {request_id} | "
                f"耗时: {processing_time:.3f}秒 | 错误: {str(e)}"
            )

            # 超时时创建部分结果响应
            timeout_response = result_processor.create_empty_response(
                f"处理超时（{processing_time:.1f}秒）"
            )
            self._add_pipeline_metadata(
                timeout_response, processing_time, True, request_id
            )
            fallback_used = True
            return timeout_response

        except Exception as e:
            processing_time = time.time() - start_time
            log.error(
                f"合规性检查流水线失败 | ID: {request_id} | "
                f"耗时: {processing_time:.3f}秒 | 错误: {str(e)}"
            )

            # 尝试创建降级响应
            if isinstance(
                e, (AIModelError, ExternalServiceError, TimeoutError)
            ) and self.fallback_manager.should_fallback(e):
                fallback_response = self._create_fallback_response(e, request_id)
                self._add_pipeline_metadata(
                    fallback_response, processing_time, True, request_id
                )
                fallback_used = True
                return fallback_response
            else:
                raise

        finally:
            # 更新统计信息
            self._update_pipeline_stats(
                pipeline_success, fallback_used, time.time() - start_time
            )

    def _add_pipeline_metadata(
        self,
        response: ComplianceCheckResponse,
        processing_time: float,
        fallback_used: bool,
        request_id: str,
    ):
        """添加流水线元数据到响应中"""
        # 由于ComplianceCheckResponse模型限制，我们通过日志记录元数据
        log.info(
            f"流水线元数据 | ID: {request_id} | "
            f"处理时间: {processing_time:.3f}秒 | "
            f"降级使用: {fallback_used} | "
            f"敏感词数量: {len(response.data.sensitiveWordsArr)} | "
            f"检查结果数量: {len(response.data.checkResultArr)}"
        )

    def _create_fallback_response(
        self, error: Exception, request_id: str
    ) -> ComplianceCheckResponse:
        """创建降级响应"""
        reason = self.fallback_manager.get_fallback_strategy(error)
        log.warning(f"创建降级响应 | ID: {request_id} | 原因: {reason}")

        return result_processor.create_empty_response(reason)

    def _update_pipeline_stats(
        self, success: bool, fallback_used: bool, processing_time: float
    ):
        """更新流水线统计信息"""
        if success:
            self.pipeline_stats["successful_requests"] += 1
        else:
            self.pipeline_stats["failed_requests"] += 1

        if fallback_used:
            self.pipeline_stats["fallback_requests"] += 1

        # 更新平均处理时间
        total_requests = self.pipeline_stats["total_requests"]
        current_avg = self.pipeline_stats["average_processing_time"]
        self.pipeline_stats["average_processing_time"] = (
            current_avg * (total_requests - 1) + processing_time
        ) / total_requests

    def get_pipeline_stats(self) -> Dict[str, Any]:
        """获取流水线统计信息"""
        return {
            **self.pipeline_stats,
            "success_rate": (
                self.pipeline_stats["successful_requests"]
                / max(self.pipeline_stats["total_requests"], 1)
            ),
            "fallback_rate": (
                self.pipeline_stats["fallback_requests"]
                / max(self.pipeline_stats["total_requests"], 1)
            ),
        }

    def reset_stats(self):
        """重置统计信息"""
        self.pipeline_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "fallback_requests": 0,
            "average_processing_time": 0.0,
        }
        log.info("流水线统计信息已重置")


class ComplianceService:
    """合规性检查服务主类"""

    def __init__(self):
        """初始化服务"""
        self.pipeline = ComplianceCheckPipeline()
        self.service_info = {
            "service_name": "bidding-document-compliance-checker",
            "version": "1.0.0",
            "description": "招标文件合规性检查服务",
            "supported_formats": [".docx", ".pdf"],
            "max_file_size_mb": settings.max_file_size / 1024 / 1024,
            "features": [
                "AI合规性检查",
                "敏感词检测",
                "结果聚合处理",
                "降级机制",
                "性能监控",
            ],
        }

    @log_function_call
    def check_compliance(
        self, request: ComplianceCheckRequest, request_id: str = ""
    ) -> ComplianceCheckResponse:
        """
        执行合规性检查

        Args:
            request: 合规性检查请求
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 检查结果
        """
        try:
            log.info(f"合规性检查服务开始 | ID: {request_id}")

            # 执行流水线
            result = self.pipeline.execute_pipeline(request, request_id)

            log.info(f"合规性检查服务完成 | ID: {request_id}")
            return result

        except Exception as e:
            log.error(f"合规性检查服务失败 | ID: {request_id} | 错误: {str(e)}")
            raise BusinessLogicError(
                f"合规性检查失败: {str(e)}",
                operation="compliance_check",
                context={
                    "request_id": request_id,
                    "filename": request.bidding_doc.filename,
                },
                original_error=e,
            )

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        pipeline_stats = self.pipeline.get_pipeline_stats()
        health_status = self.pipeline.validate_prerequisites()

        return {
            "service_info": self.service_info,
            "health_status": health_status,
            "pipeline_stats": pipeline_stats,
            "timestamp": datetime.now().isoformat(),
        }

    def get_processing_metrics(self) -> Dict[str, Any]:
        """获取处理指标"""
        return {
            "pipeline_metrics": self.pipeline.get_pipeline_stats(),
            "service_health": self.pipeline.validate_prerequisites(),
            "performance_stats": performance_monitor.get_global_stats(),
            "component_info": {
                "file_processor": file_processor.get_processing_stats(),
                "ai_model": ai_model_service.get_model_info(),
                "sensitive_word": sensitive_word_service.get_service_info(),
                "result_processor": result_processor.get_processing_stats(),
            },
        }

    def get_request_performance_analysis(self, request_id: str) -> Dict[str, Any]:
        """获取特定请求的性能分析"""
        return performance_monitor.analyze_performance(request_id)

    def cleanup_performance_data(self, max_age_hours: int = 24):
        """清理过期的性能数据"""
        performance_monitor.cleanup_old_metrics(max_age_hours)


# 创建全局合规性检查服务实例
compliance_service = ComplianceService()
