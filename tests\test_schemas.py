#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型和Schema测试
"""

import sys
import os
import unittest
from pydantic import ValidationError

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.models.schemas import (
    FileInfo,
    ProjectInfo,
    ComplianceCheckRequest,
    SimpleComplianceCheckRequest,
    SensitiveWordItem,
    CheckResultItem,
    ComplianceCheckResponse,
)
from app.models.enums import (
    FileExtension,
    MimeType,
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    QuestionType,
)


class TestSchemas(unittest.TestCase):
    """数据模型测试类"""

    def test_file_info_valid(self):
        """测试有效的FileInfo创建"""

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        self.assertEqual(file_info.filename, "test.docx")
        self.assertEqual(file_info.extension, FileExtension.DOCX)
        self.assertEqual(file_info.mime_type, MimeType.DOCX)
        self.assertEqual(file_info.size, 1024)
        self.assertEqual(str(file_info.url), "http://example.com/test.docx")

    def test_file_info_invalid_size(self):
        """测试无效文件大小"""

        with self.assertRaises(ValidationError) as context:
            FileInfo(
                filename="test.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.DOCX,
                size=0,  # 无效大小
                url="http://example.com/test.docx",
            )

        self.assertIn("greater than 0", str(context.exception))

    def test_file_info_invalid_url(self):
        """测试无效URL"""

        with self.assertRaises(ValidationError):
            FileInfo(
                filename="test.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.DOCX,
                size=1024,
                url="not-a-valid-url",
            )

    def test_file_info_format_mismatch(self):
        """测试文件格式不匹配"""

        with self.assertRaises(ValidationError) as context:
            FileInfo(
                filename="test.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.PDF,  # 不匹配的MIME类型
                size=1024,
                url="http://example.com/test.docx",
            )

        self.assertIn("不匹配", str(context.exception))

    def test_project_info_valid(self):
        """测试有效的ProjectInfo创建"""

        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
        )

        self.assertEqual(
            project_info.procurement_project_type, ProcurementProjectType.SERVICE
        )
        self.assertEqual(
            project_info.project_category, ProjectCategory.GOVERNMENT_PROCUREMENT
        )
        self.assertEqual(
            project_info.bidding_procurement_method,
            BiddingProcurementMethod.OPEN_BIDDING,
        )

    def test_project_info_is_government_procurement(self):
        """测试政府采购判断"""

        # 政府采购
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
        )
        self.assertTrue(project_info.is_government_procurement())

        # 依法招标
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.LEGAL_BIDDING,
            bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
        )
        self.assertFalse(project_info.is_government_procurement())

    def test_compliance_check_request_valid(self):
        """测试有效的ComplianceCheckRequest创建"""

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        request = ComplianceCheckRequest(
            bidding_doc=file_info,
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
        )

        self.assertEqual(request.bidding_doc, file_info)
        self.assertEqual(
            request.procurement_project_type, ProcurementProjectType.SERVICE
        )

    def test_compliance_check_request_file_too_large(self):
        """测试文件过大的情况"""

        file_info = FileInfo(
            filename="large.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=400 * 1024 * 1024,  # 400MB，超过限制
            url="http://example.com/large.docx",
        )

        with self.assertRaises(ValidationError) as context:
            ComplianceCheckRequest(
                bidding_doc=file_info,
                procurement_project_type=ProcurementProjectType.SERVICE,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
            )

        self.assertIn("不能超过", str(context.exception))

    def test_compliance_check_request_get_project_info(self):
        """测试获取项目信息"""

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        request = ComplianceCheckRequest(
            bidding_doc=file_info,
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.OPEN_BIDDING,
        )

        project_info = request.get_project_info()

        self.assertIsInstance(project_info, ProjectInfo)
        self.assertEqual(
            project_info.procurement_project_type, ProcurementProjectType.SERVICE
        )
        self.assertEqual(
            project_info.project_category, ProjectCategory.GOVERNMENT_PROCUREMENT
        )
        self.assertEqual(
            project_info.bidding_procurement_method,
            BiddingProcurementMethod.OPEN_BIDDING,
        )

    def test_simple_compliance_check_request_valid(self):
        """测试有效的SimpleComplianceCheckRequest创建"""

        request = SimpleComplianceCheckRequest(
            procurement_project_type="服务类",
            project_category="政府采购",
            bidding_procurement_method="公开招标",
            file_url="http://example.com/test.docx",
        )

        self.assertEqual(request.procurement_project_type, "服务类")
        self.assertEqual(request.project_category, "政府采购")
        self.assertEqual(request.bidding_procurement_method, "公开招标")
        self.assertEqual(request.file_url, "http://example.com/test.docx")

    def test_simple_compliance_check_request_invalid_project_type(self):
        """测试无效的采购项目类型"""

        with self.assertRaises(ValidationError) as context:
            SimpleComplianceCheckRequest(
                procurement_project_type="无效类型",
                project_category="政府采购",
                bidding_procurement_method="公开招标",
                file_url="http://example.com/test.docx",
            )

        self.assertIn("无效的采购项目类型", str(context.exception))

    def test_simple_compliance_check_request_invalid_category(self):
        """测试无效的项目类别"""

        with self.assertRaises(ValidationError) as context:
            SimpleComplianceCheckRequest(
                procurement_project_type="服务类",
                project_category="无效类别",
                bidding_procurement_method="公开招标",
                file_url="http://example.com/test.docx",
            )

        self.assertIn("无效的项目类别", str(context.exception))

    def test_simple_compliance_check_request_invalid_method(self):
        """测试无效的招标采购方式"""

        with self.assertRaises(ValidationError) as context:
            SimpleComplianceCheckRequest(
                procurement_project_type="服务类",
                project_category="政府采购",
                bidding_procurement_method="无效方式",
                file_url="http://example.com/test.docx",
            )

        self.assertIn("无效的招标采购方式", str(context.exception))

    def test_simple_compliance_check_request_invalid_url(self):
        """测试无效的文件URL"""

        with self.assertRaises(ValidationError) as context:
            SimpleComplianceCheckRequest(
                procurement_project_type="服务类",
                project_category="政府采购",
                bidding_procurement_method="公开招标",
                file_url="ftp://example.com/test.docx",  # 不支持的协议
            )

        self.assertIn("必须以http://或https://开头", str(context.exception))

    def test_simple_compliance_check_request_get_project_info(self):
        """测试简化请求获取项目信息"""

        request = SimpleComplianceCheckRequest(
            procurement_project_type="服务类",
            project_category="政府采购",
            bidding_procurement_method="公开招标",
            file_url="http://example.com/test.docx",
        )

        project_info = request.get_project_info()

        self.assertIsInstance(project_info, ProjectInfo)
        self.assertEqual(
            project_info.procurement_project_type, ProcurementProjectType.SERVICE
        )
        self.assertEqual(
            project_info.project_category, ProjectCategory.GOVERNMENT_PROCUREMENT
        )
        self.assertEqual(
            project_info.bidding_procurement_method,
            BiddingProcurementMethod.OPEN_BIDDING,
        )

    def test_sensitive_word_item_valid(self):
        """测试有效的SensitiveWordItem创建"""

        item = SensitiveWordItem(type="政治敏感", content="敏感词", num=2)

        self.assertEqual(item.type, "政治敏感")
        self.assertEqual(item.content, "敏感词")
        self.assertEqual(item.num, 2)

    def test_sensitive_word_item_invalid_num(self):
        """测试无效的出现次数"""

        with self.assertRaises(ValidationError) as context:
            SensitiveWordItem(type="政治敏感", content="敏感词", num=0)  # 必须大于等于1

        self.assertIn("greater than or equal to 1", str(context.exception))

    def test_check_result_item_valid(self):
        """测试有效的CheckResultItem创建"""

        item = CheckResultItem(
            quesType=QuestionType.COMPLIANCE,
            quesDesc="存在合规性问题",
            originalArr=["原文1", "原文2"],
            point="质量控制要点",
            advice="处理建议",
        )

        self.assertEqual(item.quesType, QuestionType.COMPLIANCE)
        self.assertEqual(item.quesDesc, "存在合规性问题")
        self.assertEqual(item.originalArr, ["原文1", "原文2"])
        self.assertEqual(item.point, "质量控制要点")
        self.assertEqual(item.advice, "处理建议")

    def test_compliance_check_response_valid(self):
        """测试有效的ComplianceCheckResponse创建"""

        sensitive_word = SensitiveWordItem(type="政治敏感", content="敏感词", num=1)

        check_result = CheckResultItem(
            quesType=QuestionType.COMPLIANCE,
            quesDesc="存在合规性问题",
            originalArr=["原文"],
            point="质量控制要点",
            advice="处理建议",
        )

        response = ComplianceCheckResponse(
            sensitiveWordsArr=[sensitive_word], checkResultArr=[check_result]
        )

        self.assertEqual(len(response.sensitiveWordsArr), 1)
        self.assertEqual(len(response.checkResultArr), 1)
        self.assertEqual(response.sensitiveWordsArr[0], sensitive_word)
        self.assertEqual(response.checkResultArr[0], check_result)

    def test_compliance_check_response_empty(self):
        """测试空的ComplianceCheckResponse创建"""

        response = ComplianceCheckResponse()

        self.assertEqual(len(response.sensitiveWordsArr), 0)
        self.assertEqual(len(response.checkResultArr), 0)
        self.assertIsInstance(response.sensitiveWordsArr, list)
        self.assertIsInstance(response.checkResultArr, list)


def run_tests():
    """运行测试"""

    print("=" * 60)
    print("开始运行数据模型和Schema测试")
    print("=" * 60)

    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestSchemas)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"总测试数: {total_tests}")
    print(f"通过: {passed}")
    print(f"失败: {failures}")
    print(f"错误: {errors}")
    print(f"成功率: {passed/total_tests*100:.1f}%")

    if failures > 0:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")

    if errors > 0:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")

    success = failures == 0 and errors == 0

    if success:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n⚠️  有 {failures + errors} 个测试失败")

    return success


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
