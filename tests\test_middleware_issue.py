#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中间件问题
"""

import requests
import json
import time


def test_without_decorator():
    """测试不带装饰器的端点"""
    print("测试不带装饰器的端点（直接端点）")
    print("=" * 50)

    base_url = "http://localhost:8088"

    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/debug/validate-file-direct",
            json=file_info_data,
            timeout=10,
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ 不带装饰器的端点正常")
            return True
        else:
            print("❌ 不带装饰器的端点失败")
            return False

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 不带装饰器的端点异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_with_decorator():
    """测试带装饰器的端点"""
    print("\n测试带装饰器的端点（原始端点）")
    print("=" * 50)

    base_url = "http://localhost:8088"

    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info_data, timeout=10
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ 带装饰器的端点正常")
            return True
        else:
            print("❌ 带装饰器的端点失败")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 带装饰器的端点超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 带装饰器的端点异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def main():
    """主函数"""
    print("中间件问题测试")
    print("=" * 60)

    # 测试不带装饰器的端点
    direct_ok = test_without_decorator()

    # 测试带装饰器的端点
    decorated_ok = test_with_decorator()

    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if direct_ok and not decorated_ok:
        print("🔍 问题确认：@api_error_handler 装饰器或相关组件导致超时")
        print("\n可能的原因：")
        print("1. ErrorHandler 类的某个方法有阻塞操作")
        print("2. 日志记录中的某个同步调用")
        print("3. 异常处理逻辑中的死循环")
        print("4. Request 对象处理中的问题")

        print("\n建议的解决方案：")
        print("1. 临时移除 @api_error_handler 装饰器")
        print("2. 简化日志记录逻辑")
        print("3. 检查 ErrorHandler 类的实现")

    elif not direct_ok:
        print("❌ 两个端点都有问题，需要进一步调试")
    else:
        print("✅ 两个端点都正常，问题可能已解决")


if __name__ == "__main__":
    main()
