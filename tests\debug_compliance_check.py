#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合规性检查调试脚本
"""

import requests
import json
import time


def test_file_download():
    """测试文件下载"""
    print("1. 测试文件下载")
    print("-" * 40)

    file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3"

    try:
        print("尝试下载文件...")
        start_time = time.time()

        response = requests.head(file_url, timeout=30)  # 只获取头信息

        download_time = time.time() - start_time

        print(f"HEAD请求耗时: {download_time:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            content_length = response.headers.get("content-length")
            if content_length:
                size_mb = int(content_length) / 1024 / 1024
                print(f"文件大小: {size_mb:.2f}MB")

            print("✅ 文件可以访问")
            return True
        else:
            print(f"❌ 文件访问失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 文件下载测试失败: {str(e)}")
        return False


def test_request_validation():
    """测试请求验证"""
    print("\n2. 测试请求验证")
    print("-" * 40)

    base_url = "http://localhost:8088"

    # 简单的测试请求
    simple_request = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://example.com/test.docx",
        },
    }

    try:
        print("发送简单测试请求...")
        response = requests.post(
            f"{base_url}/api/v1/check-compliance", json=simple_request, timeout=30
        )

        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")

        if response.status_code == 200:
            print("✅ 请求验证通过")
            return True
        else:
            print(f"❌ 请求验证失败")
            return False

    except Exception as e:
        print(f"❌ 请求验证测试失败: {str(e)}")
        return False


def test_ai_model_connection():
    """测试AI模型连接"""
    print("\n3. 测试AI模型连接")
    print("-" * 40)

    base_url = "http://localhost:8088"

    try:
        response = requests.get(f"{base_url}/api/v1/service-status", timeout=10)

        if response.status_code == 200:
            data = response.json()
            compliance_service = data.get("compliance_service", {})
            health_status = compliance_service.get("health_status", {})

            print("服务健康状态:")
            for service, healthy in health_status.items():
                status = "✅" if healthy else "❌"
                print(f"  {service}: {status}")

            ai_healthy = health_status.get("ai_model", False)
            if ai_healthy:
                print("✅ AI模型连接正常")
                return True
            else:
                print("❌ AI模型连接异常")
                return False
        else:
            print(f"❌ 无法获取服务状态: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ AI模型连接测试失败: {str(e)}")
        return False


def test_sensitive_word_service():
    """测试敏感词服务"""
    print("\n4. 测试敏感词服务")
    print("-" * 40)

    base_url = "http://localhost:8088"

    try:
        response = requests.get(f"{base_url}/api/v1/sensitive-word-stats", timeout=10)

        if response.status_code == 200:
            data = response.json()
            health = data.get("health", False)
            stats = data.get("stats", {})

            print(f"健康状态: {'✅' if health else '❌'}")
            print(f"敏感词总数: {stats.get('total_words', 0)}")

            if health:
                print("✅ 敏感词服务正常")
                return True
            else:
                print("❌ 敏感词服务异常")
                return False
        else:
            print(f"❌ 无法获取敏感词服务状态: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 敏感词服务测试失败: {str(e)}")
        return False


def test_with_smaller_file():
    """使用更小的文件测试"""
    print("\n5. 使用小文件测试合规性检查")
    print("-" * 40)

    base_url = "http://localhost:8088"

    # 使用一个假的小文件URL进行测试
    small_file_request = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "small_test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,  # 1KB
            "url": "http://httpbin.org/status/404",  # 这会导致下载失败，但可以测试错误处理
        },
    }

    try:
        print("发送小文件测试请求...")
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/check-compliance", json=small_file_request, timeout=60
        )

        processing_time = time.time() - start_time

        print(f"处理时间: {processing_time:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")

        if response.status_code in [200, 400]:  # 400也是预期的，因为文件下载会失败
            print("✅ 请求处理正常（即使文件下载失败）")
            return True
        else:
            print(f"❌ 请求处理异常")
            return False

    except Exception as e:
        print(f"❌ 小文件测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("合规性检查调试")
    print("=" * 60)

    tests = [
        ("文件下载", test_file_download),
        ("请求验证", test_request_validation),
        ("AI模型连接", test_ai_model_connection),
        ("敏感词服务", test_sensitive_word_service),
        ("小文件测试", test_with_smaller_file),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行测试: {test_name}")
        print("=" * 60)

        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            results[test_name] = False

    # 总结
    print(f"\n{'='*60}")
    print("调试总结")
    print("=" * 60)

    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    # 分析问题
    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if not results.get("文件下载", False):
        print("🔍 问题可能在于文件下载：")
        print("   - 文件URL可能已过期")
        print("   - 网络连接问题")
        print("   - 文件服务器响应慢")

    if not results.get("AI模型连接", False):
        print("🔍 问题可能在于AI模型：")
        print("   - AI模型服务未启动")
        print("   - API密钥配置错误")
        print("   - 模型服务响应超时")

    if not results.get("敏感词服务", False):
        print("🔍 问题可能在于敏感词服务：")
        print("   - 敏感词API服务不可用")
        print("   - 网络连接问题")

    if results.get("小文件测试", False):
        print("✅ 系统处理逻辑正常，问题可能在于大文件处理")

    return 0


if __name__ == "__main__":
    exit(main())
