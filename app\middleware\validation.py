# -*- coding: utf-8 -*-
"""
请求验证中间件
"""

import json
import time
from typing import Callable
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logger import log
from app.core.exceptions import ValidationError
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """请求验证中间件"""

    def __init__(self, app, validate_enum_params: bool = True):
        """
        初始化请求验证中间件

        Args:
            app: FastAPI应用实例
            validate_enum_params: 是否验证枚举参数
        """
        super().__init__(app)
        self.validate_enum_params = validate_enum_params

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求验证"""
        request_id = getattr(request.state, "request_id", "")

        try:
            # 只对特定路径进行验证
            if self._should_validate(request.url.path):
                await self._validate_request(request, request_id)

            # 继续处理请求
            response = await call_next(request)
            return response

        except ValidationError as e:
            log.warning(f"请求验证失败 | ID: {request_id} | 错误: {str(e)}")
            return self._create_validation_error_response(e)
        except Exception as e:
            log.error(f"请求验证异常 | ID: {request_id} | 错误: {str(e)}")
            return self._create_generic_error_response(str(e))

    def _should_validate(self, path: str) -> bool:
        """判断是否需要验证请求"""
        # 需要验证的路径
        validation_paths = [
            "/api/v1/check-compliance",
            "/api/v1/validate-file",
        ]  # 重新启用 /api/v1/validate-file

        return any(path.startswith(vpath) for vpath in validation_paths)

    async def _validate_request(self, request: Request, request_id: str):
        """验证请求内容 - 修改为不消费请求体"""
        if request.method not in ["POST", "PUT", "PATCH"]:
            return

        # 跳过请求体验证，避免消费请求流
        # 验证将在端点内部进行
        log.info(f"跳过中间件验证 | ID: {request_id} | 路径: {request.url.path}")
        return

    def _validate_enum_parameters(self, request_data: dict, path: str):
        """验证枚举参数"""
        # 采购项目类型验证
        if "procurement_project_type" in request_data:
            value = request_data["procurement_project_type"]
            if value not in [e.value for e in ProcurementProjectType]:
                raise ValidationError(
                    f"无效的采购项目类型: {value}",
                    field="procurement_project_type",
                    value=value,
                    error_code="INVALID_PROCUREMENT_PROJECT_TYPE",
                )

        # 项目类别验证
        if "project_category" in request_data:
            value = request_data["project_category"]
            if value not in [e.value for e in ProjectCategory]:
                raise ValidationError(
                    f"无效的项目类别: {value}",
                    field="project_category",
                    value=value,
                    error_code="INVALID_PROJECT_CATEGORY",
                )

        # 招标采购方式验证
        if "bidding_procurement_method" in request_data:
            value = request_data["bidding_procurement_method"]
            if value not in [e.value for e in BiddingProcurementMethod]:
                raise ValidationError(
                    f"无效的招标采购方式: {value}",
                    field="bidding_procurement_method",
                    value=value,
                    error_code="INVALID_BIDDING_PROCUREMENT_METHOD",
                )

    def _validate_file_info(self, file_info: dict):
        """验证文件信息"""
        # 验证必需字段
        required_fields = ["filename", "extension", "mime_type", "size", "url"]
        for field in required_fields:
            if field not in file_info:
                raise ValidationError(
                    f"文件信息缺少必需字段: {field}",
                    field=field,
                    error_code="MISSING_FILE_FIELD",
                )

        # 验证文件扩展名
        extension = file_info.get("extension")
        if extension not in [e.value for e in FileExtension]:
            raise ValidationError(
                f"不支持的文件扩展名: {extension}",
                field="extension",
                value=extension,
                error_code="UNSUPPORTED_FILE_EXTENSION",
            )

        # 验证MIME类型
        mime_type = file_info.get("mime_type")
        if mime_type not in [e.value for e in MimeType]:
            raise ValidationError(
                f"不支持的MIME类型: {mime_type}",
                field="mime_type",
                value=mime_type,
                error_code="UNSUPPORTED_MIME_TYPE",
            )

        # 验证文件大小
        size = file_info.get("size", 0)
        if not isinstance(size, int) or size <= 0:
            raise ValidationError(
                "文件大小必须是大于0的整数",
                field="size",
                value=size,
                error_code="INVALID_FILE_SIZE",
            )

        # 验证URL格式
        url = file_info.get("url", "")
        if not url or not isinstance(url, str):
            raise ValidationError(
                "文件URL不能为空", field="url", value=url, error_code="INVALID_FILE_URL"
            )

    def _create_validation_error_response(self, error: ValidationError) -> JSONResponse:
        """创建验证错误响应"""
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": error.message,
                "error_code": error.error_code,
                "field": error.field,
                "value": str(error.value) if error.value is not None else None,
                "timestamp": error.timestamp.isoformat(),
            },
        )

    def _create_generic_error_response(self, message: str) -> JSONResponse:
        """创建通用错误响应"""
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": f"请求验证失败: {message}",
                "error_code": "VALIDATION_ERROR",
                "timestamp": json.loads(json.dumps({"timestamp": None}, default=str))[
                    "timestamp"
                ],
            },
        )


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """添加安全头"""
        response = await call_next(request)

        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"

        # 移除可能暴露服务器信息的头
        try:
            if "Server" in response.headers:
                del response.headers["Server"]
        except (AttributeError, KeyError):
            # 如果删除失败，记录警告但不影响正常流程
            log.warning("无法删除Server头部，可能是headers对象不支持此操作")

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的限流中间件"""

    def __init__(self, app, max_requests: int = 100, window_seconds: int = 3600):
        """
        初始化限流中间件

        Args:
            app: FastAPI应用实例
            max_requests: 时间窗口内最大请求数
            window_seconds: 时间窗口大小（秒）
        """
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.request_counts = {}  # 简单的内存存储，生产环境应使用Redis

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理限流"""
        client_ip = self._get_client_ip(request)
        current_time = int(time.time())
        window_start = current_time - (current_time % self.window_seconds)

        # 清理过期的记录
        self._cleanup_expired_records(window_start)

        # 检查当前IP的请求数
        key = f"{client_ip}:{window_start}"
        current_count = self.request_counts.get(key, 0)

        if current_count >= self.max_requests:
            log.warning(f"限流触发 | IP: {client_ip} | 请求数: {current_count}")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "success": False,
                    "message": "请求过于频繁，请稍后重试",
                    "error_code": "RATE_LIMIT_EXCEEDED",
                    "retry_after": self.window_seconds
                    - (current_time % self.window_seconds),
                },
                headers={
                    "Retry-After": str(
                        self.window_seconds - (current_time % self.window_seconds)
                    )
                },
            )

        # 增加请求计数
        self.request_counts[key] = current_count + 1

        # 处理请求
        response = await call_next(request)

        # 添加限流相关头
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(
            self.max_requests - self.request_counts[key]
        )
        response.headers["X-RateLimit-Reset"] = str(window_start + self.window_seconds)

        return response

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 优先从X-Forwarded-For头获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        # 从X-Real-IP头获取
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 最后使用客户端IP
        return request.client.host if request.client else "unknown"

    def _cleanup_expired_records(self, current_window_start: int):
        """清理过期的请求记录"""
        expired_keys = []
        for key in self.request_counts:
            window_start = int(key.split(":")[1])
            if window_start < current_window_start:
                expired_keys.append(key)

        for key in expired_keys:
            del self.request_counts[key]
