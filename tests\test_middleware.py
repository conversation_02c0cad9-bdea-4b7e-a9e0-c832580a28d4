# -*- coding: utf-8 -*-
"""
中间件测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from fastapi import FastAPI, Request, Response
from fastapi.testclient import TestClient

from app.middleware.logging import LoggingMiddleware, RequestSizeMiddleware


class TestLoggingMiddleware:
    """日志中间件测试"""

    def test_logging_middleware_success(self, caplog):
        """测试日志中间件 - 成功请求"""
        app = FastAPI()
        app.add_middleware(LoggingMiddleware)

        @app.get("/test")
        async def test_endpoint():
            return {"message": "success"}

        client = TestClient(app)
        response = client.get("/test")

        assert response.status_code == 200
        assert "X-Request-ID" in response.headers
        assert "X-Process-Time" in response.headers

        # 验证日志记录
        assert "请求开始" in caplog.text
        assert "请求完成" in caplog.text
        assert "GET" in caplog.text
        assert "/test" in caplog.text

    def test_logging_middleware_exception(self, caplog):
        """测试日志中间件 - 异常请求"""
        app = FastAPI()
        app.add_middleware(LoggingMiddleware)

        @app.get("/test")
        async def test_endpoint():
            raise ValueError("测试异常")

        client = TestClient(app)

        with pytest.raises(Exception):
            client.get("/test")

        # 验证日志记录
        assert "请求开始" in caplog.text
        assert "请求异常" in caplog.text
        assert "测试异常" in caplog.text


class TestRequestSizeMiddleware:
    """请求大小中间件测试"""

    def test_request_size_middleware_normal(self):
        """测试请求大小中间件 - 正常请求"""
        app = FastAPI()
        app.add_middleware(RequestSizeMiddleware, max_request_size=1024)

        @app.post("/test")
        async def test_endpoint(data: dict):
            return {"message": "success"}

        client = TestClient(app)
        response = client.post("/test", json={"test": "data"})

        assert response.status_code == 200

    def test_request_size_middleware_large_request(self, caplog):
        """测试请求大小中间件 - 大请求"""
        app = FastAPI()
        app.add_middleware(RequestSizeMiddleware, max_request_size=100)  # 100字节限制

        @app.post("/test")
        async def test_endpoint(data: dict):
            return {"message": "success"}

        client = TestClient(app)

        # 创建一个大的请求数据
        large_data = {"data": "x" * 1000}  # 超过100字节

        # 手动设置Content-Length头
        response = client.post(
            "/test",
            json=large_data,
            headers={"Content-Length": str(len(str(large_data)))},
        )

        # 中间件会记录警告，但不会阻止请求
        assert "请求大小超限" in caplog.text or response.status_code in [
            200,
            422,
        ]  # 422是验证错误


@pytest.fixture
def mock_request():
    """模拟请求对象"""
    request = MagicMock(spec=Request)
    request.method = "GET"
    request.url = "http://test.com/api/test"
    request.client.host = "127.0.0.1"
    request.headers = {"user-agent": "test-agent"}
    request.state = MagicMock()
    return request


@pytest.fixture
def mock_response():
    """模拟响应对象"""
    response = MagicMock(spec=Response)
    response.status_code = 200
    response.headers = {}
    return response


class TestMiddlewareIntegration:
    """中间件集成测试"""

    @pytest.mark.asyncio
    async def test_logging_middleware_dispatch(
        self, mock_request, mock_response, caplog
    ):
        """测试日志中间件的dispatch方法"""
        middleware = LoggingMiddleware(app=None)

        # 模拟call_next函数
        async def mock_call_next(request):
            return mock_response

        result = await middleware.dispatch(mock_request, mock_call_next)

        assert result == mock_response
        assert hasattr(mock_request.state, "request_id")
        assert "X-Request-ID" in mock_response.headers
        assert "X-Process-Time" in mock_response.headers

    @pytest.mark.asyncio
    async def test_request_size_middleware_dispatch(self, mock_request, mock_response):
        """测试请求大小中间件的dispatch方法"""
        middleware = RequestSizeMiddleware(app=None, max_request_size=1024)

        # 设置正常的Content-Length
        mock_request.headers = {"content-length": "500"}

        # 模拟call_next函数
        async def mock_call_next(request):
            return mock_response

        result = await middleware.dispatch(mock_request, mock_call_next)

        assert result == mock_response
