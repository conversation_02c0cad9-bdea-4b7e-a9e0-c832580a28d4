#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速网络修复脚本
用于临时解决文件下载连接问题
"""

import os
import sys
import requests
import urllib3
from urllib.parse import urlparse

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def create_fallback_session():
    """创建备用的requests会话，使用更宽松的配置"""
    session = requests.Session()
    
    # 设置更宽松的headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    })
    
    # 禁用SSL验证（仅用于测试）
    session.verify = False
    
    # 设置适配器
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    
    retry_strategy = Retry(
        total=5,
        backoff_factor=2,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        raise_on_status=False,
    )
    
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20,
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session

def download_with_fallback(url, max_size_mb=50):
    """使用多种策略下载文件"""
    print(f"尝试下载: {url}")
    
    # 策略1: 使用标准requests
    try:
        print("策略1: 标准requests...")
        session = create_fallback_session()
        
        response = session.get(url, timeout=(30, 300), stream=True)
        response.raise_for_status()
        
        content = b''
        downloaded_size = 0
        max_size_bytes = max_size_mb * 1024 * 1024
        
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                content += chunk
                downloaded_size += len(chunk)
                
                if downloaded_size > max_size_bytes:
                    raise Exception(f"文件大小超过限制: {max_size_mb}MB")
        
        print(f"✅ 下载成功: {len(content)} 字节")
        return content
        
    except Exception as e:
        print(f"❌ 策略1失败: {e}")
    
    # 策略2: 使用urllib
    try:
        print("策略2: urllib...")
        import urllib.request
        import urllib.error
        
        req = urllib.request.Request(
            url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        with urllib.request.urlopen(req, timeout=300) as response:
            content = response.read()
            print(f"✅ urllib下载成功: {len(content)} 字节")
            return content
            
    except Exception as e:
        print(f"❌ 策略2失败: {e}")
    
    # 策略3: 使用curl（如果可用）
    try:
        print("策略3: curl...")
        import subprocess
        
        cmd = [
            'curl', '-L', '-k', '--max-time', '300',
            '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            url
        ]
        
        result = subprocess.run(cmd, capture_output=True, timeout=300)
        if result.returncode == 0:
            content = result.stdout
            print(f"✅ curl下载成功: {len(content)} 字节")
            return content
        else:
            print(f"❌ curl失败: {result.stderr.decode()}")
            
    except Exception as e:
        print(f"❌ 策略3失败: {e}")
    
    print("❌ 所有下载策略都失败了")
    return None

def test_download():
    """测试下载功能"""
    test_url = "https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781"
    
    content = download_with_fallback(test_url)
    
    if content:
        # 保存到临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as f:
            f.write(content)
            print(f"文件已保存到: {f.name}")
            
        # 检测文件类型
        try:
            import filetype
            kind = filetype.guess(content)
            if kind:
                print(f"检测到文件类型: {kind.extension} ({kind.mime})")
            else:
                print("无法检测文件类型")
        except ImportError:
            print("filetype库未安装，无法检测文件类型")
            
        return True
    else:
        return False

def apply_network_fixes():
    """应用网络修复"""
    print("应用网络修复...")
    
    # 设置环境变量
    fixes = {
        'VERIFY_SSL': 'false',
        'CONNECT_TIMEOUT': '30',
        'READ_TIMEOUT': '300',
        'MAX_RETRIES': '5',
    }
    
    for key, value in fixes.items():
        os.environ[key] = value
        print(f"设置 {key}={value}")
    
    # 如果有代理配置，也设置一下
    if os.getenv('HTTP_PROXY') or os.getenv('HTTPS_PROXY'):
        print("检测到代理配置:")
        print(f"HTTP_PROXY: {os.getenv('HTTP_PROXY', '未设置')}")
        print(f"HTTPS_PROXY: {os.getenv('HTTPS_PROXY', '未设置')}")

def main():
    """主函数"""
    print("网络连接快速修复工具")
    print("=" * 50)
    
    # 应用修复
    apply_network_fixes()
    
    # 测试下载
    success = test_download()
    
    if success:
        print("\n✅ 网络连接修复成功！")
        print("可以重新启动应用程序进行测试")
    else:
        print("\n❌ 网络连接仍有问题")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. 防火墙设置")
        print("3. 代理配置")
        print("4. DNS设置")

if __name__ == "__main__":
    main()
