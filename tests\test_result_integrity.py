#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果聚合完整性验证功能
"""

import sys
import os
from unittest.mock import patch

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.result_processor import ResultProcessor
from app.models.schemas import SensitiveWordItem, CheckResultItem
from app.models.enums import QuestionType


def test_integrity_baseline_creation():
    """测试完整性基线创建"""
    print("测试完整性基线创建...")

    processor = ResultProcessor()

    # 创建测试数据
    sensitive_words = [
        SensitiveWordItem(type="政治敏感", content="测试词1", num=2),
        SensitiveWordItem(type="商业敏感", content="测试词2", num=1),
        SensitiveWordItem(type="政治敏感", content="测试词3", num=3),
    ]

    check_results = [
        CheckResultItem(
            quesType=QuestionType.COMPLIANCE.value,
            quesDesc="合规性问题1",
            originalArr=["位置1"],
            point="控制要点1",
            advice="建议1",
        ),
        CheckResultItem(
            quesType=QuestionType.LOGIC.value,
            quesDesc="逻辑性问题1",
            originalArr=["位置2"],
            point="控制要点2",
            advice="建议2",
        ),
    ]

    # 创建基线
    baseline = processor._create_integrity_baseline(
        sensitive_words, check_results, "test-baseline"
    )

    # 验证基线数据
    assert baseline["request_id"] == "test-baseline"
    assert baseline["sensitive_words"]["count"] == 3
    assert baseline["sensitive_words"]["total_occurrences"] == 6  # 2+1+3
    assert len(baseline["sensitive_words"]["unique_contents"]) == 3
    assert len(baseline["sensitive_words"]["types"]) == 2  # 政治敏感, 商业敏感

    assert baseline["check_results"]["count"] == 2
    assert len(baseline["check_results"]["types"]) == 2
    assert len(baseline["check_results"]["unique_descriptions"]) == 2

    print("✅ 完整性基线创建测试通过")


def test_processing_integrity_verification():
    """测试处理完整性验证"""
    print("\n测试处理完整性验证...")

    processor = ResultProcessor()

    # 测试正常情况（无数据丢失）
    input_data = [1, 2, 3, 4, 5]
    output_data = [1, 2, 3, 4, 5]

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_processing_integrity(
            "正常处理", input_data, output_data, "test-normal"
        )
        # 正常情况应该只有debug日志
        assert mock_log.debug.called
        assert not mock_log.warning.called
        assert not mock_log.error.called

    # 测试轻微数据丢失（5%）
    input_data = list(range(20))
    output_data = list(range(19))  # 丢失1个，5%

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_processing_integrity(
            "轻微丢失", input_data, output_data, "test-minor"
        )
        # 轻微丢失应该有警告日志
        assert mock_log.warning.called
        assert not mock_log.error.called

    # 测试中等数据丢失（20%）
    input_data = list(range(10))
    output_data = list(range(8))  # 丢失2个，20%

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_processing_integrity(
            "中等丢失", input_data, output_data, "test-medium"
        )
        # 中等丢失（20%）应该有错误日志
        assert mock_log.error.called
        assert not mock_log.warning.called

    # 测试严重数据丢失（60%）
    input_data = list(range(10))
    output_data = list(range(4))  # 丢失6个，60%

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_processing_integrity(
            "严重丢失", input_data, output_data, "test-severe"
        )
        # 严重丢失应该有错误日志
        assert mock_log.error.called

    print("✅ 处理完整性验证测试通过")


def test_deduplication_integrity_verification():
    """测试去重完整性验证"""
    print("\n测试去重完整性验证...")

    processor = ResultProcessor()

    # 创建有重复的输入数据
    input_words = [
        SensitiveWordItem(type="政治敏感", content="重复词", num=2),
        SensitiveWordItem(type="政治敏感", content="重复词", num=3),  # 重复
        SensitiveWordItem(type="商业敏感", content="唯一词", num=1),
    ]

    # 去重后的输出数据
    output_words = [
        SensitiveWordItem(type="政治敏感", content="重复词", num=5),  # 合并了2+3
        SensitiveWordItem(type="商业敏感", content="唯一词", num=1),
    ]

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_deduplication_integrity(
            input_words, output_words, "test-dedup"
        )

        # 应该有信息日志记录出现次数的变化
        assert mock_log.info.called
        info_call = mock_log.info.call_args[0][0]
        assert "出现次数变化" in info_call
        assert "6 -> 6" in info_call  # 总次数应该保持不变

    # 测试内容丢失的情况
    input_words_with_loss = [
        SensitiveWordItem(type="政治敏感", content="保留词", num=2),
        SensitiveWordItem(type="商业敏感", content="丢失词", num=1),
    ]

    output_words_with_loss = [
        SensitiveWordItem(type="政治敏感", content="保留词", num=2),
        # "丢失词" 被丢失了
    ]

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_deduplication_integrity(
            input_words_with_loss, output_words_with_loss, "test-loss"
        )

        # 应该有警告日志记录内容丢失
        assert mock_log.warning.called
        warning_call = mock_log.warning.call_args[0][0]
        assert "丢失敏感词内容" in warning_call

    print("✅ 去重完整性验证测试通过")


def test_final_integrity_verification():
    """测试最终完整性验证"""
    print("\n测试最终完整性验证...")

    processor = ResultProcessor()

    # 创建基线数据
    baseline = {
        "sensitive_words": {"count": 3, "unique_contents": {"词1", "词2", "词3"}},
        "check_results": {
            "count": 5,
            "unique_descriptions": {"问题1", "问题2", "问题3", "问题4", "问题5"},
        },
    }

    # 创建最终响应（部分数据丢失）
    from app.models.schemas import ComplianceCheckResponse

    response = ComplianceCheckResponse(
        sensitiveWordsArr=[
            SensitiveWordItem(type="政治敏感", content="词1", num=2),
            SensitiveWordItem(type="商业敏感", content="词2", num=1),
            # "词3" 丢失了
        ],
        checkResultArr=[
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE.value,
                quesDesc="问题1",
                originalArr=["位置1"],
                point="要点1",
                advice="建议1",
            ),
            CheckResultItem(
                quesType=QuestionType.LOGIC.value,
                quesDesc="问题2",
                originalArr=["位置2"],
                point="要点2",
                advice="建议2",
            ),
            # 其他问题因为数量限制被截断
        ],
    )

    with patch("app.services.result_processor.log") as mock_log:
        processor._verify_final_integrity(baseline, response, "test-final")

        # 应该有错误日志记录敏感词丢失
        assert mock_log.error.called
        error_call = mock_log.error.call_args[0][0]
        assert "敏感词内容丢失" in error_call

        # 应该有信息日志记录检查结果数量限制
        assert mock_log.info.called
        info_calls = [call[0][0] for call in mock_log.info.call_args_list]
        assert any("检查结果数量限制" in call for call in info_calls)
        assert any("完整性报告" in call for call in info_calls)

    print("✅ 最终完整性验证测试通过")


def test_complete_aggregation_with_integrity():
    """测试完整的聚合过程与完整性验证"""
    print("\n测试完整的聚合过程与完整性验证...")

    processor = ResultProcessor()

    # 创建测试数据
    sensitive_words = [
        SensitiveWordItem(type="政治敏感", content="测试词1", num=2),
        SensitiveWordItem(
            type="政治敏感", content="测试词1", num=1
        ),  # 重复，应该被合并
        SensitiveWordItem(type="商业敏感", content="测试词2", num=3),
    ]

    check_results = [
        CheckResultItem(
            quesType=QuestionType.COMPLIANCE.value,
            quesDesc="合规性问题",
            originalArr=["位置1"],
            point="控制要点",
            advice="处理建议",
        )
    ]

    # 执行聚合
    result = processor.aggregate_results(
        sensitive_words, check_results, "test-complete"
    )

    # 验证结果
    assert len(result.sensitiveWordsArr) == 2  # 去重后应该有2个
    assert len(result.checkResultArr) == 1

    # 验证去重是否正确
    merged_word = next(
        (word for word in result.sensitiveWordsArr if word.content == "测试词1"), None
    )
    assert merged_word is not None
    assert merged_word.num == 3  # 2 + 1 = 3

    print("✅ 完整的聚合过程与完整性验证测试通过")


if __name__ == "__main__":
    print("结果聚合完整性验证功能测试")
    print("=" * 50)

    try:
        test_integrity_baseline_creation()
        test_processing_integrity_verification()
        test_deduplication_integrity_verification()
        test_final_integrity_verification()
        test_complete_aggregation_with_integrity()

        print("\n🎉 所有测试通过！结果聚合完整性验证功能实现成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
