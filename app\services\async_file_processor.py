# -*- coding: utf-8 -*-
"""
异步文件处理服务
提供异步的文件下载和处理功能，提升并发性能
"""

import asyncio
import os
import tempfile
from io import BytesIO
from typing import Optional, Tuple, Dict, Any
import filetype
import aiohttp
from aiohttp import ClientTimeout, ClientSession

# 可选的MarkItDown库
try:
    from markitdown import MarkItDown

    MARKITDOWN_AVAILABLE = True
except ImportError:
    MarkItDown = None
    MARKITDOWN_AVAILABLE = False

# 备用文档处理库
import docx
import pdfplumber

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.models.enums import FileExtension, MimeType
from app.models.schemas import FileInfo
from app.core.exceptions import FileProcessingError
from app.services.bidding_info_processor import bidding_info_processor, BiddingInfoError


class AsyncFileProcessor:
    """异步文件处理器"""

    def __init__(self):
        """初始化异步文件处理器"""
        self.processing_stats = {
            "total_files_processed": 0,
            "successful_processes": 0,
            "failed_processes": 0,
            "total_download_size": 0,
            "average_processing_time": 0.0,
        }

        # 支持的文件类型
        self.supported_extensions = {
            FileExtension.DOCX: self._process_docx_async,
            FileExtension.PDF: self._process_pdf_async,
        }

        # 初始化MarkItDown（如果可用）
        self.markitdown = None
        if MARKITDOWN_AVAILABLE:
            try:
                self.markitdown = MarkItDown()
                log.info("MarkItDown库初始化成功，将作为主要处理引擎")
            except Exception as e:
                log.warning(f"MarkItDown库初始化失败，将使用备用处理方式: {str(e)}")

    @log_function_call
    async def process_file(self, file_info: FileInfo, request_id: str = "") -> str:
        """
        异步处理文件

        Args:
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            str: 处理后的markdown内容
        """
        start_time = asyncio.get_event_loop().time()
        self.processing_stats["total_files_processed"] += 1

        try:
            with TimingContext("异步文件处理", request_id):
                log.info(
                    f"开始异步文件处理 | ID: {request_id} | "
                    f"文件: {file_info.filename} | 大小: {file_info.size} 字节"
                )

                # 验证文件处理能力
                capability = self.validate_processing_capability(file_info)
                if not capability["can_process"]:
                    raise FileProcessingError(
                        f"文件无法处理: {', '.join(capability['warnings'])}",
                        filename=file_info.filename,
                        file_type=file_info.extension.value,
                    )

                # 异步下载文件
                file_data = await self._download_file_async(file_info, request_id)

                # 分析招标信息（在转markdown之前）
                bidding_info = {}
                try:
                    log.info("开始异步分析招标文件信息")
                    # 在线程池中提取前10页内容
                    loop = asyncio.get_event_loop()
                    first_10_pages = await loop.run_in_executor(
                        None,
                        bidding_info_processor.extract_first_10_pages,
                        file_data,
                        file_info.extension,
                        request_id,
                    )
                    # 在线程池中分析招标信息
                    bidding_info = await loop.run_in_executor(
                        None,
                        bidding_info_processor.analyze_bidding_info,
                        first_10_pages,
                        request_id,
                    )
                    log.info(f"异步招标信息分析完成: {bidding_info}")
                except BiddingInfoError as e:
                    log.warning(f"异步招标信息分析失败: {str(e)}")
                    bidding_info = {}
                except Exception as e:
                    log.warning(f"异步招标信息分析异常: {str(e)}")
                    bidding_info = {}

                # 处理文件内容
                if self.markitdown and MARKITDOWN_AVAILABLE:
                    # 优先使用MarkItDown
                    content = await self._process_with_markitdown_async(
                        file_data, file_info, request_id
                    )
                else:
                    # 使用备用处理方式
                    content = await self._process_with_fallback_async(
                        file_data, file_info, request_id
                    )

                # 清理和验证内容
                cleaned_content = self._clean_content(content)

                # 在markdown内容上进行招标信息替换
                if bidding_info and any(info.strip() for info in bidding_info.values()):
                    try:
                        loop = asyncio.get_event_loop()
                        cleaned_content = await loop.run_in_executor(
                            None,
                            bidding_info_processor.replace_bidding_info,
                            cleaned_content,
                            bidding_info,
                            request_id,
                        )
                        log.info("异步Markdown内容中的招标信息替换完成")
                    except Exception as e:
                        log.warning(f"异步Markdown招标信息替换失败: {str(e)}")

                # 更新统计信息
                duration = asyncio.get_event_loop().time() - start_time
                self._update_success_stats(duration, file_info.size)

                log.info(
                    f"异步文件处理完成 | ID: {request_id} | "
                    f"内容长度: {len(cleaned_content)} 字符 | 耗时: {duration:.3f}秒"
                )

                return cleaned_content

        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self._update_failure_stats(duration)

            if isinstance(e, FileProcessingError):
                raise
            else:
                raise FileProcessingError(
                    f"异步文件处理失败: {str(e)}",
                    filename=file_info.filename,
                    original_error=e,
                )

    async def _download_file_async(
        self, file_info: FileInfo, request_id: str = ""
    ) -> bytes:
        """
        异步下载文件

        Args:
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            bytes: 文件数据
        """
        try:
            # 配置超时
            timeout = ClientTimeout(total=settings.file_download_timeout)

            log.debug(f"开始异步下载文件 | ID: {request_id} | URL: {file_info.url}")

            async with ClientSession(timeout=timeout) as session:
                async with session.get(file_info.url) as response:
                    if response.status == 200:
                        file_data = await response.read()

                        # 验证文件大小
                        if len(file_data) != file_info.size:
                            log.warning(
                                f"文件大小不匹配 | ID: {request_id} | "
                                f"预期: {file_info.size} | 实际: {len(file_data)}"
                            )

                        # 验证文件类型
                        detected_type = filetype.guess(file_data)
                        if detected_type:
                            log.debug(
                                f"检测到文件类型 | ID: {request_id} | "
                                f"类型: {detected_type.extension} | MIME: {detected_type.mime}"
                            )

                        log.debug(
                            f"文件下载完成 | ID: {request_id} | 大小: {len(file_data)} 字节"
                        )

                        return file_data
                    else:
                        raise FileProcessingError(
                            f"文件下载失败，HTTP状态码: {response.status}",
                            filename=file_info.filename,
                            url=file_info.url,
                        )

        except asyncio.TimeoutError:
            raise FileProcessingError(
                f"文件下载超时（{settings.file_download_timeout}秒）",
                filename=file_info.filename,
                url=file_info.url,
            )
        except Exception as e:
            raise FileProcessingError(
                f"文件下载失败: {str(e)}",
                filename=file_info.filename,
                url=file_info.url,
                original_error=e,
            )

    async def _process_with_markitdown_async(
        self, file_data: bytes, file_info: FileInfo, request_id: str = ""
    ) -> str:
        """
        使用MarkItDown异步处理文件

        Args:
            file_data: 文件数据
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            str: 处理后的markdown内容
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(
                suffix=f".{file_info.extension.value}", delete=False
            ) as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name

            try:
                # 在线程池中运行MarkItDown处理
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None, self.markitdown.convert, temp_file_path
                )

                content = (
                    result.text_content
                    if hasattr(result, "text_content")
                    else str(result)
                )

                log.debug(
                    f"MarkItDown处理完成 | ID: {request_id} | "
                    f"内容长度: {len(content)} 字符"
                )

                return content

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    log.warning(f"清理临时文件失败 | ID: {request_id} | 错误: {str(e)}")

        except Exception as e:
            log.warning(
                f"MarkItDown处理失败，使用备用方式 | ID: {request_id} | 错误: {str(e)}"
            )
            return await self._process_with_fallback_async(
                file_data, file_info, request_id
            )

    async def _process_with_fallback_async(
        self, file_data: bytes, file_info: FileInfo, request_id: str = ""
    ) -> str:
        """
        使用备用方式异步处理文件

        Args:
            file_data: 文件数据
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            str: 处理后的markdown内容
        """
        processor = self.supported_extensions.get(file_info.extension)
        if not processor:
            raise FileProcessingError(
                f"不支持的文件类型: {file_info.extension.value}",
                filename=file_info.filename,
                file_type=file_info.extension.value,
            )

        # 在线程池中运行处理函数
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, processor, file_data, file_info, request_id
        )

    def _process_docx_async(
        self, file_data: bytes, file_info: FileInfo, request_id: str = ""
    ) -> str:
        """处理DOCX文件（在线程池中运行）"""
        try:
            doc = docx.Document(BytesIO(file_data))

            # 提取文本内容
            content_parts = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 根据样式判断标题级别
                    style_name = paragraph.style.name.lower()
                    if "heading" in style_name:
                        level = 1
                        if "heading 2" in style_name:
                            level = 2
                        elif "heading 3" in style_name:
                            level = 3
                        content_parts.append(f"{'#' * level} {text}")
                    else:
                        content_parts.append(text)

            # 处理表格
            for table in doc.tables:
                table_content = []
                for row in table.rows:
                    row_content = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_content.append(cell_text)
                    if any(row_content):  # 只添加非空行
                        table_content.append("| " + " | ".join(row_content) + " |")

                if table_content:
                    content_parts.append("\n".join(table_content))

            content = "\n\n".join(content_parts)

            log.debug(
                f"DOCX处理完成 | ID: {request_id} | "
                f"段落数: {len(doc.paragraphs)} | 表格数: {len(doc.tables)}"
            )

            return content

        except Exception as e:
            raise FileProcessingError(
                f"DOCX文件处理失败: {str(e)}",
                filename=file_info.filename,
                file_type="docx",
                original_error=e,
            )

    def _process_pdf_async(
        self, file_data: bytes, file_info: FileInfo, request_id: str = ""
    ) -> str:
        """处理PDF文件（在线程池中运行）"""
        try:
            content_parts = []

            with pdfplumber.open(BytesIO(file_data)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    # 提取文本
                    text = page.extract_text()
                    if text:
                        content_parts.append(f"## 第{page_num}页\n\n{text}")

                    # 提取表格
                    tables = page.extract_tables()
                    for table_num, table in enumerate(tables, 1):
                        if table:
                            table_content = []
                            for row in table:
                                if row and any(cell for cell in row if cell):
                                    clean_row = [
                                        str(cell).strip() if cell else ""
                                        for cell in row
                                    ]
                                    table_content.append(
                                        "| " + " | ".join(clean_row) + " |"
                                    )

                            if table_content:
                                content_parts.append(
                                    f"### 第{page_num}页表格{table_num}\n\n"
                                    + "\n".join(table_content)
                                )

            content = "\n\n".join(content_parts)

            log.debug(
                f"PDF处理完成 | ID: {request_id} | "
                f"页数: {len(pdf.pages)} | 内容长度: {len(content)}"
            )

            return content

        except Exception as e:
            raise FileProcessingError(
                f"PDF文件处理失败: {str(e)}",
                filename=file_info.filename,
                file_type="pdf",
                original_error=e,
            )

    def _clean_content(self, content: str) -> str:
        """清理内容"""
        if not content:
            return ""

        # 移除多余的空行
        lines = content.split("\n")
        cleaned_lines = []
        prev_empty = False

        for line in lines:
            line = line.strip()
            if line:
                cleaned_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                cleaned_lines.append("")
                prev_empty = True

        return "\n".join(cleaned_lines)

    def validate_processing_capability(self, file_info: FileInfo) -> Dict[str, Any]:
        """验证文件处理能力"""
        warnings = []
        can_process = True
        estimated_time = 1.0

        # 检查文件类型支持
        if file_info.extension not in self.supported_extensions:
            can_process = False
            warnings.append(f"不支持的文件类型: {file_info.extension.value}")

        # 检查文件大小
        if file_info.size > settings.max_file_size:
            can_process = False
            warnings.append(f"文件过大: {file_info.size} > {settings.max_file_size}")
        elif file_info.size > settings.max_file_size * 0.8:
            warnings.append("文件较大，处理时间可能较长")
            estimated_time = 5.0

        # 估算处理时间
        if file_info.extension == FileExtension.PDF:
            estimated_time *= 1.5  # PDF处理通常更慢

        return {
            "can_process": can_process,
            "warnings": warnings,
            "estimated_time": estimated_time,
            "processor_available": {
                "markitdown": MARKITDOWN_AVAILABLE and self.markitdown is not None,
                "fallback": True,
            },
        }

    def _update_success_stats(self, duration: float, file_size: int):
        """更新成功统计"""
        self.processing_stats["successful_processes"] += 1
        self.processing_stats["total_download_size"] += file_size

        # 更新平均处理时间
        total_files = self.processing_stats["total_files_processed"]
        current_avg = self.processing_stats["average_processing_time"]
        self.processing_stats["average_processing_time"] = (
            current_avg * (total_files - 1) + duration
        ) / total_files

    def _update_failure_stats(self, duration: float):
        """更新失败统计"""
        self.processing_stats["failed_processes"] += 1

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        success_rate = 0.0
        if self.processing_stats["total_files_processed"] > 0:
            success_rate = (
                self.processing_stats["successful_processes"]
                / self.processing_stats["total_files_processed"]
            )

        return {
            "service_type": "async_file_processor",
            "markitdown_available": MARKITDOWN_AVAILABLE
            and self.markitdown is not None,
            "supported_extensions": [
                ext.value for ext in self.supported_extensions.keys()
            ],
            "max_file_size": settings.max_file_size,
            "stats": {
                **self.processing_stats,
                "success_rate": success_rate,
                "average_file_size": (
                    self.processing_stats["total_download_size"]
                    / max(self.processing_stats["successful_processes"], 1)
                ),
            },
        }


# 创建全局异步文件处理器实例
async_file_processor = AsyncFileProcessor()
