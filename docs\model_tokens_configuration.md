# AI模型Tokens配置说明

## 核心概念

### 1. max_tokens (最大输出tokens)
- **定义**: 限制模型单次生成的最大token数量
- **作用范围**: 仅影响模型的回复/输出长度
- **用途**: 
  - 控制API成本
  - 防止模型生成过长回复
  - 确保回复符合预期长度

### 2. Context Length (上下文长度)
- **定义**: 模型能处理的总token数量 (输入 + 输出)
- **作用范围**: 包括系统提示词 + 用户输入 + 历史对话 + 模型输出
- **限制**: 这是模型的硬性限制，超过会导致请求失败

## 实际配置策略

### Qwen2.5-7B-Instruct 模型规格
- **上下文长度**: 32,768 tokens (约2.5万中文字符)
- **建议配置**:

```python
# 不同任务的max_tokens配置建议

# 1. 招标信息提取 (简短结构化输出)
max_tokens = 1000  # 足够输出JSON格式的招标信息

# 2. 文档审查 (详细分析报告)  
max_tokens = 8000  # 允许输出详细的审查结果

# 3. 文档摘要 (中等长度输出)
max_tokens = 2000  # 适合摘要长度

# 4. 对话交互 (灵活输出)
max_tokens = 4000  # 平衡回复质量和成本
```

## 配置原则

### 1. 根据任务类型设置max_tokens

| 任务类型 | 推荐max_tokens | 说明 |
|---------|---------------|------|
| 信息提取 | 500-1000 | 结构化数据，输出简短 |
| 文档分析 | 4000-8000 | 需要详细分析结果 |
| 摘要生成 | 1000-2000 | 中等长度摘要 |
| 翻译任务 | 输入长度×1.2 | 根据输入长度动态调整 |
| 对话聊天 | 2000-4000 | 平衡质量和成本 |

### 2. 上下文长度管理

```python
# 计算当前上下文使用量
def estimate_tokens(text: str) -> int:
    """估算文本的token数量 (中文约1.5字符=1token)"""
    return len(text) // 1.5

# 检查上下文是否超限
def check_context_limit(messages: List[Dict], max_context: int = 30000):
    total_tokens = sum(estimate_tokens(msg['content']) for msg in messages)
    if total_tokens > max_context:
        # 截断或压缩输入
        return truncate_messages(messages, max_context)
    return messages
```

### 3. 成本优化策略

```python
# 根据任务重要性动态调整
def get_max_tokens_by_priority(task_type: str, priority: str) -> int:
    base_tokens = {
        'extraction': 1000,
        'analysis': 6000, 
        'summary': 2000,
        'chat': 3000
    }
    
    multipliers = {
        'high': 1.5,
        'medium': 1.0,
        'low': 0.7
    }
    
    return int(base_tokens.get(task_type, 2000) * multipliers.get(priority, 1.0))
```

## 当前项目配置

### 配置文件 (app/core/config.py)
```python
# 全局上下文限制 (用于检查输入是否超限)
max_context_length: int = 30000  # 为Qwen2.5-7B预留安全边界

# 全局输出限制 (默认值，可被具体任务覆盖)  
max_output_tokens: int = 4000
```

### 具体任务配置

#### 1. 招标信息提取
```python
# app/services/bidding_info_processor.py
max_tokens = 1000  # 只需要输出JSON格式的招标信息
temperature = 0.1  # 低随机性，确保格式稳定
```

#### 2. 文档合规审查  
```python
# app/services/ai_model_service.py
max_tokens = 8000  # 需要详细的审查结果
temperature = 0.0  # 严格按规则审查
```

## 监控和调优

### 1. Token使用监控
```python
def log_token_usage(request_id: str, input_tokens: int, output_tokens: int, total_cost: float):
    log.info(f"Token使用 | 请求ID: {request_id} | 输入: {input_tokens} | 输出: {output_tokens} | 成本: ${total_cost:.4f}")
```

### 2. 动态调整策略
- 监控平均输出长度
- 根据任务成功率调整max_tokens
- 定期评估成本效益

## 常见问题

### Q: max_tokens设置过小会怎样？
A: 模型输出可能被截断，导致JSON格式不完整或信息不全

### Q: max_tokens设置过大会怎样？  
A: 增加API成本，但不会影响输出质量（模型会在合适位置停止）

### Q: 如何处理上下文长度超限？
A: 
1. 截断输入内容（保留重要部分）
2. 分批处理长文档
3. 使用文档摘要作为输入

### Q: 不同模型的配置差异？
A:
- GPT-4: 上下文128K, 建议max_tokens 4000-16000
- Claude-3: 上下文200K, 建议max_tokens 4000-8000  
- Qwen2.5-7B: 上下文32K, 建议max_tokens 1000-8000