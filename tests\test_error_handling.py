#!/usr/bin/env python3
"""
测试错误处理修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_none_error_code():
    """测试None错误代码的处理"""
    print("🔍 测试None错误代码的处理...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 模拟一个有None error_code的错误
        class MockError:
            def __init__(self):
                self.error_code = None
                
        mock_error = MockError()
        
        # 测试_should_retry方法
        should_retry = ai_service._should_retry(mock_error, 0, 3)
        
        print(f"✅ None错误代码处理成功，should_retry: {should_retry}")
        return True
        
    except Exception as e:
        print(f"❌ None错误代码处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_error_code():
    """测试空错误代码的处理"""
    print("\n🔍 测试空错误代码的处理...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 模拟一个有空error_code的错误
        class MockError:
            def __init__(self):
                self.error_code = ""
                
        mock_error = MockError()
        
        # 测试_should_retry方法
        should_retry = ai_service._should_retry(mock_error, 0, 3)
        
        print(f"✅ 空错误代码处理成功，should_retry: {should_retry}")
        return True
        
    except Exception as e:
        print(f"❌ 空错误代码处理失败: {e}")
        return False

def test_503_error_code():
    """测试503错误代码的处理"""
    print("\n🔍 测试503错误代码的处理...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 模拟一个503错误
        class MockError:
            def __init__(self):
                self.error_code = "503"
                
        mock_error = MockError()
        
        # 测试_should_retry方法
        should_retry = ai_service._should_retry(mock_error, 0, 3)
        
        print(f"✅ 503错误代码处理成功，should_retry: {should_retry}")
        print(f"   503错误应该被重试: {should_retry}")
        return True
        
    except Exception as e:
        print(f"❌ 503错误代码处理失败: {e}")
        return False

def test_no_error_code_attribute():
    """测试没有error_code属性的错误"""
    print("\n🔍 测试没有error_code属性的错误...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 模拟一个没有error_code属性的错误
        class MockError:
            def __init__(self):
                pass  # 没有error_code属性
                
        mock_error = MockError()
        
        # 测试_should_retry方法
        should_retry = ai_service._should_retry(mock_error, 0, 3)
        
        print(f"✅ 无error_code属性处理成功，should_retry: {should_retry}")
        return True
        
    except Exception as e:
        print(f"❌ 无error_code属性处理失败: {e}")
        return False

def test_standard_exception():
    """测试标准异常的处理"""
    print("\n🔍 测试标准异常的处理...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 使用标准异常
        mock_error = Exception("Test error message")
        
        # 测试_should_retry方法
        should_retry = ai_service._should_retry(mock_error, 0, 3)
        
        print(f"✅ 标准异常处理成功，should_retry: {should_retry}")
        return True
        
    except Exception as e:
        print(f"❌ 标准异常处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试错误处理修复...")
    
    success = True
    success &= test_none_error_code()
    success &= test_empty_error_code()
    success &= test_503_error_code()
    success &= test_no_error_code_attribute()
    success &= test_standard_exception()
    
    if success:
        print("\n🎉 所有错误处理测试通过!")
        print("现在AI模型服务应该能正确处理各种错误情况了。")
    else:
        print("\n❌ 部分错误处理测试失败")
        sys.exit(1)
