#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理调试脚本
"""

import requests
import json
import time
import threading


def test_with_timeout_monitoring():
    """带超时监控的测试"""
    print("文件处理超时监控测试")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 使用一个会快速失败的URL
    request_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 100,
        "url": "http://httpbin.org/status/404",  # 会立即返回404
    }

    def monitor_progress():
        """监控进度"""
        for i in range(30):  # 30秒监控
            time.sleep(1)
            print(f"等待中... {i+1}秒")

    print("发送文件验证请求...")
    print(f"请求数据: {json.dumps(request_data, indent=2)}")

    # 启动监控线程
    monitor_thread = threading.Thread(target=monitor_progress)
    monitor_thread.daemon = True
    monitor_thread.start()

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=request_data, timeout=30
        )

        elapsed = time.time() - start_time
        print(f"\n请求完成，耗时: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text[:500]}...")

        return True

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"\n❌ 请求超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"\n❌ 请求异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_direct_file_processor():
    """直接测试文件处理器组件"""
    print("\n直接测试文件处理器")
    print("=" * 50)

    try:
        # 尝试直接导入和测试文件处理器
        import sys

        sys.path.append(".")

        from app.services.file_processor_v2 import optimized_file_processor
        from app.models.schemas import FileInfo
        from app.models.enums import FileExtension, MimeType

        print("创建测试文件信息...")
        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=100,
            url="http://httpbin.org/status/404",
        )

        print("测试文件格式验证...")
        start_time = time.time()

        try:
            optimized_file_processor.validate_file_format(file_info)
            elapsed = time.time() - start_time
            print(f"✅ 文件格式验证完成，耗时: {elapsed:.3f}秒")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 文件格式验证失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")

        print("测试处理能力验证...")
        start_time = time.time()

        try:
            capability = optimized_file_processor.validate_processing_capability(
                file_info
            )
            elapsed = time.time() - start_time
            print(f"✅ 处理能力验证完成，耗时: {elapsed:.3f}秒")
            print(f"结果: {capability}")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 处理能力验证失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")

        return True

    except ImportError as e:
        print(f"❌ 无法导入文件处理器: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 直接测试失败: {str(e)}")
        return False


def test_estimate_processing_time():
    """测试处理时间估算函数"""
    print("\n测试处理时间估算")
    print("=" * 50)

    try:
        import sys

        sys.path.append(".")

        # 尝试导入估算函数
        try:
            from app.utils.file_utils import estimate_processing_time

            print("测试处理时间估算函数...")
            start_time = time.time()

            estimated_time = estimate_processing_time(1024, ".docx")

            elapsed = time.time() - start_time
            print(
                f"✅ 处理时间估算完成，耗时: {elapsed:.3f}秒，估算结果: {estimated_time}秒"
            )

        except ImportError:
            print("⚠️  estimate_processing_time 函数不存在，这可能是问题所在")

            # 测试是否是这个函数导致的问题
            print("尝试创建简单的估算函数...")

            def simple_estimate(file_size, file_type):
                size_mb = file_size / (1024 * 1024)
                if file_type == ".pdf":
                    return 1.0 + size_mb * 2.0
                elif file_type == ".docx":
                    return 1.0 + size_mb * 1.0
                else:
                    return 1.0 + size_mb * 1.5

            estimated_time = simple_estimate(1024, ".docx")
            print(f"✅ 简单估算完成，结果: {estimated_time}秒")

        return True

    except Exception as e:
        print(f"❌ 处理时间估算测试失败: {str(e)}")
        return False


def create_fixed_file_utils():
    """创建修复的文件工具函数"""
    print("\n创建修复的文件工具函数")
    print("=" * 50)

    # 检查文件是否存在问题
    try:
        with open("app/utils/file_utils.py", "r", encoding="utf-8") as f:
            content = f.read()

        if "def estimate_processing_time" in content:
            print("✅ estimate_processing_time 函数存在")
        else:
            print("❌ estimate_processing_time 函数不存在")

        # 检查是否有语法错误
        try:
            compile(content, "app/utils/file_utils.py", "exec")
            print("✅ 文件语法正确")
        except SyntaxError as e:
            print(f"❌ 文件语法错误: {str(e)}")

    except FileNotFoundError:
        print("❌ app/utils/file_utils.py 文件不存在")
    except Exception as e:
        print(f"❌ 检查文件失败: {str(e)}")


def main():
    """主函数"""
    print("文件处理详细调试")
    print("=" * 60)

    tests = [
        ("超时监控测试", test_with_timeout_monitoring),
        ("直接文件处理器测试", test_direct_file_processor),
        ("处理时间估算测试", test_estimate_processing_time),
        ("文件工具检查", create_fixed_file_utils),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行: {test_name}")
        print("=" * 60)

        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
            results[test_name] = False

    # 分析结果
    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if not results.get("直接文件处理器测试", False):
        print("🔍 问题可能在文件处理器组件本身")

    if not results.get("处理时间估算测试", False):
        print("🔍 问题可能在 estimate_processing_time 函数")
        print("   这个函数可能导致无限循环或长时间阻塞")

    print(f"\n{'='*60}")
    print("建议的修复方案")
    print("=" * 60)
    print("1. 修复 estimate_processing_time 函数的导入问题")
    print("2. 在文件处理器中添加更多超时保护")
    print("3. 为每个处理步骤添加独立的超时机制")


if __name__ == "__main__":
    main()
