#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证器修复测试
"""


def test_validator_fix():
    """测试验证器修复"""
    print("测试验证器修复...")

    try:
        from app.core.validators import ParameterValidator

        # 使用正确的枚举值
        test_request_data = {
            "procurement_project_type": "货物类",  # 正确的枚举值
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
            "bidding_doc": {
                "filename": "test.docx",
                "extension": ".docx",
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 1024,
                "url": "http://example.com/test.docx",
            },
        }

        print("测试数据:")
        print(f"  采购项目类型: {test_request_data['procurement_project_type']}")
        print(f"  项目类别: {test_request_data['project_category']}")
        print(f"  招标采购方式: {test_request_data['bidding_procurement_method']}")

        # 验证请求
        validated_request = ParameterValidator.validate_compliance_request(
            test_request_data
        )

        print(f"✅ 请求验证成功")
        print(f"  验证后的采购项目类型: {validated_request.procurement_project_type}")
        print(f"  验证后的项目类别: {validated_request.project_category}")

        return True

    except Exception as e:
        print(f"❌ 验证器测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("验证器修复测试")
    print("=" * 50)

    if test_validator_fix():
        print("\n🎉 验证器修复成功！")
        return 0
    else:
        print("\n❌ 验证器修复失败")
        return 1


if __name__ == "__main__":
    exit(main())
