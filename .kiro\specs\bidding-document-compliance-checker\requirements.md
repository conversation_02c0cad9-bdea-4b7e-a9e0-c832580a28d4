# Requirements Document

## Introduction

招标文件合规性检查助手是一个基于Python + FastAPI + OpenAI大模型的API接口，用于自动检查招标文件的合规性。系统接收招标文件（docx/pdf格式）和项目信息，通过大模型分析文件内容，识别合规性、逻辑性、风险管理、规范性、公平性、可操作性等问题，并调用敏感词检测服务，最终返回结构化的检查结果。

## Requirements

### Requirement 1

**User Story:** 作为招标管理人员，我希望能够上传招标文件并获得合规性检查结果，以便及时发现和修正文件中的问题。

#### Acceptance Criteria

1. WHEN 用户上传文件 THEN 系统 SHALL 验证文件格式必须为docx或pdf
2. WHEN 文件格式不符合要求 THEN 系统 SHALL 返回格式错误信息
3. WHEN 文件上传成功 THEN 系统 SHALL 提取文件内容并转换为markdown格式
4. WHEN 文件内容提取失败 THEN 系统 SHALL 记录错误日志并返回处理失败信息

### Requirement 2

**User Story:** 作为招标管理人员，我希望系统能够根据项目类型和采购方式进行针对性的合规性检查，以确保检查结果的准确性。

#### Acceptance Criteria

1. WHEN 接收到项目参数 THEN 系统 SHALL 验证procurement_project_type、project_category、bidding_procurement_method参数
2. WHEN 参数验证通过 THEN 系统 SHALL 根据项目类型调整系统提示词
3. WHEN 调用大模型 THEN 系统 SHALL 传入优化后的系统提示词和文件内容
4. WHEN 大模型返回结果 THEN 系统 SHALL 验证返回内容的JSON格式

### Requirement 3

**User Story:** 作为招标管理人员，我希望系统能够检测文件中的敏感词，以确保招标文件符合相关规定。

#### Acceptance Criteria

1. WHEN 文件内容提取完成 THEN 系统 SHALL 调用敏感词检测API
2. WHEN 调用敏感词API THEN 系统 SHALL 传入markdown格式的文件内容
3. WHEN 敏感词API返回结果 THEN 系统 SHALL 解析敏感词检测结果
4. WHEN 敏感词API调用失败 THEN 系统 SHALL 记录错误日志并继续处理其他检查项

### Requirement 4

**User Story:** 作为招标管理人员，我希望获得结构化的检查结果，包括问题描述、原文内容、质量控制要点和处理建议，以便快速定位和解决问题。

#### Acceptance Criteria

1. WHEN 大模型返回检查结果 THEN 系统 SHALL 验证结果包含必要字段
2. WHEN 敏感词检测完成 THEN 系统 SHALL 整合敏感词结果到最终输出
3. WHEN 结果整合完成 THEN 系统 SHALL 按照指定格式返回JSON数据
4. WHEN 任何检查项失败 THEN 系统 SHALL 返回空数据格式{"sensitiveWordsArr": [], "checkResultArr": []}

### Requirement 5

**User Story:** 作为系统管理员，我希望系统具备完善的日志记录和异常处理机制，以便监控系统运行状态和排查问题。

#### Acceptance Criteria

1. WHEN 系统启动 THEN 系统 SHALL 加载环境配置文件.env中的模型参数
2. WHEN 处理请求 THEN 系统 SHALL 记录请求日志包括输入参数和处理状态
3. WHEN 发生异常 THEN 系统 SHALL 捕获异常并记录详细错误信息
4. WHEN 异常处理完成 THEN 系统 SHALL 返回友好的错误响应而不暴露系统内部信息

### Requirement 6

**User Story:** 作为开发人员，我希望系统具备良好的可配置性和可测试性，以便于维护和扩展。

#### Acceptance Criteria

1. WHEN 系统部署 THEN 系统 SHALL 支持通过.env文件配置模型参数
2. WHEN 进行测试 THEN 系统 SHALL 提供测试接口和测试用例
3. WHEN 模型参数变更 THEN 系统 SHALL 无需重新编译即可生效
4. WHEN 系统运行 THEN 系统 SHALL 支持健康检查接口

### Requirement 7

**User Story:** 作为系统管理员，我希望对输入参数进行严格的枚举值验证，以确保数据的准确性和系统的稳定性。

#### Acceptance Criteria

1. WHEN 接收procurement_project_type参数 THEN 系统 SHALL 验证值必须是"工程类"、"服务类"、"货物类"之一
2. WHEN 接收project_category参数 THEN 系统 SHALL 验证值必须是"依法招标"、"非依法招标"、"政府采购"之一
3. WHEN 接收bidding_procurement_method参数 THEN 系统 SHALL 验证值必须是"公开招标"、"单一来源"、"竞争性磋商"、"竞争性磋商邀请"、"邀请招标"、"竞争性谈判"、"公开竞价"、"邀请竞价"、"询价"、"其他"、"比选"之一
4. WHEN 参数值不在允许范围内 THEN 系统 SHALL 返回详细的参数验证错误信息

### Requirement 8

**User Story:** 作为API调用方，我希望接口具备良好的性能和稳定性，能够处理大文件和并发请求。

#### Acceptance Criteria

1. WHEN 处理大文件 THEN 系统 SHALL 在合理时间内完成处理（不超过60秒）
2. WHEN 接收并发请求 THEN 系统 SHALL 正确处理多个同时请求
3. WHEN 模型上下文超限 THEN 系统 SHALL 截断内容并记录警告日志
4. WHEN 输出token超限 THEN 系统 SHALL 限制输出长度并确保JSON格式完整