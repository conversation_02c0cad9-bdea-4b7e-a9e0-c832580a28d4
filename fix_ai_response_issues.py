#!/usr/bin/env python3
"""
修复AI响应问题的脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def fix_ai_prompt_and_parsing():
    """修复AI提示词和解析逻辑"""
    print("=== 修复AI提示词和解析逻辑 ===")

    service_file = "app/services/ai_model_service.py"
    if not os.path.exists(service_file):
        print(f"❌ 文件不存在: {service_file}")
        return False

    try:
        with open(service_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 备份原文件
        backup_file = f"{service_file}.backup2"
        with open(backup_file, "w", encoding="utf-8") as f:
            f.write(content)
        print(f"✅ 已备份原文件到: {backup_file}")

        # 修复1: 改进问题类型处理逻辑
        old_type_validation = """                        # 验证问题类型
                        ques_type = item.get("quesType", "规范性")
                        if ques_type not in [e.value for e in QuestionType]:
                            log.warning(f"无效的问题类型: {ques_type}, 使用默认值")
                            ques_type = QuestionType.STANDARDIZATION.value"""

        new_type_validation = """                        # 验证问题类型
                        ques_type = item.get("quesType", "规范性")
                        
                        # 处理组合类型（如"规范性/可操作性"），取第一个类型
                        if "/" in ques_type:
                            ques_type = ques_type.split("/")[0].strip()
                            log.debug(f"检测到组合问题类型，使用第一个类型: {ques_type}")
                        
                        # 验证问题类型是否有效
                        valid_types = [e.value for e in QuestionType]
                        if ques_type not in valid_types:
                            log.warning(f"无效的问题类型: {ques_type}, 使用默认值")
                            ques_type = QuestionType.STANDARDIZATION.value"""

        if old_type_validation in content:
            content = content.replace(old_type_validation, new_type_validation)
            print("✅ 已改进问题类型处理逻辑")
        else:
            print("⚠️  未找到完全匹配的问题类型验证代码")

        # 修复2: 改进JSON解析的调试信息
        old_debug_line = """            log.debug(f"原始AI响应长度: {len(raw_response)} 字符")
            log.debug(f"原始AI响应前200字符: {raw_response[:200]}")"""

        new_debug_line = """            log.debug(f"原始AI响应长度: {len(raw_response)} 字符")
            log.debug(f"原始AI响应前200字符: {raw_response[:200]}")
            
            # 检查响应是否包含常见的非JSON内容
            if "抱歉" in raw_response or "无法" in raw_response or "不能" in raw_response:
                log.warning(f"AI响应包含拒绝性内容，可能无法提供有效结果")
            
            if len(raw_response) < 50:
                log.warning(f"AI响应过短，可能不包含完整的JSON结果")"""

        if old_debug_line in content:
            content = content.replace(old_debug_line, new_debug_line)
            print("✅ 已改进JSON解析的调试信息")

        # 保存修改后的文件
        with open(service_file, "w", encoding="utf-8") as f:
            f.write(content)

        print(f"✅ 已保存修复后的文件: {service_file}")
        return True

    except Exception as e:
        print(f"❌ 修复AI服务失败: {e}")
        return False


def create_improved_prompt_test():
    """创建改进的提示词测试脚本"""
    print("=== 创建改进的提示词测试脚本 ===")

    test_script = '''#!/usr/bin/env python3
"""
测试改进的AI提示词
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_improved_prompt():
    """测试改进的AI提示词"""
    print("开始测试改进的AI提示词...")
    
    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
            QuestionType,
        )
        
        ai_service = AIModelService()
        
        # 创建测试项目信息
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )
        
        # 创建包含明显问题的测试内容
        test_content = """
        招标公告
        
        一、项目基本情况
        项目编号：TEST-2025-001
        项目名称：某品牌电脑采购项目
        采购预算：未明确
        
        二、投标人资格要求
        1. 必须是某某品牌的授权代理商
        2. 注册资金不少于1000万元
        3. 近三年年均营业额不少于5000万元
        
        三、技术要求
        1. 必须提供某某品牌产品
        2. 配置要求：最好的处理器，最优秀的显卡
        3. 质量要求：达到世界一流水平
        
        四、商务条款
        1. 投标保证金：未明确
        2. 履约保证金：未明确
        3. 付款方式：验收后付款（未明确具体时间）
        
        五、评标办法
        价格分占100%
        
        六、其他要求
        1. 投标文件必须使用指定格式
        2. 开标时间：待定
        3. 联系方式：详见附件（但未提供附件）
        """
        
        print(f"测试内容长度: {len(test_content)} 字符")
        print("\\n测试内容包含的明显问题:")
        print("- 指定品牌（违反公平竞争原则）")
        print("- 预算未明确")
        print("- 主观评价词汇（最好的、最优秀的、一流）")
        print("- 投标保证金未明确")
        print("- 评标办法不合理（价格分100%）")
        print("- 开标时间待定")
        
        print("\\n开始AI合规性检查...")
        start_time = time.time()
        
        try:
            result = ai_service.check_compliance(test_content, project_info, "improved-prompt-test")
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"\\n✅ AI模型调用成功!")
            print(f"调用时间: {duration:.2f}秒")
            print(f"发现问题数量: {len(result.checkResultArr)}")
            
            if len(result.checkResultArr) > 0:
                print("\\n发现的问题:")
                for i, item in enumerate(result.checkResultArr):
                    print(f"  {i+1}. 类型: {item.quesType}")
                    print(f"     描述: {item.quesDesc[:150]}...")
                    print(f"     建议: {item.advice[:100]}...")
                    print()
                
                # 统计问题类型
                type_counts = {}
                for item in result.checkResultArr:
                    type_counts[item.quesType] = type_counts.get(item.quesType, 0) + 1
                
                print("问题类型统计:")
                for qtype, count in type_counts.items():
                    print(f"  {qtype}: {count}个")
                
                if len(result.checkResultArr) >= 5:
                    print("\\n✅ AI模型能够识别多种类型的合规性问题")
                else:
                    print("\\n⚠️  AI模型识别的问题较少，可能需要进一步优化提示词")
            else:
                print("\\n❌ 未发现任何问题，这明显不正常！")
                print("可能的原因:")
                print("1. AI模型提示词需要进一步优化")
                print("2. AI模型响应格式仍有问题")
                print("3. JSON解析逻辑存在bug")
            
            return len(result.checkResultArr) > 0
            
        except Exception as e:
            print(f"\\n❌ AI模型调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_prompt()
    if success:
        print("\\n🎉 测试成功！AI模型能够正确识别合规性问题。")
    else:
        print("\\n❌ 测试失败，需要进一步调试。")
'''

    test_file = "test_improved_prompt.py"
    try:
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(test_script)

        print(f"✅ 已创建改进的提示词测试脚本: {test_file}")
        return True
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {e}")
        return False


def debug_real_file_response():
    """调试真实文件的AI响应"""
    print("=== 调试真实文件的AI响应 ===")

    debug_script = '''#!/usr/bin/env python3
"""
调试真实文件AI响应的脚本
"""

import sys
import os
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_real_file_ai_response():
    """调试真实文件的AI响应"""
    print("开始调试真实文件的AI响应...")
    
    # 真实文件URL
    file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"
    
    try:
        # 1. 先下载文件内容
        print("1. 下载文件内容...")
        response = requests.get(file_url, timeout=30)
        if response.status_code != 200:
            print(f"❌ 文件下载失败: {response.status_code}")
            return False
        
        print(f"✅ 文件下载成功，大小: {len(response.content)} 字节")
        
        # 2. 处理文件内容
        print("2. 处理文件内容...")
        try:
            from app.services.file_processor_v2 import FileProcessorV2
            
            processor = FileProcessorV2()
            
            # 保存临时文件
            temp_file = "temp_debug_file.docx"
            with open(temp_file, 'wb') as f:
                f.write(response.content)
            
            # 处理文件
            processed_content = processor.process_file(temp_file, "debug-file")
            
            # 清理临时文件
            os.remove(temp_file)
            
            print(f"✅ 文件处理成功，内容长度: {len(processed_content)} 字符")
            print(f"内容前500字符: {processed_content[:500]}...")
            
        except Exception as e:
            print(f"❌ 文件处理失败: {e}")
            return False
        
        # 3. 直接调用AI模型
        print("3. 直接调用AI模型...")
        try:
            from app.services.ai_model_service import AIModelService
            from app.models.schemas import ProjectInfo
            from app.models.enums import (
                ProcurementProjectType,
                ProjectCategory,
                BiddingProcurementMethod,
            )
            
            ai_service = AIModelService()
            
            project_info = ProjectInfo(
                procurement_project_type=ProcurementProjectType.SERVICE,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            )
            
            # 截取内容前10000字符进行测试
            test_content = processed_content[:10000] if len(processed_content) > 10000 else processed_content
            print(f"测试内容长度: {len(test_content)} 字符")
            
            result = ai_service.check_compliance(test_content, project_info, "debug-real-file")
            
            print(f"✅ AI模型调用成功!")
            print(f"发现问题数量: {len(result.checkResultArr)}")
            
            if len(result.checkResultArr) > 0:
                print("发现的问题:")
                for i, item in enumerate(result.checkResultArr[:5]):  # 只显示前5个
                    print(f"  {i+1}. {item.quesType}: {item.quesDesc[:100]}...")
                return True
            else:
                print("❌ 未发现任何问题，需要进一步调试")
                
                # 尝试直接调用模型获取原始响应
                print("\\n4. 获取AI模型原始响应...")
                
                system_prompt = f"""你是一个专业的招标文件合规性检查助手。请仔细审查招标文件内容，识别其中可能存在的合规性问题。

项目信息：
- 采购项目类型：{project_info.procurement_project_type.value}
- 项目类别：{project_info.project_category.value}
- 招标采购方式：{project_info.bidding_procurement_method.value}

请严格按照以下JSON格式返回结果，问题类型必须是以下之一：合规性、逻辑性、风险管理、规范性、公平性、可操作性

{{
    "checkResultArr": [
        {{
            "quesType": "规范性",
            "quesDesc": "问题描述",
            "originalArr": ["原文内容"],
            "point": "问题要点",
            "advice": "修改建议"
        }}
    ]
}}

如果没有发现问题，请返回：
{{
    "checkResultArr": []
}}"""

                user_prompt = f"请对以下招标文件进行合规性审查：\\n\\n{test_content[:5000]}"
                
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ]
                
                raw_response = ai_service.call_model(messages, "debug-raw")
                print(f"AI原始响应长度: {len(raw_response)} 字符")
                print(f"AI原始响应内容: {raw_response}")
                
                # 尝试清理和解析
                cleaned = ai_service.clean_json_data(raw_response)
                print(f"\\n清理后响应: {cleaned}")
                
                try:
                    parsed = json.loads(cleaned)
                    print(f"\\n✅ JSON解析成功: {parsed}")
                except Exception as parse_error:
                    print(f"\\n❌ JSON解析失败: {parse_error}")
                
                return False
                
        except Exception as e:
            print(f"❌ AI模型调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_real_file_ai_response()
    if success:
        print("\\n🎉 调试成功！")
    else:
        print("\\n❌ 调试发现问题，需要进一步修复。")
'''

    debug_file = "debug_real_file_response.py"
    try:
        with open(debug_file, "w", encoding="utf-8") as f:
            f.write(debug_script)

        print(f"✅ 已创建真实文件响应调试脚本: {debug_file}")
        return True
    except Exception as e:
        print(f"❌ 创建调试脚本失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 开始修复AI响应问题...")
    print("=" * 50)

    fixes = [
        ("修复AI提示词和解析逻辑", fix_ai_prompt_and_parsing),
        ("创建改进的提示词测试", create_improved_prompt_test),
        ("创建真实文件响应调试脚本", debug_real_file_response),
    ]

    results = {}

    for fix_name, fix_func in fixes:
        print(f"🔧 执行修复: {fix_name}")
        try:
            result = fix_func()
            results[fix_name] = result
        except Exception as e:
            print(f"❌ 修复失败: {e}")
            results[fix_name] = False
        print()

    # 总结报告
    print("=" * 50)
    print("📊 修复总结报告")
    print("=" * 50)

    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)

    print(f"修复状态: {success_count}/{total_count} 项修复成功")

    for fix_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {fix_name}: {status}")

    if success_count == total_count:
        print("\\n🎉 所有修复都成功了！")
        print("\\n📋 后续步骤:")
        print("  1. 运行 python test_improved_prompt.py 测试改进的提示词")
        print("  2. 运行 python debug_real_file_response.py 调试真实文件响应")
        print("  3. 运行 python test_real_file.py 验证最终效果")
    else:
        print(f"\\n⚠️  {total_count - success_count} 项修复失败，可能需要手动处理")


if __name__ == "__main__":
    main()
