#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标信息处理功能集成测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.bidding_info_processor import bidding_info_processor, BiddingInfoError
from app.models.enums import FileExtension


def test_bidding_info_analysis():
    """测试招标信息分析功能"""
    print("=== 测试招标信息分析功能 ===")

    # 模拟招标文件内容
    test_content = """
    招标公告
    
    项目名称：某某工程项目
    招标人名称：某某建设集团有限公司
    招标人联系人：李经理
    招标人联系电话：0551-12345678
    招标人地址：安徽省合肥市蜀山区某某路100号
    招标人邮箱：<EMAIL>
    
    项目概况：
    本项目为某某工程建设项目...
    
    投标人资格要求：
    1. 具有独立法人资格
    2. 具有相应的资质证书
    ...
    """

    try:
        # 测试招标信息分析
        result = bidding_info_processor.analyze_bidding_info(test_content, "test-001")
        print(f"分析结果: {result}")

        # 测试信息替换
        if result and any(info.strip() for info in result.values()):
            replaced_content = bidding_info_processor.replace_bidding_info(
                test_content, result, "test-001"
            )
            print("\n=== 替换后的内容 ===")
            print(
                replaced_content[:500] + "..."
                if len(replaced_content) > 500
                else replaced_content
            )
        else:
            print("未提取到有效招标信息")

    except BiddingInfoError as e:
        print(f"招标信息处理错误: {e}")
    except Exception as e:
        print(f"测试异常: {e}")


def test_content_extraction():
    """测试内容提取功能（模拟）"""
    print("\n=== 测试内容提取功能 ===")

    # 由于没有真实文件，这里只测试方法是否存在
    try:
        # 检查方法是否存在
        assert hasattr(bidding_info_processor, "extract_first_10_pages")
        assert hasattr(bidding_info_processor, "analyze_bidding_info")
        assert hasattr(bidding_info_processor, "replace_bidding_info")

        print("✓ 所有必需的方法都存在")

        # 测试替换规则配置
        rules = bidding_info_processor.replacement_rules
        print(f"✓ 替换规则配置: {rules}")

    except Exception as e:
        print(f"内容提取测试失败: {e}")


def main():
    """主测试函数"""
    print("招标信息处理功能集成测试")
    print("=" * 50)

    try:
        test_content_extraction()
        test_bidding_info_analysis()

        print("\n" + "=" * 50)
        print("测试完成")

    except Exception as e:
        print(f"测试失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
