#!/usr/bin/env python3
"""
修复AI模型问题的脚本
"""

import sys
import os
import json
import re
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_ai_model_service():
    """修复AI模型服务的问题"""
    print("=== 修复AI模型服务 ===")
    
    service_file = "app/services/ai_model_service.py"
    if not os.path.exists(service_file):
        print(f"❌ 文件不存在: {service_file}")
        return False
    
    try:
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = f"{service_file}.backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 修复1: 改进clean_json_data方法的错误处理
        old_clean_method = '''    def clean_json_data(self, raw_response: str) -> str:
        """
        清理AI模型返回的JSON数据

        Args:
            raw_response: 原始响应

        Returns:
            str: 清理后的JSON字符串
        """
        if not raw_response or not raw_response.strip():
            log.warning("原始响应为空，返回默认空结果JSON")
            return '{"checkResultArr": []}'

        try:
            # 移除可能的前后缀文本
            cleaned = raw_response.strip()

            # 移除markdown代码块标记
            if cleaned.startswith("```json"):
                cleaned = cleaned[7:]
            if cleaned.startswith("```"):
                cleaned = cleaned[3:]
            if cleaned.endswith("```"):
                cleaned = cleaned[:-3]

            cleaned = cleaned.strip()

            # 再次检查清理后是否为空
            if not cleaned:
                log.warning("清理后响应为空，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            # 尝试找到JSON对象的开始和结束
            start_idx = cleaned.find("{")
            end_idx = cleaned.rfind("}")

            if start_idx == -1 or end_idx == -1 or end_idx <= start_idx:
                log.warning("响应中未找到有效的JSON对象，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            cleaned = cleaned[start_idx : end_idx + 1]

            # 修复常见的JSON格式问题
            # 移除注释
            cleaned = re.sub(r"//.*?\\n", "\\n", cleaned)
            cleaned = re.sub(r"/\\*.*?\\*/", "", cleaned, flags=re.DOTALL)

            # 修复尾随逗号
            cleaned = re.sub(r",(\\s*[}\\]])", r"\\1", cleaned)

            # 验证JSON格式
            json.loads(cleaned)

            return cleaned

        except json.JSONDecodeError as e:
            log.warning(f"JSON解析失败: {str(e)}, 返回默认空结果JSON")
            log.debug(f"原始响应: {raw_response}")
            return '{"checkResultArr": []}'
        except Exception as e:
            log.warning(f"JSON清理失败: {str(e)}, 返回默认空结果JSON")
            log.debug(f"原始响应: {raw_response}")
            return '{"checkResultArr": []}'
'''
        
        new_clean_method = '''    def clean_json_data(self, raw_response: str) -> str:
        """
        清理AI模型返回的JSON数据

        Args:
            raw_response: 原始响应

        Returns:
            str: 清理后的JSON字符串
        """
        if not raw_response or not raw_response.strip():
            log.warning("原始响应为空，返回默认空结果JSON")
            return '{"checkResultArr": []}'

        try:
            # 记录原始响应用于调试
            log.debug(f"原始AI响应长度: {len(raw_response)} 字符")
            log.debug(f"原始AI响应前200字符: {raw_response[:200]}")
            
            # 移除可能的前后缀文本
            cleaned = raw_response.strip()

            # 移除markdown代码块标记
            if cleaned.startswith("```json"):
                cleaned = cleaned[7:]
            elif cleaned.startswith("```"):
                cleaned = cleaned[3:]
            if cleaned.endswith("```"):
                cleaned = cleaned[:-3]

            cleaned = cleaned.strip()

            # 再次检查清理后是否为空
            if not cleaned:
                log.warning("清理后响应为空，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            # 检查是否包含基本的JSON结构
            if "{" not in cleaned or "}" not in cleaned:
                log.warning(f"响应不包含JSON结构，原始内容: {cleaned[:100]}")
                return '{"checkResultArr": []}'

            # 尝试找到JSON对象的开始和结束
            start_idx = cleaned.find("{")
            end_idx = cleaned.rfind("}")

            if start_idx == -1 or end_idx == -1 or end_idx <= start_idx:
                log.warning(f"响应中未找到有效的JSON对象，开始位置: {start_idx}, 结束位置: {end_idx}")
                return '{"checkResultArr": []}'

            json_part = cleaned[start_idx : end_idx + 1]
            log.debug(f"提取的JSON部分长度: {len(json_part)} 字符")

            # 修复常见的JSON格式问题
            # 移除注释
            json_part = re.sub(r"//.*?\\n", "\\n", json_part)
            json_part = re.sub(r"/\\*.*?\\*/", "", json_part, flags=re.DOTALL)

            # 修复尾随逗号
            json_part = re.sub(r",(\\s*[}\\]])", r"\\1", json_part)

            # 验证JSON格式
            parsed = json.loads(json_part)
            log.debug(f"JSON解析成功，包含字段: {list(parsed.keys()) if isinstance(parsed, dict) else 'non-dict'}")

            return json_part

        except json.JSONDecodeError as e:
            log.warning(f"JSON解析失败: {str(e)}")
            log.warning(f"尝试解析的内容: {cleaned[:200] if 'cleaned' in locals() else raw_response[:200]}")
            return '{"checkResultArr": []}'
        except Exception as e:
            log.warning(f"JSON清理失败: {str(e)}")
            log.debug(f"原始响应: {raw_response[:200]}")
            return '{"checkResultArr": []}'
'''
        
        # 替换clean_json_data方法
        if old_clean_method.strip() in content:
            content = content.replace(old_clean_method.strip(), new_clean_method.strip())
            print("✅ 已改进clean_json_data方法的错误处理")
        else:
            print("⚠️  未找到完全匹配的clean_json_data方法，可能需要手动修复")
        
        # 修复2: 改进超时设置
        old_timeout_line = "        timeout: float = 30.0,"
        new_timeout_line = "        timeout: float = 60.0,"
        
        if old_timeout_line in content:
            content = content.replace(old_timeout_line, new_timeout_line)
            print("✅ 已将默认超时时间从30秒增加到60秒")
        
        # 修复3: 改进空响应处理
        old_empty_check = '''                    # 检查空响应
                    if not result or not result.strip():
                        error_msg = f"AI模型返回空响应 | 尝试: {attempt+1} | 请求ID: {request_id}"
                        log.warning(error_msg)'''
        
        new_empty_check = '''                    # 检查空响应
                    if not result or not result.strip():
                        error_msg = f"AI模型返回空响应 | 尝试: {attempt+1} | 请求ID: {request_id}"
                        log.warning(error_msg)
                        log.debug(f"空响应的原始内容: '{result}', 类型: {type(result)}")'''
        
        if old_empty_check in content:
            content = content.replace(old_empty_check, new_empty_check)
            print("✅ 已改进空响应的调试信息")
        
        # 保存修改后的文件
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已保存修复后的文件: {service_file}")
        return True
        
    except Exception as e:
        print(f"❌ 修复AI模型服务失败: {e}")
        return False

def create_ai_debug_config():
    """创建AI调试配置文件"""
    print("=== 创建AI调试配置 ===")
    
    debug_config = {
        "ai_model": {
            "debug_mode": True,
            "log_requests": True,
            "log_responses": True,
            "timeout_seconds": 60,
            "max_retries": 3,
            "retry_delay": 2.0
        },
        "compliance_check": {
            "enable_fallback": True,
            "min_response_length": 10,
            "require_json_format": True
        },
        "logging": {
            "level": "DEBUG",
            "log_ai_calls": True,
            "log_performance": True
        }
    }
    
    config_file = "ai_debug_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(debug_config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已创建调试配置文件: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建调试配置失败: {e}")
        return False

def create_test_script():
    """创建简化的测试脚本"""
    print("=== 创建简化测试脚本 ===")
    
    test_script = '''#!/usr/bin/env python3
"""
简化的AI模型测试脚本
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_simple():
    """简单的AI模型测试"""
    print("开始简单AI模型测试...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        # 创建AI服务实例
        ai_service = AIModelService()
        
        # 简单测试消息
        messages = [
            {"role": "system", "content": "请返回一个简单的JSON对象: {\\"test\\": \\"success\\"}"},
            {"role": "user", "content": "请按照系统消息的要求返回JSON"}
        ]
        
        print("发送测试消息...")
        start_time = time.time()
        
        response = ai_service.call_model(messages, "simple-test")
        
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f}秒")
        print(f"响应长度: {len(response)}字符")
        print(f"响应内容: {response}")
        
        # 尝试解析JSON
        try:
            cleaned = ai_service.clean_json_data(response)
            parsed = json.loads(cleaned)
            print(f"✅ JSON解析成功: {parsed}")
        except Exception as e:
            print(f"❌ JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_simple()
'''
    
    test_file = "test_ai_simple.py"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print(f"✅ 已创建简化测试脚本: {test_file}")
        return True
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {e}")
        return False

def fix_colorama_issue():
    """修复colorama相关的KeyboardInterrupt问题"""
    print("=== 修复colorama问题 ===")
    
    try:
        # 检查是否可以导入colorama
        import colorama
        print("✅ colorama库可以正常导入")
        
        # 尝试重新安装colorama
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "colorama"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ colorama库已更新")
        else:
            print(f"⚠️  colorama更新失败: {result.stderr}")
        
        return True
    except ImportError:
        print("❌ colorama库未安装或损坏")
        try:
            import subprocess
            result = subprocess.run([sys.executable, "-m", "pip", "install", "colorama"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ colorama库已重新安装")
                return True
            else:
                print(f"❌ colorama安装失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 修复colorama失败: {e}")
            return False
    except Exception as e:
        print(f"❌ 检查colorama时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复AI模型问题...")
    print("=" * 50)
    
    fixes = [
        ("修复AI模型服务", fix_ai_model_service),
        ("创建调试配置", create_ai_debug_config),
        ("创建测试脚本", create_test_script),
        ("修复colorama问题", fix_colorama_issue),
    ]
    
    results = {}
    
    for fix_name, fix_func in fixes:
        print(f"🔧 执行修复: {fix_name}")
        try:
            result = fix_func()
            results[fix_name] = result
        except Exception as e:
            print(f"❌ 修复失败: {e}")
            results[fix_name] = False
        print()
    
    # 总结报告
    print("=" * 50)
    print("📊 修复总结报告")
    print("=" * 50)
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"修复状态: {success_count}/{total_count} 项修复成功")
    
    for fix_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {fix_name}: {status}")
    
    if success_count == total_count:
        print("\\n🎉 所有修复都成功了！")
        print("\\n📋 后续步骤:")
        print("  1. 运行 python diagnose_ai_issues.py 验证修复效果")
        print("  2. 运行 python test_ai_simple.py 测试基本功能")
        print("  3. 运行 python test_real_file.py 测试完整功能")
    else:
        print(f"\\n⚠️  {total_count - success_count} 项修复失败，可能需要手动处理")

if __name__ == "__main__":
    main()
'''