# -*- coding: utf-8 -*-
"""
缓存管理器
实现内存缓存和资源管理功能
"""

import time
import hashlib
import gc
import psutil
import os
from typing import Dict, Any, Optional, Tuple
from threading import Lock
from dataclasses import dataclass
from datetime import datetime, timedelta

from app.core.logger import log, log_function_call
from app.core.config import settings


@dataclass
class CacheItem:
    """缓存项"""

    value: Any
    created_at: float
    access_count: int = 0
    last_accessed: float = 0
    size_bytes: int = 0


class MemoryCache:
    """内存缓存管理器"""

    def __init__(self, max_size_mb: int = 100, ttl_seconds: int = 3600):
        """
        初始化缓存管理器

        Args:
            max_size_mb: 最大缓存大小（MB）
            ttl_seconds: 缓存生存时间（秒）
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, CacheItem] = {}
        self.lock = Lock()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "current_size_bytes": 0,
            "item_count": 0,
        }

    @log_function_call
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项

        Args:
            key: 缓存键

        Returns:
            Optional[Any]: 缓存值，不存在或过期返回None
        """
        with self.lock:
            if key not in self.cache:
                self.stats["misses"] += 1
                return None

            item = self.cache[key]
            current_time = time.time()

            # 检查是否过期
            if current_time - item.created_at > self.ttl_seconds:
                del self.cache[key]
                self.stats["current_size_bytes"] -= item.size_bytes
                self.stats["item_count"] -= 1
                self.stats["misses"] += 1
                return None

            # 更新访问信息
            item.access_count += 1
            item.last_accessed = current_time
            self.stats["hits"] += 1

            return item.value

    @log_function_call
    def set(self, key: str, value: Any, size_hint: Optional[int] = None) -> bool:
        """
        设置缓存项

        Args:
            key: 缓存键
            value: 缓存值
            size_hint: 大小提示（字节）

        Returns:
            bool: 是否设置成功
        """
        with self.lock:
            current_time = time.time()

            # 估算大小
            if size_hint is None:
                size_bytes = self._estimate_size(value)
            else:
                size_bytes = size_hint

            # 检查是否超过最大缓存大小
            if size_bytes > self.max_size_bytes:
                log.warning(
                    f"缓存项过大，无法缓存 | 键: {key} | 大小: {size_bytes} 字节"
                )
                return False

            # 检查是否是新增项
            is_new_item = key not in self.cache

            # 如果键已存在，先删除旧项
            if not is_new_item:
                old_item = self.cache[key]
                self.stats["current_size_bytes"] -= old_item.size_bytes

            # 确保有足够空间
            while (
                self.stats["current_size_bytes"] + size_bytes > self.max_size_bytes
                and self.cache
            ):
                self._evict_lru()

            # 添加新项
            item = CacheItem(
                value=value,
                created_at=current_time,
                access_count=1,
                last_accessed=current_time,
                size_bytes=size_bytes,
            )

            self.cache[key] = item
            self.stats["current_size_bytes"] += size_bytes
            if is_new_item:  # 新增项
                self.stats["item_count"] += 1

            return True

    def _estimate_size(self, obj: Any) -> int:
        """估算对象大小"""
        try:
            import sys

            return sys.getsizeof(obj)
        except Exception:
            # 简单估算
            if isinstance(obj, str):
                return len(obj.encode("utf-8"))
            elif isinstance(obj, (list, tuple)):
                return sum(self._estimate_size(item) for item in obj)
            elif isinstance(obj, dict):
                return sum(
                    self._estimate_size(k) + self._estimate_size(v)
                    for k, v in obj.items()
                )
            else:
                return 1024  # 默认1KB

    def _evict_lru(self):
        """驱逐最少使用的缓存项"""
        if not self.cache:
            return

        # 找到最少使用的项
        lru_key = min(
            self.cache.keys(),
            key=lambda k: (
                self.cache[k].access_count,
                self.cache[k].last_accessed,
            ),
        )

        item = self.cache[lru_key]
        del self.cache[lru_key]
        self.stats["current_size_bytes"] -= item.size_bytes
        self.stats["item_count"] -= 1
        self.stats["evictions"] += 1

        log.debug(f"驱逐缓存项 | 键: {lru_key} | 大小: {item.size_bytes} 字节")

    @log_function_call
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.stats["current_size_bytes"] = 0
            self.stats["item_count"] = 0
            log.info("缓存已清空")

    @log_function_call
    def cleanup_expired(self):
        """清理过期项"""
        with self.lock:
            current_time = time.time()
            expired_keys = []

            for key, item in self.cache.items():
                if current_time - item.created_at > self.ttl_seconds:
                    expired_keys.append(key)

            for key in expired_keys:
                item = self.cache[key]
                del self.cache[key]
                self.stats["current_size_bytes"] -= item.size_bytes
                self.stats["item_count"] -= 1

            if expired_keys:
                log.info(f"清理过期缓存项: {len(expired_keys)}个")

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            hit_rate = (
                self.stats["hits"] / (self.stats["hits"] + self.stats["misses"])
                if (self.stats["hits"] + self.stats["misses"]) > 0
                else 0
            )

            return {
                **self.stats,
                "hit_rate": hit_rate,
                "max_size_mb": self.max_size_bytes / 1024 / 1024,
                "current_size_mb": self.stats["current_size_bytes"] / 1024 / 1024,
                "utilization": (
                    self.stats["current_size_bytes"] / self.max_size_bytes
                    if self.max_size_bytes > 0
                    else 0
                ),
            }


class ResourceManager:
    """资源管理器"""

    def __init__(self):
        """初始化资源管理器"""
        self.process = psutil.Process(os.getpid())
        self.memory_threshold_mb = 2048  # 2GB内存阈值
        self.gc_threshold = 0.8  # 内存使用率达到80%时触发GC

    @log_function_call
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()

            return {
                "rss_mb": memory_info.rss / 1024 / 1024,  # 物理内存
                "vms_mb": memory_info.vms / 1024 / 1024,  # 虚拟内存
                "percent": memory_percent,
                "available_mb": psutil.virtual_memory().available / 1024 / 1024,
                "threshold_mb": self.memory_threshold_mb,
            }
        except Exception as e:
            log.error(f"获取内存使用情况失败: {str(e)}")
            return {}

    @log_function_call
    def check_memory_pressure(self) -> bool:
        """检查内存压力"""
        try:
            memory_usage = self.get_memory_usage()
            rss_mb = memory_usage.get("rss_mb", 0)

            if rss_mb > self.memory_threshold_mb:
                log.warning(
                    f"内存使用超过阈值 | 当前: {rss_mb:.1f}MB | 阈值: {self.memory_threshold_mb}MB"
                )
                return True

            memory_percent = memory_usage.get("percent", 0)
            if memory_percent > self.gc_threshold * 100:
                log.warning(f"内存使用率过高 | 当前: {memory_percent:.1f}%")
                return True

            return False
        except Exception as e:
            log.error(f"检查内存压力失败: {str(e)}")
            return False

    @log_function_call
    def force_garbage_collection(self) -> Dict[str, int]:
        """强制垃圾回收"""
        try:
            before_memory = self.get_memory_usage().get("rss_mb", 0)

            # 执行垃圾回收
            collected = {
                "gen0": gc.collect(0),
                "gen1": gc.collect(1),
                "gen2": gc.collect(2),
            }

            after_memory = self.get_memory_usage().get("rss_mb", 0)
            freed_mb = before_memory - after_memory

            log.info(
                f"垃圾回收完成 | 回收对象: {sum(collected.values())}个 | "
                f"释放内存: {freed_mb:.1f}MB"
            )

            return {
                **collected,
                "total_collected": sum(collected.values()),
                "memory_freed_mb": freed_mb,
            }
        except Exception as e:
            log.error(f"垃圾回收失败: {str(e)}")
            return {}

    @log_function_call
    def optimize_memory(self) -> Dict[str, Any]:
        """内存优化"""
        results = {"actions_taken": []}

        try:
            # 检查内存压力
            if self.check_memory_pressure():
                results["actions_taken"].append("memory_pressure_detected")

                # 强制垃圾回收
                gc_result = self.force_garbage_collection()
                results["garbage_collection"] = gc_result
                results["actions_taken"].append("garbage_collection")

                # 清理缓存（如果有全局缓存实例）
                if hasattr(self, "cache") and self.cache:
                    self.cache.cleanup_expired()
                    results["actions_taken"].append("cache_cleanup")

            results["memory_usage"] = self.get_memory_usage()
            return results

        except Exception as e:
            log.error(f"内存优化失败: {str(e)}")
            results["error"] = str(e)
            return results


class DocumentCache:
    """文档处理缓存"""

    def __init__(self, cache_manager: MemoryCache):
        """初始化文档缓存"""
        self.cache = cache_manager

    def _generate_cache_key(self, file_url: str, file_size: int) -> str:
        """生成缓存键"""
        content = f"{file_url}:{file_size}"
        return hashlib.md5(content.encode()).hexdigest()

    @log_function_call
    def get_processed_content(self, file_url: str, file_size: int) -> Optional[str]:
        """获取已处理的文档内容"""
        cache_key = self._generate_cache_key(file_url, file_size)
        return self.cache.get(cache_key)

    @log_function_call
    def cache_processed_content(
        self, file_url: str, file_size: int, content: str
    ) -> bool:
        """缓存已处理的文档内容"""
        cache_key = self._generate_cache_key(file_url, file_size)
        content_size = len(content.encode("utf-8"))
        return self.cache.set(cache_key, content, content_size)


# 创建全局实例
memory_cache = MemoryCache(
    max_size_mb=getattr(settings, "cache_max_size_mb", 100),
    ttl_seconds=getattr(settings, "cache_ttl_seconds", 3600),
)

resource_manager = ResourceManager()
document_cache = DocumentCache(memory_cache)
