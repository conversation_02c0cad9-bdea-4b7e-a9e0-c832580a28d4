# 需求文档

## 介绍

本功能解决合规性检查服务流水线中发现的关键bug，这些bug导致空结果、数据转换失败和性能问题。系统目前无法正确处理AI模型响应、转换敏感词结果和聚合最终输出，导致合规性检查结果不正确。

## 需求

### 需求1

**用户故事：** 作为系统管理员，我希望AI模型服务能够优雅地处理空响应，这样合规性检查就不会静默失败并返回空结果。

#### 验收标准

1. 当AI模型返回空响应时，系统应记录包含模型名称和请求详情的详细错误消息
2. 当由于内容为空导致JSON解析失败时，系统应返回结构化错误响应而不是空结果
3. 当AI模型调用超时或失败时，系统应实现带指数退避的重试逻辑
4. 如果AI模型持续返回空响应，系统应切换到备用模型或降级模式

### 需求2

**用户故事：** 作为系统管理员，我希望MarkItDown服务依赖得到正确管理，这样当服务不可用时文档处理不会失败。

#### 验收标准

1. 当MarkItDown服务不可用时，系统应无缝使用备用提取方法
2. 当使用备用提取时，系统应记录使用备用方案的原因
3. 如果MarkItDown重新可用，系统应自动恢复使用它以获得更好的提取质量
4. 当文档处理失败时，系统应提供关于尝试了哪种提取方法的清晰错误消息

### 需求3

**用户故事：** 作为开发人员，我希望敏感词结果转换能够正确工作，这样检测到的敏感词能够正确包含在最终响应中。

#### 验收标准

1. 当转换SensitiveWordResult对象时，系统应使用正确的属性访问方法
2. 当检测到敏感词时，系统应在转换中保留所有检测到的词信息
3. 如果转换失败，系统应记录具体错误并尝试替代转换方法
4. 当发现多个敏感词时，系统应转换所有结果而不丢失数据

### 需求4

**用户故事：** 作为最终用户，我希望所有检测到的合规性问题和敏感词都出现在最终响应中，这样我就能看到完整的分析结果。

#### 验收标准

1. 当检测到敏感词时，系统应将它们包含在最终聚合结果中
2. 当AI合规性检查发现问题时，系统应将它们包含在最终响应中
3. 当同时发现敏感词和合规性问题时，系统应正确合并它们
4. 如果结果聚合失败，系统应返回带有错误指示器的部分结果

### 需求5

**用户故事：** 作为系统管理员，我希望合规性检查流水线在合理的时间限制内完成，这样用户就不会经历过长的等待时间。

#### 验收标准

1. 当AI模型调用超过30秒时，系统应实现超时处理
2. 当总流水线超过60秒时，系统应记录性能警告
3. 当检测到性能问题时，系统应提供详细的时间分解
4. 如果流水线持续性能不佳，系统应建议优化建议

### 需求6

**用户故事：** 作为开发人员，我希望有全面的错误日志记录和监控，这样我就能快速识别和解决合规性服务中的问题。

#### 验收标准

1. 当任何服务组件失败时，系统应记录包含请求ID的详细错误信息
2. 当数据转换失败时，系统应记录原始数据结构和转换尝试
3. 当超过性能阈值时，系统应记录每个流水线阶段的时间详情
4. 如果发生关键错误，系统应向监控系统发送警报