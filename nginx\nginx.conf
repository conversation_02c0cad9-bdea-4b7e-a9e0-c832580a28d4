# Nginx配置 - 招标文件合规性检查助手

# 工作进程数，通常设置为CPU核心数
worker_processes auto;

# 错误日志
error_log /var/log/nginx/error.log warn;

# 进程ID文件
pid /var/run/nginx.pid;

# 事件配置
events {
    # 每个工作进程的最大连接数
    worker_connections 1024;
    
    # 使用epoll事件模型（Linux）
    use epoll;
    
    # 允许一个工作进程同时接受多个连接
    multi_accept on;
}

# HTTP配置
http {
    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # 访问日志
    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 隐藏Nginx版本信息
    server_tokens off;

    # 客户端配置
    client_max_body_size 300M;  # 最大文件上传大小
    client_body_timeout 60s;
    client_header_timeout 60s;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=health:10m rate=30r/s;

    # 上游服务器配置
    upstream compliance_checker {
        # 负载均衡方式：least_conn（最少连接）
        least_conn;
        
        # 后端服务器
        server compliance-checker:8088 max_fails=3 fail_timeout=30s;
        
        # 如果有多个实例，可以添加更多服务器
        # server compliance-checker-2:8088 max_fails=3 fail_timeout=30s;
        
        # 保持连接
        keepalive 32;
    }

    # HTTP服务器配置（重定向到HTTPS）
    server {
        listen 80;
        server_name _;
        
        # 健康检查端点（允许HTTP访问）
        location /health {
            proxy_pass http://compliance_checker/health;
            access_log off;
        }
        
        # 其他请求重定向到HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS服务器配置
    server {
        listen 443 ssl http2;
        server_name your-domain.com;  # 替换为实际域名

        # SSL证书配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # HSTS
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

        # 安全头
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;

        # 代理配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # 健康检查端点（无限流）
        location /health {
            limit_req zone=health burst=10 nodelay;
            
            proxy_pass http://compliance_checker/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            access_log off;
        }

        # API接口（限流保护）
        location /api/ {
            # 限流：每秒10个请求，突发20个
            limit_req zone=api burst=20 nodelay;
            
            # 代理到后端服务
            proxy_pass http://compliance_checker;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            
            # 禁用缓存（API响应）
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            add_header Expires "0" always;
        }

        # 根路径
        location / {
            proxy_pass http://compliance_checker;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
        }

        # 静态文件缓存（如果有）
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            
            # 如果文件不存在，代理到后端
            try_files $uri @backend;
        }

        # 后端代理（用于静态文件回退）
        location @backend {
            proxy_pass http://compliance_checker;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # 禁止访问备份文件
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }

    # 开发环境配置（可选）
    server {
        listen 8080;
        server_name localhost;

        # 开发环境不使用SSL
        location / {
            proxy_pass http://compliance_checker;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}