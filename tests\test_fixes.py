#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复验证脚本
"""


def test_enum_fix():
    """测试枚举修复"""
    print("测试枚举修复...")

    try:
        from app.models.enums import ProjectCategory
        from app.core.validators import get_enum_values

        # 测试正确的枚举值
        print(
            f"ProjectCategory.GOVERNMENT_PROCUREMENT: {ProjectCategory.GOVERNMENT_PROCUREMENT}"
        )

        # 测试get_enum_values函数
        enum_values = get_enum_values()
        print(f"枚举值键: {list(enum_values.keys())}")

        # 检查是否包含期望的键
        expected_keys = ["ProcurementProjectType", "ProjectCategory"]
        for key in expected_keys:
            if key in enum_values:
                print(f"✅ 找到键: {key}")
            else:
                print(f"❌ 缺少键: {key}")

        return True

    except Exception as e:
        print(f"❌ 枚举测试失败: {e}")
        return False


def test_data_model_fix():
    """测试数据模型修复"""
    print("\n测试数据模型修复...")

    try:
        from app.models.schemas import ProjectInfo, ComplianceCheckRequest, FileInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
            FileExtension,
            MimeType,
        )

        # 测试ProjectInfo
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,  # 使用正确的枚举值
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )
        print(f"✅ ProjectInfo创建成功: {project_info.project_category}")

        # 测试ComplianceCheckRequest
        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        request = ComplianceCheckRequest(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,  # 使用正确的枚举值
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            bidding_doc=file_info,
        )
        print(f"✅ ComplianceCheckRequest创建成功: {request.project_category}")

        return True

    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


def main():
    """主函数"""
    print("修复验证测试")
    print("=" * 50)

    tests = [
        ("枚举修复", test_enum_fix),
        ("数据模型修复", test_data_model_fix),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)

        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")

    print(f"\n{'=' * 50}")
    print(f"测试总结: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有修复验证通过！")
        return 0
    else:
        print("❌ 部分修复验证失败")
        return 1


if __name__ == "__main__":
    exit(main())
