#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合规性检查服务演示脚本
展示端到端的集成功能
"""

import asyncio
import json
from typing import Dict, Any

from app.services.compliance_service import compliance_service
from app.models.schemas import ComplianceCheckRequest, FileInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)
from app.core.logger import log


def create_sample_request() -> ComplianceCheckRequest:
    """创建示例请求"""
    file_info = FileInfo(
        filename="sample_bidding_document.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=2 * 1024 * 1024,  # 2MB
        url="http://example.com/sample_bidding_document.docx",
    )

    return ComplianceCheckRequest(
        procurement_project_type=ProcurementProjectType.GOODS,
        project_category=ProjectCategory.GOVERNMENT,
        bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        bidding_doc=file_info,
    )


def print_service_status():
    """打印服务状态信息"""
    print("\n" + "=" * 60)
    print("合规性检查服务状态")
    print("=" * 60)

    try:
        status = compliance_service.get_service_status()
        print(f"服务名称: {status['service_info']['service_name']}")
        print(f"版本: {status['service_info']['version']}")
        print(f"支持格式: {', '.join(status['service_info']['supported_formats'])}")
        print(f"最大文件大小: {status['service_info']['max_file_size_mb']}MB")

        print("\n健康状态:")
        health = status["health_status"]
        for service, healthy in health.items():
            status_text = "✅ 健康" if healthy else "❌ 不健康"
            print(f"  {service}: {status_text}")

        print("\n流水线统计:")
        stats = status["pipeline_stats"]
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功请求数: {stats['successful_requests']}")
        print(f"  失败请求数: {stats['failed_requests']}")
        print(f"  降级请求数: {stats['fallback_requests']}")
        print(f"  平均处理时间: {stats['average_processing_time']:.3f}秒")
        print(f"  成功率: {stats['success_rate']:.1%}")
        print(f"  降级率: {stats['fallback_rate']:.1%}")

    except Exception as e:
        print(f"获取服务状态失败: {str(e)}")


def print_processing_metrics():
    """打印处理指标"""
    print("\n" + "=" * 60)
    print("处理指标详情")
    print("=" * 60)

    try:
        metrics = compliance_service.get_processing_metrics()

        print("\n组件信息:")
        components = metrics["component_info"]

        # 文件处理器信息
        file_processor = components["file_processor"]
        print(f"  文件处理器:")
        print(f"    MarkItDown可用: {file_processor['markitdown_available']}")
        print(f"    支持格式: {', '.join(file_processor['supported_formats'])}")
        print(f"    最大文件大小: {file_processor['max_file_size_mb']}MB")

        # AI模型信息
        ai_model = components["ai_model"]
        print(f"  AI模型:")
        print(f"    模型名称: {ai_model['model_name']}")
        print(f"    客户端初始化: {ai_model['client_initialized']}")
        print(f"    最大重试次数: {ai_model['max_retries']}")

        # 敏感词服务信息
        sensitive_word = components["sensitive_word"]
        print(f"  敏感词服务:")
        print(f"    基础URL: {sensitive_word['base_url']}")
        print(f"    超时时间: {sensitive_word['timeout']}秒")
        print(f"    最大重试次数: {sensitive_word['max_retries']}")

        # 结果处理器信息
        result_processor = components["result_processor"]
        print(f"  结果处理器:")
        print(f"    去重启用: {result_processor['deduplication_enabled']}")
        print(f"    排序启用: {result_processor['sorting_enabled']}")

    except Exception as e:
        print(f"获取处理指标失败: {str(e)}")


def simulate_compliance_check():
    """模拟合规性检查"""
    print("\n" + "=" * 60)
    print("模拟合规性检查")
    print("=" * 60)

    try:
        # 创建示例请求
        request = create_sample_request()
        print(f"文件名: {request.bidding_doc.filename}")
        print(f"文件大小: {request.bidding_doc.size / 1024 / 1024:.1f}MB")
        print(f"项目类型: {request.procurement_project_type.value}")
        print(f"项目类别: {request.project_category.value}")
        print(f"采购方式: {request.bidding_procurement_method.value}")

        print("\n注意: 这是一个演示脚本，实际的文件下载和处理需要真实的文件URL")
        print("在生产环境中，请提供有效的文件URL进行测试")

        # 在实际环境中，这里会执行真实的合规性检查
        # result = compliance_service.check_compliance(request, "demo-001")
        # print(f"\n检查结果:")
        # print(f"  敏感词数量: {len(result.sensitiveWordsArr)}")
        # print(f"  检查问题数量: {len(result.checkResultArr)}")

    except Exception as e:
        print(f"模拟检查失败: {str(e)}")


def main():
    """主函数"""
    print("合规性检查服务集成演示")
    print("=" * 60)

    # 1. 显示服务状态
    print_service_status()

    # 2. 显示处理指标
    print_processing_metrics()

    # 3. 模拟合规性检查
    simulate_compliance_check()

    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
