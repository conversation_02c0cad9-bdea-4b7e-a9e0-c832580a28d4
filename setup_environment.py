#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境设置和依赖安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    if description:
        print(f"执行: {description}")
    print(f"命令: {command}")
    print("=" * 60)

    try:
        result = subprocess.run(
            command, shell=True, check=True, capture_output=True, text=True
        )
        print("✅ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")

    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False

    print("✅ Python版本符合要求")
    return True


def install_requirements():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")

    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False

    print("安装依赖包...")
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "安装requirements.txt中的依赖",
    )


def create_env_file():
    """创建.env文件模板"""
    env_file = Path(".env")

    if env_file.exists():
        print("✅ .env文件已存在")
        return True

    print("创建.env文件模板...")

    env_template = """# 环境配置
ENVIRONMENT=development
DEBUG=true

# AI模型配置（必需）
MODEL_APIKEY=your_api_key_here
MODEL_NAME=gemini-2.5-flash
MODEL_URL=https://jccbmkhayojw.ap-southeast-1.clawcloudrun.com/v1
MODEL_TOP_P=0.5
MODEL_SEED=42
MODEL_TEMPERATURE=0.0
MAX_CONTEXT_LENGTH=65536
MAX_OUTPUT_TOKENS=8192

# 敏感词检测API配置
SENSITIVE_WORD_API_URL=http://************:8087

# 文件处理配置
MAX_FILE_SIZE=314572800  # 300MB
ALLOWED_EXTENSIONS=[".docx", ".pdf"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs

# HTTP请求配置
REQUEST_TIMEOUT=300  # 5分钟
MAX_RETRIES=3
"""

    try:
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_template)
        print("✅ .env文件创建成功")
        print("⚠️  请编辑.env文件，设置正确的API密钥和配置")
        return True
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "temp",
        "tests/data",
    ]

    print("创建必要的目录...")
    for directory in directories:
        dir_path = Path(directory)
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 目录创建成功: {directory}")
        except Exception as e:
            print(f"❌ 目录创建失败 {directory}: {e}")
            return False

    return True


def test_imports():
    """测试关键模块导入"""
    print("测试关键模块导入...")

    test_modules = [
        ("fastapi", "FastAPI框架"),
        ("pydantic", "Pydantic数据验证"),
        ("pydantic_settings", "Pydantic设置"),
        ("requests", "HTTP客户端"),
        ("docx", "Word文档处理"),
        ("pdfplumber", "PDF处理"),
        ("filetype", "文件类型检测"),
        ("openai", "OpenAI客户端"),
        ("loguru", "日志库"),
    ]

    failed_imports = []

    for module_name, description in test_modules:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {e}")
            failed_imports.append(module_name)

    if failed_imports:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False

    print("✅ 所有关键模块导入成功")
    return True


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 环境设置")
    print("=" * 60)

    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_requirements),
        ("创建环境文件", create_env_file),
        ("创建目录结构", create_directories),
        ("测试模块导入", test_imports),
    ]

    failed_steps = []

    for step_name, step_func in steps:
        print(f"\n{'='*60}")
        print(f"步骤: {step_name}")
        print("=" * 60)

        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ 步骤失败: {e}")
            failed_steps.append(step_name)

    # 总结
    print(f"\n{'='*60}")
    print("环境设置总结")
    print("=" * 60)

    if failed_steps:
        print(f"❌ 失败的步骤: {', '.join(failed_steps)}")
        print("\n请解决上述问题后重新运行此脚本")
        return 1
    else:
        print("✅ 所有步骤完成成功！")
        print("\n下一步:")
        print("1. 编辑.env文件，设置正确的API密钥")
        print("2. 运行测试: python test_basic_functionality.py")
        print("3. 启动服务: python main.py")
        return 0


if __name__ == "__main__":
    exit(main())
