# -*- coding: utf-8 -*-
"""
异步AI模型服务
提供异步的AI模型调用功能，提升并发性能
"""

import asyncio
import json
import re
import time
from typing import Dict, List, Optional, Any
from openai import AsyncOpenAI

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.core.error_logger import error_logger
from app.models.schemas import ProjectInfo, CheckResultItem, ComplianceCheckResponse
from app.models.enums import QuestionType
from app.core.exceptions import AIModelError


class AsyncAIModelService:
    """异步AI模型服务"""

    def __init__(self):
        """初始化异步AI模型服务"""
        self.client = None
        self.model_config = {
            "model_name": settings.model_name,
            "api_key": settings.model_apikey,
            "base_url": settings.model_url,
            "top_p": settings.model_top_p,
            "seed": settings.model_seed,
            "temperature": settings.model_temperature,
            "max_context_length": settings.max_context_length,
            "max_output_tokens": settings.max_output_tokens,
        }
        self.service_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens_used": 0,
            "average_response_time": 0.0,
        }
        self._initialize_client()

    def _initialize_client(self):
        """初始化异步OpenAI客户端"""
        try:
            self.client = AsyncOpenAI(
                api_key=self.model_config["api_key"],
                base_url=self.model_config["base_url"],
                timeout=60.0,  # 60秒超时
            )
            log.info(
                f"异步AI模型客户端初始化成功 | 模型: {self.model_config['model_name']}"
            )
        except Exception as e:
            log.error(f"异步AI模型客户端初始化失败: {str(e)}")
            self.client = None

    @log_function_call
    async def check_compliance(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> ComplianceCheckResponse:
        """
        异步执行合规性检查

        Args:
            content: 文档内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 检查结果
        """
        if not self.client:
            raise AIModelError(
                "AI模型客户端未初始化",
                model_name=self.model_config["model_name"],
                error_code="CLIENT_NOT_INITIALIZED",
            )

        start_time = time.time()
        self.service_stats["total_requests"] += 1

        try:
            with TimingContext("异步AI合规性检查", request_id):
                # 构建系统提示词
                system_prompt = self._build_system_prompt(project_info)

                # 检查内容长度并截断
                truncated_content = self._truncate_content(content, request_id)

                # 构建用户消息
                user_message = f"""请对以下招标文件进行合规性检查：

{truncated_content}

请严格按照JSON格式返回检查结果。"""

                log.info(
                    f"开始异步AI模型调用 | ID: {request_id} | "
                    f"内容长度: {len(truncated_content)} 字符"
                )

                # 异步调用AI模型
                response = await self._call_model_async(
                    system_prompt, user_message, request_id
                )

                # 解析响应
                check_results = self._parse_ai_response(response, request_id)

                # 更新统计信息
                duration = time.time() - start_time
                self._update_success_stats(duration, response)

                log.info(
                    f"异步AI合规性检查完成 | ID: {request_id} | "
                    f"发现问题: {len(check_results)}个 | 耗时: {duration:.3f}秒"
                )

                return ComplianceCheckResponse(
                    sensitiveWordsArr=[],  # AI服务不处理敏感词
                    checkResultArr=check_results,
                )

        except Exception as e:
            duration = time.time() - start_time
            self._update_failure_stats(duration)

            if isinstance(e, AIModelError):
                raise
            else:
                raise AIModelError(
                    f"异步AI合规性检查失败: {str(e)}",
                    model_name=self.model_config["model_name"],
                    original_error=e,
                )

    async def _call_model_async(
        self, system_prompt: str, user_message: str, request_id: str = ""
    ) -> str:
        """
        异步调用AI模型

        Args:
            system_prompt: 系统提示词
            user_message: 用户消息
            request_id: 请求ID

        Returns:
            str: 模型响应
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message},
            ]

            # 异步调用模型
            completion = await self.client.chat.completions.create(
                model=self.model_config["model_name"],
                messages=messages,
                temperature=self.model_config["temperature"],
                top_p=self.model_config["top_p"],
                seed=self.model_config["seed"],
                max_tokens=self.model_config["max_output_tokens"],
            )

            response_content = completion.choices[0].message.content

            # 记录token使用情况
            if hasattr(completion, "usage") and completion.usage:
                self.service_stats["total_tokens_used"] += completion.usage.total_tokens
                log.debug(
                    f"Token使用情况 | ID: {request_id} | "
                    f"输入: {completion.usage.prompt_tokens} | "
                    f"输出: {completion.usage.completion_tokens} | "
                    f"总计: {completion.usage.total_tokens}"
                )

            return response_content

        except Exception as e:
            log.error(f"异步AI模型调用失败 | ID: {request_id} | 错误: {str(e)}")
            raise AIModelError(
                f"模型调用失败: {str(e)}",
                model_name=self.model_config["model_name"],
                original_error=e,
            )

    def _build_system_prompt(self, project_info: ProjectInfo) -> str:
        """构建系统提示词"""
        base_prompt = """你是一个专业的招标文件合规性检查助手。请对招标文件进行全面的合规性检查，重点关注以下方面：

1. 合规性：是否符合相关法律法规
2. 逻辑性：条款之间是否存在矛盾
3. 风险管理：是否存在潜在风险
4. 规范性：格式和表述是否规范
5. 公平性：是否存在歧视性条款
6. 可操作性：条款是否具体可执行

请严格按照以下JSON格式返回检查结果：
{
  "checkResultArr": [
    {
      "quesType": "问题类型（合规性/逻辑性/风险管理/规范性/公平性/可操作性）",
      "quesDesc": "具体问题描述",
      "originalArr": ["相关原文片段1", "相关原文片段2"],
      "point": "质量控制要点",
      "advice": "处理建议"
    }
  ]
}"""

        # 根据项目信息调整提示词
        project_specific = f"""

项目信息：
- 采购项目类型：{project_info.procurement_project_type.value}
- 项目类别：{project_info.project_category.value}
- 招标采购方式：{project_info.bidding_procurement_method.value}

请根据以上项目信息进行针对性的检查。"""

        return base_prompt + project_specific

    def _truncate_content(self, content: str, request_id: str = "") -> str:
        """截断内容以适应模型上下文限制"""
        max_length = (
            self.model_config["max_context_length"] - 2000
        )  # 预留系统提示词空间

        if len(content) <= max_length:
            return content

        truncated = content[:max_length]
        log.warning(
            f"内容被截断 | ID: {request_id} | "
            f"原长度: {len(content)} | 截断后: {len(truncated)}"
        )

        return truncated + "\n\n[内容因长度限制被截断]"

    def _parse_ai_response(
        self, response: str, request_id: str = ""
    ) -> List[CheckResultItem]:
        """解析AI响应"""
        try:
            # 清理响应内容
            cleaned_response = self._clean_json_response(response)

            # 解析JSON
            parsed_data = json.loads(cleaned_response)

            # 提取检查结果
            check_results = []
            if "checkResultArr" in parsed_data:
                for item in parsed_data["checkResultArr"]:
                    try:
                        check_result = CheckResultItem(
                            quesType=item.get("quesType", "未知"),
                            quesDesc=item.get("quesDesc", ""),
                            originalArr=item.get("originalArr", []),
                            point=item.get("point", ""),
                            advice=item.get("advice", ""),
                        )
                        check_results.append(check_result)
                    except Exception as e:
                        log.warning(
                            f"解析检查结果项失败 | ID: {request_id} | 错误: {str(e)}"
                        )
                        continue

            log.info(
                f"AI响应解析成功 | ID: {request_id} | 结果数量: {len(check_results)}"
            )
            return check_results

        except json.JSONDecodeError as e:
            log.error(f"AI响应JSON解析失败 | ID: {request_id} | 错误: {str(e)}")
            return []
        except Exception as e:
            log.error(f"AI响应解析异常 | ID: {request_id} | 错误: {str(e)}")
            return []

    def _clean_json_response(self, response: str) -> str:
        """清理JSON响应"""
        # 移除可能的markdown代码块标记
        response = re.sub(r"```json\s*", "", response)
        response = re.sub(r"```\s*$", "", response)

        # 移除前后空白
        response = response.strip()

        # 尝试提取JSON部分
        json_match = re.search(r"\{.*\}", response, re.DOTALL)
        if json_match:
            response = json_match.group()

        return response

    def _update_success_stats(self, duration: float, response: str):
        """更新成功统计"""
        self.service_stats["successful_requests"] += 1

        # 更新平均响应时间
        total_requests = self.service_stats["total_requests"]
        current_avg = self.service_stats["average_response_time"]
        self.service_stats["average_response_time"] = (
            current_avg * (total_requests - 1) + duration
        ) / total_requests

    def _update_failure_stats(self, duration: float):
        """更新失败统计"""
        self.service_stats["failed_requests"] += 1

    async def health_check(self) -> bool:
        """异步健康检查"""
        if not self.client:
            return False

        try:
            # 发送简单的测试请求
            test_response = await self.client.chat.completions.create(
                model=self.model_config["model_name"],
                messages=[{"role": "user", "content": "测试"}],
                max_tokens=10,
                timeout=10.0,
            )
            return True
        except Exception as e:
            log.warning(f"异步AI模型健康检查失败: {str(e)}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "service_type": "async_ai_model",
            "model_name": self.model_config["model_name"],
            "base_url": self.model_config["base_url"],
            "client_initialized": self.client is not None,
            "max_context_length": self.model_config["max_context_length"],
            "max_output_tokens": self.model_config["max_output_tokens"],
            "stats": self.service_stats,
        }

    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        success_rate = 0.0
        if self.service_stats["total_requests"] > 0:
            success_rate = (
                self.service_stats["successful_requests"]
                / self.service_stats["total_requests"]
            )

        return {
            **self.service_stats,
            "success_rate": success_rate,
            "average_tokens_per_request": (
                self.service_stats["total_tokens_used"]
                / max(self.service_stats["successful_requests"], 1)
            ),
        }


# 创建全局异步AI模型服务实例
async_ai_model_service = AsyncAIModelService()
