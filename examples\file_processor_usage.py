#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理器使用示例
"""

import asyncio
from app.services.file_processor_v2 import optimized_file_processor
from app.models.schemas import FileInfo
from app.models.enums import FileExtension, MimeType


async def example_process_docx():
    """处理DOCX文件示例"""
    print("=== DOCX文件处理示例 ===")

    # 创建文件信息
    file_info = FileInfo(
        filename="招标文件.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=1024 * 1024,  # 1MB
        url="http://example.com/bidding_document.docx",
    )

    try:
        # 检查处理能力
        capability = optimized_file_processor.validate_processing_capability(file_info)
        print(f"处理能力评估: {capability}")

        if capability["can_process"]:
            # 处理文件
            print("开始处理文件...")
            # result = optimized_file_processor.process_file(file_info, "example-001")
            # print(f"处理结果长度: {len(result)} 字符")
            # print(f"内容预览: {result[:200]}...")
            print("注意: 实际处理需要有效的文件URL")
        else:
            print("无法处理此文件")
            for warning in capability["warnings"]:
                print(f"警告: {warning}")

    except Exception as e:
        print(f"处理失败: {e}")


async def example_process_pdf():
    """处理PDF文件示例"""
    print("\n=== PDF文件处理示例 ===")

    # 创建文件信息
    file_info = FileInfo(
        filename="技术规范.pdf",
        extension=FileExtension.PDF,
        mime_type=MimeType.PDF,
        size=2 * 1024 * 1024,  # 2MB
        url="http://example.com/technical_specification.pdf",
    )

    try:
        # 检查处理能力
        capability = optimized_file_processor.validate_processing_capability(file_info)
        print(f"处理能力评估: {capability}")

        if capability["can_process"]:
            print(f"预计处理时间: {capability['estimated_time']:.1f} 秒")
            print(f"推荐处理方法: {capability['preferred_method']}")

            # 实际处理代码
            print("开始处理文件...")
            # result = optimized_file_processor.process_file(file_info, "example-002")
            print("注意: 实际处理需要有效的文件URL")
        else:
            print("无法处理此文件")

    except Exception as e:
        print(f"处理失败: {e}")


def example_get_stats():
    """获取处理器统计信息示例"""
    print("\n=== 处理器统计信息 ===")

    stats = optimized_file_processor.get_processing_stats()
    print(f"MarkItDown可用: {stats['markitdown_available']}")
    print(f"支持的格式: {stats['supported_formats']}")
    print(f"最大文件大小: {stats['max_file_size_mb']:.1f} MB")
    print(f"请求超时时间: {stats['request_timeout']} 秒")
    print(f"最大重试次数: {stats['max_retries']}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")

    from app.services.file_processor_v2 import (
        FileFormatError,
        FileDownloadError,
        FileProcessingError,
    )

    # 创建无效文件信息
    invalid_file_info = FileInfo(
        filename="invalid.txt",
        extension=".txt",  # 不支持的格式
        mime_type="text/plain",
        size=1024,
        url="http://example.com/invalid.txt",
    )

    try:
        optimized_file_processor.validate_file_format(invalid_file_info)
    except FileFormatError as e:
        print(f"格式错误: {e}")
        print(f"错误类型: {e.error_type}")
        print(f"文件名: {e.filename}")
        print(f"期望格式: {e.expected_format}")
        print(f"实际格式: {e.actual_format}")

    # 模拟下载错误
    try:
        # 这会导致下载错误（无效URL）
        # optimized_file_processor.download_file("invalid-url")
        print("下载错误示例（已跳过实际调用）")
    except FileDownloadError as e:
        print(f"下载错误: {e}")
        print(f"错误类型: {e.error_type}")
        print(f"URL: {e.url}")
        print(f"状态码: {e.status_code}")


async def main():
    """主函数"""
    print("文件处理器使用示例")
    print("=" * 50)

    # 获取统计信息
    example_get_stats()

    # 处理示例
    await example_process_docx()
    await example_process_pdf()

    # 错误处理示例
    example_error_handling()

    print("\n示例完成!")


if __name__ == "__main__":
    asyncio.run(main())
