#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化合规性检查接口测试
"""

import pytest
import requests
import json
from typing import Dict, Any


class TestSimpleComplianceCheck:
    """简化合规性检查接口测试类"""

    BASE_URL = "http://localhost:8088"

    def test_simple_compliance_check_success(self):
        """测试简化合规性检查成功案例"""

        # 测试数据
        test_data = {
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
            "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQSVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk",
        }

        try:
            # 发送请求
            response = requests.post(
                f"{self.BASE_URL}/api/v1/check-compliance-simple",
                json=test_data,
                timeout=300,
            )

            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {response.headers}")

            if response.status_code != 200:
                print(f"错误响应内容: {response.text}")

            # 验证响应
            assert (
                response.status_code == 200
            ), f"期望状态码200，实际: {response.status_code}"

            result = response.json()
            print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            # 验证响应结构
            assert "sensitiveWordsArr" in result, "响应中缺少sensitiveWordsArr字段"
            assert "checkResultArr" in result, "响应中缺少checkResultArr字段"
            assert isinstance(
                result["sensitiveWordsArr"], list
            ), "sensitiveWordsArr应该是列表"
            assert isinstance(
                result["checkResultArr"], list
            ), "checkResultArr应该是列表"

            print("✅ 简化合规性检查测试通过")
            return True

        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            return False

    def test_invalid_parameters(self):
        """测试无效参数"""

        # 测试无效的采购项目类型
        invalid_data = {
            "procurement_project_type": "无效类型",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
            "file_url": "http://example.com/test.docx",
        }

        try:
            response = requests.post(
                f"{self.BASE_URL}/api/v1/check-compliance-simple",
                json=invalid_data,
                timeout=30,
            )

            print(f"无效参数测试 - 状态码: {response.status_code}")
            print(f"无效参数测试 - 响应: {response.text}")

            # 应该返回400错误
            assert (
                response.status_code == 422
            ), f"期望状态码422，实际: {response.status_code}"

            print("✅ 无效参数测试通过")
            return True

        except Exception as e:
            print(f"❌ 无效参数测试异常: {str(e)}")
            return False

    def test_file_info_inference(self):
        """测试文件信息推断功能"""

        # 导入文件信息推断工具
        try:
            import sys
            import os

            # 添加项目根目录到Python路径
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            from app.utils.file_info_utils import infer_file_info_from_url

            test_url = "http://example.com/招标文件.docx"

            file_info = infer_file_info_from_url(test_url)

            print(f"推断的文件信息:")
            print(f"  文件名: {file_info.filename}")
            print(f"  扩展名: {file_info.extension}")
            print(f"  MIME类型: {file_info.mime_type}")
            print(f"  文件大小: {file_info.size}")
            print(f"  URL: {file_info.url}")

            # 验证推断结果
            assert file_info.filename is not None, "文件名不能为空"
            assert file_info.extension is not None, "扩展名不能为空"
            assert file_info.mime_type is not None, "MIME类型不能为空"
            assert file_info.size > 0, "文件大小必须大于0"

            print("✅ 文件信息推断测试通过")
            return True

        except Exception as e:
            print(f"❌ 文件信息推断测试异常: {str(e)}")
            return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("开始运行简化合规性检查接口测试")
    print("=" * 60)

    test_instance = TestSimpleComplianceCheck()

    tests = [
        ("文件信息推断测试", test_instance.test_file_info_inference),
        ("无效参数测试", test_instance.test_invalid_parameters),
        ("简化合规性检查成功测试", test_instance.test_simple_compliance_check_success),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 40)

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n总计: {len(results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    print(f"成功率: {passed/len(results)*100:.1f}%")

    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
