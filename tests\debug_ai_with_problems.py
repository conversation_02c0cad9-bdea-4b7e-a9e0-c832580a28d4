#!/usr/bin/env python3
"""
使用包含明显问题的文档测试AI模型响应
"""

import sys
import os
import json
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_model_service import AIModelService
from app.models.schemas import ProjectInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
)

# 设置详细的日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
)


def test_ai_with_problematic_content():
    """使用包含明显问题的文档测试AI模型"""
    print("开始测试包含问题的招标文档...")

    try:
        # 创建AI模型服务实例
        ai_service = AIModelService()

        # 创建测试项目信息
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

        # 包含明显问题的测试内容
        problematic_content = """
        第一章 招标公告
        
        项目名称：办公设备采购项目
        项目编号：ABC-2025-001
        招标人：某政府机关
        
        第二章 投标人须知前附表
        
        1. 投标保证金：项目预算的5%（明显超过2%的规定）
        2. 投标有效期：30天
        3. 开标时间：2025年8月15日
        4. 文件获取期：3个工作日（少于5个工作日的要求）
        
        第三章 投标人资格要求
        
        1. 投标人必须是本地企业（涉嫌地域歧视）
        2. 注册资本不少于1000万元
        3. 近三年无违法记录
        
        第四章 技术规格
        
        1. 品牌要求：必须是某某品牌（涉嫌品牌歧视）
        2. 型号：具体型号XXX-123
        3. 配置要求不明确
        
        第五章 评标办法
        
        1. 评标委员会人数：3人（少于5人的要求）
        2. 评分标准：技术分60分，商务分50分（总分110分，超过100分）
        3. 价格分计算方法不明确
        
        第六章 合同条款
        
        1. 履约保证金：合同金额的3%（超过2.5%的规定）
        2. 质保期：无明确规定
        3. 验收标准：不明确
        
        第七章 其他
        
        1. 答疑时间：开标前1天（少于3天的要求）
        2. 答疑邮箱：未提供
        3. 最高投标限价：未设置
        """

        print(f"测试内容长度: {len(problematic_content)} 字符")
        print("包含的明显问题：")
        print("- 投标保证金5%（超过2%规定）")
        print("- 文件获取期3天（少于5天要求）")
        print("- 地域歧视条款")
        print("- 品牌歧视条款")
        print("- 评标委员会3人（少于5人要求）")
        print("- 评分总分110分（超过100分）")
        print("- 履约保证金3%（超过2.5%规定）")
        print("- 答疑时间1天（少于3天要求）")
        print("- 未设置最高投标限价")

        # 测试完整的合规性检查
        print(f"\n=== 开始AI合规性检查 ===")
        try:
            result = ai_service.check_compliance(
                problematic_content, project_info, "debug-problematic"
            )
            print(f"✅ 合规性检查完成!")
            print(f"发现问题数量: {len(result.checkResultArr)}")

            if len(result.checkResultArr) == 0:
                print("❌ 警告：AI模型未发现任何问题，这可能表明存在问题！")
            else:
                print("\n发现的问题：")
                for i, item in enumerate(result.checkResultArr, 1):
                    print(f"\n问题 {i}:")
                    print(f"  类型: {item.quesType}")
                    print(f"  描述: {item.quesDesc}")
                    print(f"  要点: {item.point}")
                    print(f"  建议: {item.advice}")
                    if item.originalArr:
                        print(f"  原文: {item.originalArr}")

        except Exception as e:
            print(f"❌ 合规性检查失败: {str(e)}")
            import traceback

            traceback.print_exc()

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test_ai_with_problematic_content()
