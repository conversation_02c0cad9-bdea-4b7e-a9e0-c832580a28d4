# -*- coding: utf-8 -*-
"""
文件信息推断工具
"""

import os
import re
import requests
from urllib.parse import urlparse, unquote
from typing import Optional, Tuple

from app.models.enums import FileExtension, MimeType
from app.models.schemas import FileInfo
from app.core.logger import log


class FileInfoInferenceError(Exception):
    """文件信息推断异常"""

    pass


def extract_filename_from_url(url: str) -> str:
    """
    从URL中提取文件名

    Args:
        url: 文件URL

    Returns:
        str: 文件名
    """
    try:
        # 解析URL
        parsed_url = urlparse(url)

        # 从路径中提取文件名
        path = unquote(parsed_url.path)
        filename = os.path.basename(path)

        # 如果路径中没有文件名，尝试从查询参数中提取
        if not filename or "." not in filename:
            # 检查是否有常见的文件名参数
            query_params = parsed_url.query
            if query_params:
                # 尝试匹配文件名模式
                filename_patterns = [
                    r"filename[^=]*=([^&]+)",
                    r"file[^=]*=([^&]+)",
                    r"name[^=]*=([^&]+)",
                ]

                for pattern in filename_patterns:
                    match = re.search(pattern, query_params, re.IGNORECASE)
                    if match:
                        potential_filename = unquote(match.group(1))
                        if "." in potential_filename:
                            filename = potential_filename
                            break

        # 如果仍然没有找到合适的文件名，使用默认名称
        if not filename or "." not in filename:
            # 尝试从URL路径的最后部分推断
            path_parts = [part for part in path.split("/") if part]
            if path_parts:
                last_part = path_parts[-1]
                if "." in last_part:
                    filename = last_part
                else:
                    filename = "document.docx"  # 默认文件名
            else:
                filename = "document.docx"  # 默认文件名

        # 清理文件名中的特殊字符
        filename = re.sub(r'[<>:"/\\|?*]', "_", filename)

        return filename

    except Exception as e:
        log.warning(f"从URL提取文件名失败: {str(e)}")
        return "document.docx"  # 返回默认文件名


def infer_file_extension_and_mime_type(filename: str) -> Tuple[FileExtension, MimeType]:
    """
    从文件名推断文件扩展名和MIME类型

    Args:
        filename: 文件名

    Returns:
        Tuple[FileExtension, MimeType]: 文件扩展名和MIME类型
    """
    # 获取文件扩展名
    _, ext = os.path.splitext(filename.lower())

    # 映射扩展名到枚举值
    extension_mapping = {
        ".docx": (FileExtension.DOCX, MimeType.DOCX),
        ".pdf": (FileExtension.PDF, MimeType.PDF),
    }

    if ext in extension_mapping:
        return extension_mapping[ext]
    else:
        # 默认为docx格式
        log.warning(f"未识别的文件扩展名: {ext}，默认使用docx格式")
        return FileExtension.DOCX, MimeType.DOCX


def get_file_size_from_url(url: str, timeout: int = 30) -> Optional[int]:
    """
    通过HEAD请求获取文件大小

    Args:
        url: 文件URL
        timeout: 请求超时时间

    Returns:
        Optional[int]: 文件大小（字节），如果无法获取则返回None
    """
    try:
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        response.raise_for_status()

        content_length = response.headers.get("content-length")
        if content_length:
            size = int(content_length)
            # 确保返回的大小大于0
            return size if size > 0 else None
        else:
            log.warning(f"无法从HEAD请求获取文件大小: {url}")
            return None

    except Exception as e:
        log.warning(f"获取文件大小失败: {str(e)}")
        return None


def infer_file_info_from_url(url: str) -> FileInfo:
    """
    从URL推断完整的文件信息

    Args:
        url: 文件URL

    Returns:
        FileInfo: 推断的文件信息

    Raises:
        FileInfoInferenceError: 推断失败时抛出异常
    """
    try:
        log.info(f"开始从URL推断文件信息: {url}")

        # 1. 提取文件名
        filename = extract_filename_from_url(url)
        log.info(f"推断的文件名: {filename}")

        # 2. 推断扩展名和MIME类型
        extension, mime_type = infer_file_extension_and_mime_type(filename)
        log.info(f"推断的扩展名: {extension.value}, MIME类型: {mime_type.value}")

        # 3. 获取文件大小
        file_size = get_file_size_from_url(url)
        if file_size is None or file_size <= 0:
            # 如果无法获取文件大小或大小为0，使用默认值
            file_size = 1024 * 1024  # 1MB 默认大小
            log.warning(f"无法获取有效文件大小，使用默认值: {file_size} 字节")
        else:
            log.info(f"获取的文件大小: {file_size} 字节")

        # 4. 创建FileInfo对象
        file_info = FileInfo(
            filename=filename,
            extension=extension,
            mime_type=mime_type,
            size=file_size,
            url=url,
        )

        log.info(f"文件信息推断完成: {filename}")
        return file_info

    except Exception as e:
        error_msg = f"从URL推断文件信息失败: {str(e)}"
        log.error(error_msg)
        raise FileInfoInferenceError(error_msg)


def validate_inferred_file_info(file_info: FileInfo) -> bool:
    """
    验证推断的文件信息是否合理

    Args:
        file_info: 推断的文件信息

    Returns:
        bool: 验证是否通过
    """
    try:
        # 检查文件名是否合理
        if not file_info.filename or len(file_info.filename.strip()) == 0:
            log.warning("文件名为空")
            return False

        # 检查扩展名是否支持
        if file_info.extension not in [FileExtension.DOCX, FileExtension.PDF]:
            log.warning(f"不支持的文件扩展名: {file_info.extension}")
            return False

        # 检查MIME类型是否支持
        if file_info.mime_type not in [MimeType.DOCX, MimeType.PDF]:
            log.warning(f"不支持的MIME类型: {file_info.mime_type}")
            return False

        # 检查文件大小是否合理
        if file_info.size <= 0:
            log.warning(f"文件大小不合理: {file_info.size}")
            return False

        # 检查URL是否有效
        url_str = str(file_info.url)
        if not url_str.startswith(("http://", "https://")):
            log.warning(f"URL格式不正确: {file_info.url}")
            return False

        return True

    except Exception as e:
        log.error(f"验证文件信息时出错: {str(e)}")
        return False
