#!/usr/bin/env python3
"""
诊断AI模型问题的综合脚本
"""

import sys
import os
import json
import logging
import time
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(message)s",
)


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")

    # 检查.env文件
    env_file = ".env"
    if os.path.exists(env_file):
        print("✅ .env文件存在")
        try:
            with open(env_file, "r", encoding="utf-8") as f:
                env_content = f.read()

            # 检查关键配置
            if "OPENAI_API_KEY" in env_content:
                print("✅ OPENAI_API_KEY 已配置")
            else:
                print("❌ OPENAI_API_KEY 未配置")

            if "OPENAI_BASE_URL" in env_content:
                print("✅ OPENAI_BASE_URL 已配置")
            else:
                print("❌ OPENAI_BASE_URL 未配置")

        except Exception as e:
            print(f"❌ 读取.env文件失败: {e}")
    else:
        print("❌ .env文件不存在")

    print()


def test_ai_model_directly():
    """直接测试AI模型"""
    print("=== 直接测试AI模型 ===")

    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        # 创建AI模型服务实例
        ai_service = AIModelService()

        # 简单测试消息
        test_messages = [
            {"role": "system", "content": "你是一个测试助手。请简单回复'测试成功'。"},
            {"role": "user", "content": "请回复'测试成功'"},
        ]

        print("发送简单测试消息...")
        start_time = time.time()

        try:
            response = ai_service.call_model(test_messages, "simple-test")
            end_time = time.time()

            print(f"✅ AI模型响应成功")
            print(f"响应时间: {end_time - start_time:.2f}秒")
            print(f"响应长度: {len(response)}字符")
            print(f"响应内容: '{response}'")

            if end_time - start_time > 30:
                print("⚠️  响应时间过长，可能存在网络或模型问题")

        except Exception as e:
            print(f"❌ AI模型调用失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 无法初始化AI模型服务: {e}")
        return False

    print()
    return True


def test_compliance_check():
    """测试合规性检查功能"""
    print("=== 测试合规性检查功能 ===")

    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        ai_service = AIModelService()

        # 创建测试项目信息
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

        # 简单的测试内容
        test_content = """
        招标公告
        
        一、项目基本情况
        项目编号：TEST-2025-001
        项目名称：测试项目
        
        二、投标人资格要求
        1. 具有独立承担民事责任的能力
        2. 具有良好的商业信誉和健全的财务会计制度
        
        三、获取招标文件
        时间：2025年1月1日至2025年1月10日
        地点：采购代理机构
        """

        print("测试内容长度:", len(test_content))
        print("开始合规性检查...")

        start_time = time.time()

        try:
            result = ai_service.check_compliance(
                test_content, project_info, "compliance-test"
            )
            end_time = time.time()

            print(f"✅ 合规性检查成功")
            print(f"检查时间: {end_time - start_time:.2f}秒")
            print(f"发现问题数量: {len(result.checkResultArr)}")

            if len(result.checkResultArr) > 0:
                print("发现的问题:")
                for i, item in enumerate(result.checkResultArr[:3]):  # 只显示前3个
                    print(f"  {i+1}. {item.quesType}: {item.quesDesc[:100]}...")
            else:
                print("⚠️  未发现任何问题，这可能不正常")

            if end_time - start_time > 60:
                print("⚠️  检查时间过长，可能存在性能问题")

        except Exception as e:
            print(f"❌ 合规性检查失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 无法执行合规性检查: {e}")
        return False

    print()
    return True


def test_api_endpoint():
    """测试API端点"""
    print("=== 测试API端点 ===")

    # 测试健康检查
    try:
        response = requests.get("http://localhost:8088/health", timeout=10)
        if response.status_code == 200:
            print("✅ API服务健康检查通过")
        else:
            print(f"❌ API服务健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False

    # 测试简单的合规性检查
    test_data = {
        "procurement_project_type": "货物类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "data:text/plain;base64,6L+Z5piv5LiA5Liq5rWL6K+V5paH5Lu2",  # "这是一个测试文件"的base64编码
    }

    try:
        print("发送测试请求到API...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:8088/api/v1/check-compliance-simple",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=120,
        )

        end_time = time.time()

        print(f"API响应状态码: {response.status_code}")
        print(f"API响应时间: {end_time - start_time:.2f}秒")

        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功")
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")

            if end_time - start_time > 60:
                print("⚠️  API响应时间过长")
        else:
            print(f"❌ API调用失败: {response.text}")

    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

    print()
    return True


def analyze_logs():
    """分析日志文件"""
    print("=== 分析日志文件 ===")

    log_dir = "logs"
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return

    # 查找最新的日志文件
    log_files = []
    for file in os.listdir(log_dir):
        if file.endswith(".log"):
            file_path = os.path.join(log_dir, file)
            log_files.append((file_path, os.path.getmtime(file_path)))

    if not log_files:
        print("❌ 未找到日志文件")
        return

    # 按修改时间排序，获取最新的日志文件
    latest_log = sorted(log_files, key=lambda x: x[1], reverse=True)[0][0]
    print(f"分析最新日志文件: {latest_log}")

    try:
        with open(latest_log, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # 统计关键信息
        error_count = 0
        warning_count = 0
        ai_calls = 0
        timeout_issues = 0

        for line in lines[-1000:]:  # 只分析最后1000行
            if "ERROR" in line:
                error_count += 1
            elif "WARNING" in line:
                warning_count += 1
            elif "AI模型调用成功" in line:
                ai_calls += 1
            elif "超时" in line or "timeout" in line.lower():
                timeout_issues += 1

        print(f"最近1000行日志统计:")
        print(f"  错误数量: {error_count}")
        print(f"  警告数量: {warning_count}")
        print(f"  AI调用成功次数: {ai_calls}")
        print(f"  超时问题: {timeout_issues}")

        if error_count > 10:
            print("⚠️  错误数量较多，建议检查具体错误信息")
        if timeout_issues > 0:
            print("⚠️  发现超时问题，可能影响性能")

    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

    print()


def main():
    """主函数"""
    print("🔍 开始诊断AI模型问题...")
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)

    # 执行各项检查
    checks = [
        ("环境配置", check_environment),
        ("AI模型直接测试", test_ai_model_directly),
        ("合规性检查功能", test_compliance_check),
        ("API端点测试", test_api_endpoint),
        ("日志分析", analyze_logs),
    ]

    results = {}

    for check_name, check_func in checks:
        print(f"🔍 执行检查: {check_name}")
        try:
            result = check_func()
            results[check_name] = result if result is not None else True
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            results[check_name] = False
        print()

    # 总结报告
    print("=" * 50)
    print("📊 诊断总结报告")
    print("=" * 50)

    passed = sum(1 for r in results.values() if r)
    total = len(results)

    print(f"总体状态: {passed}/{total} 项检查通过")

    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")

    if passed == total:
        print("\n🎉 所有检查都通过了！系统运行正常。")
    else:
        print(f"\n⚠️  发现 {total - passed} 个问题，建议进一步排查。")

        print("\n🔧 建议的解决方案:")
        if not results.get("环境配置", True):
            print("  1. 检查.env文件中的API密钥和基础URL配置")
        if not results.get("AI模型直接测试", True):
            print("  2. 检查AI模型服务配置和网络连接")
        if not results.get("合规性检查功能", True):
            print("  3. 检查合规性检查逻辑和提示词配置")
        if not results.get("API端点测试", True):
            print("  4. 检查API服务是否正常运行")


if __name__ == "__main__":
    main()
