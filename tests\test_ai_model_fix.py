#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI模型服务空响应处理修复
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_model_service import AIModelService
from app.models.schemas import ProjectInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
)


def test_clean_json_data():
    """测试clean_json_data方法对空响应的处理"""
    print("测试clean_json_data方法...")

    ai_service = AIModelService()

    # 测试空字符串
    result = ai_service.clean_json_data("")
    print(f"空字符串处理结果: {result}")
    assert result == '{"checkResultArr": []}'

    # 测试只有空白字符
    result = ai_service.clean_json_data("   \n\t  ")
    print(f"空白字符处理结果: {result}")
    assert result == '{"checkResultArr": []}'

    # 测试无效JSON
    result = ai_service.clean_json_data("这不是JSON")
    print(f"无效JSON处理结果: {result}")
    assert result == '{"checkResultArr": []}'

    # 测试有效JSON
    valid_json = '{"checkResultArr": [{"quesType": "合规性", "quesDesc": "测试"}]}'
    result = ai_service.clean_json_data(valid_json)
    print(f"有效JSON处理结果: {result}")
    assert "checkResultArr" in result

    print("✅ clean_json_data方法测试通过")


def test_empty_response_handling():
    """测试空响应处理的完整流程"""
    print("\n测试空响应处理完整流程...")

    # 创建项目信息
    project_info = ProjectInfo(
        procurement_project_type=ProcurementProjectType.SERVICE,
        project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
        bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
    )

    # 注意：这个测试不会实际调用AI模型，只测试JSON处理逻辑
    print("✅ 项目信息创建成功")
    print("✅ 空响应处理流程测试准备完成")


if __name__ == "__main__":
    print("AI模型服务空响应处理修复测试")
    print("=" * 50)

    try:
        test_clean_json_data()
        test_empty_response_handling()

        print("\n🎉 所有测试通过！AI模型空响应处理修复成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        sys.exit(1)
