# 设计文档

## 概述

本设计解决合规性检查服务流水线中的关键bug，这些bug导致空结果、数据转换失败和性能问题。解决方案专注于改进所有服务组件的错误处理、数据转换、结果聚合和性能监控。

## 架构

合规性检查服务遵循流水线架构，包含以下阶段：
1. **服务前置条件验证**
2. **文件处理阶段**
3. **AI合规性检查阶段**
4. **敏感词检测阶段**
5. **结果聚合阶段**

### 当前问题分析

基于日志分析，已识别出以下关键问题：

1. **AI模型服务问题**：
   - 空响应处理（46.256秒返回0字符）
   - 由于内容为空导致的JSON解析失败
   - 缺少失败调用的重试机制

2. **敏感词服务问题**：
   - `SensitiveWordResult`对象的数据转换失败
   - 缺少`get`属性访问导致转换错误
   - 转换过程中结果丢失

3. **结果聚合问题**：
   - 检测到的敏感词未出现在最终结果中
   - 结果处理流水线中的数据丢失

4. **性能问题**：
   - 处理时间过长（46+秒）
   - 长时间运行操作缺少超时处理

## 组件和接口

### 1. 增强的AI模型服务

**当前问题**：
- `clean_json_data()`方法在空响应时失败
- 模型调用缺少超时处理
- 失败请求缺少重试逻辑

**设计变更**：
```python
class EnhancedAIModelService:
    def call_model_with_retry(self, messages, request_id, max_retries=3):
        """带重试逻辑和超时处理的增强模型调用"""
        
    def handle_empty_response(self, response, request_id):
        """处理空或无效的AI模型响应"""
        
    def validate_json_response(self, response_text, request_id):
        """验证和清理JSON响应，具有更好的错误处理"""
```

### 2. 修复的敏感词服务

**当前问题**：
- `_convert_to_target_format()`方法假设对象具有字典式访问
- 转换失败导致数据丢失
- 转换错误缺少备用方案

**设计变更**：
```python
class FixedSensitiveWordService:
    def _convert_to_target_format_fixed(self, results):
        """修复的转换方法，具有正确的对象属性访问"""
        
    def _handle_conversion_error(self, result, error):
        """处理单个转换错误而不丢失数据"""
        
    def _extract_sensitive_word_data(self, result):
        """使用多种访问方法提取数据（字典/对象）"""
```

### 3. 改进的结果处理器

**当前问题**：
- 聚合过程中结果丢失
- 缺少聚合过程验证
- 部分失败缺少错误处理

**设计变更**：
```python
class ImprovedResultProcessor:
    def aggregate_results_with_validation(self, sensitive_words, check_results):
        """带验证和错误恢复的结果聚合"""
        
    def validate_aggregation_integrity(self, input_data, output_data):
        """验证聚合过程中没有数据丢失"""
        
    def create_partial_response(self, partial_data, errors):
        """当某些处理失败时创建部分数据响应"""
```

### 4. 性能监控器

**新组件**：
```python
class PerformanceMonitor:
    def monitor_pipeline_stage(self, stage_name, operation):
        """监控单个流水线阶段，具有超时检测"""
        
    def detect_performance_issues(self, timing_data):
        """检测和报告性能瓶颈"""
        
    def suggest_optimizations(self, performance_data):
        """基于性能模式建议优化"""
```

## 数据模型

### 增强的错误处理模型

```python
class ServiceError:
    error_type: str
    service_name: str
    original_error: Exception
    recovery_action: str
    timestamp: datetime

class ConversionError:
    source_data: Any
    target_format: str
    error_message: str
    fallback_used: bool

class AggregationResult:
    input_sensitive_words: int
    input_check_results: int
    output_sensitive_words: int
    output_check_results: int
    data_loss_detected: bool
    errors: List[str]
```

### 修复的数据转换

```python
class SensitiveWordConverter:
    @staticmethod
    def safe_extract_field(obj, field_name, default=None):
        """安全地从对象或字典中提取字段"""
        if hasattr(obj, field_name):
            return getattr(obj, field_name)
        elif hasattr(obj, 'get') and callable(getattr(obj, 'get')):
            return obj.get(field_name, default)
        elif isinstance(obj, dict):
            return obj.get(field_name, default)
        else:
            return default
```

## 错误处理

### 1. AI模型错误处理

```python
def handle_ai_model_errors(self, error, request_id):
    """
    使用适当的恢复策略处理AI模型错误：
    - 空响应：记录详细错误并返回结构化空结果
    - 超时：实现带指数退避的重试
    - JSON解析：尝试多种解析策略
    - 服务不可用：切换到备用模式
    """
```

### 2. 敏感词转换错误处理

```python
def handle_conversion_errors(self, results, request_id):
    """
    处理敏感词转换错误：
    - 尝试多种属性访问方法
    - 记录转换失败和原始数据
    - 继续处理其他结果
    - 返回带错误指示器的部分结果
    """
```

### 3. 结果聚合错误处理

```python
def handle_aggregation_errors(self, sensitive_words, check_results, request_id):
    """
    处理结果聚合错误：
    - 验证输入数据完整性
    - 跟踪处理过程中的数据丢失
    - 提供详细的错误报告
    - 尽可能返回部分结果
    """
```

## 测试策略

### 1. 单元测试

- **AI模型服务测试**：
  - 测试空响应处理
  - 测试各种格式错误输入的JSON解析
  - 测试不同失败场景的重试逻辑
  - 测试超时处理

- **敏感词服务测试**：
  - 测试不同对象类型的转换
  - 测试格式错误数据的错误处理
  - 测试部分转换场景
  - 测试备用机制

- **结果处理器测试**：
  - 测试各种输入组合的聚合
  - 测试数据完整性验证
  - 测试部分失败场景
  - 测试错误恢复机制

### 2. 集成测试

- **流水线测试**：
  - 测试各种失败场景下的完整流水线
  - 测试不同负载条件下的性能
  - 测试跨服务的备用机制
  - 测试数据流完整性

### 3. 性能测试

- **超时测试**：
  - 测试AI模型超时处理
  - 测试服务响应时间监控
  - 测试性能降级检测

- **负载测试**：
  - 测试负载下的流水线性能
  - 测试资源利用率监控
  - 测试可扩展性限制

### 4. 错误场景测试

- **服务失败测试**：
  - 测试AI模型返回空响应时的行为
  - 测试敏感词服务失败时的行为
  - 测试转换失败时的行为
  - 测试聚合失败时的行为

## 实施方法

### 阶段1：关键bug修复
1. 修复AI模型空响应处理
2. 修复敏感词转换问题
3. 修复结果聚合数据丢失
4. 添加全面的错误日志记录

### 阶段2：性能改进
1. 为所有服务调用添加超时处理
2. 实现重试机制
3. 添加性能监控
4. 优化流水线执行

### 阶段3：增强错误处理
1. 实现全面的备用机制
2. 添加详细的错误报告
3. 实现部分结果处理
4. 添加服务健康监控

### 阶段4：监控和警报
1. 添加性能指标收集
2. 实现关键错误警报
3. 添加服务监控仪表板
4. 实现自动恢复机制