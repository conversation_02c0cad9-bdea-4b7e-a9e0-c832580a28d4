#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试完整性验证功能
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.result_processor import ResultProcessor


def debug_integrity_verification():
    """调试完整性验证功能"""
    print("调试完整性验证功能...")

    processor = ResultProcessor()

    # 测试正常情况
    input_data = [1, 2, 3, 4, 5]
    output_data = [1, 2, 3, 4, 5]

    print("测试正常情况...")
    result = processor._verify_processing_integrity(
        "正常处理", input_data, output_data, "test-debug"
    )

    print(f"完整性验证结果: {result}")
    print(f"状态: {result['integrity_status']}")
    print(f"输入数量: {result['input_count']}")
    print(f"输出数量: {result['output_count']}")
    print(f"数据丢失: {result['data_loss_count']}")
    print(f"丢失百分比: {result['data_loss_percentage']:.1f}%")

    # 测试数据丢失情况
    print("\n测试数据丢失情况...")
    input_data = list(range(20))
    output_data = list(range(19))  # 丢失1个

    result = processor._verify_processing_integrity(
        "数据丢失处理", input_data, output_data, "test-loss"
    )

    print(f"完整性验证结果: {result}")
    print(f"状态: {result['integrity_status']}")
    print(f"输入数量: {result['input_count']}")
    print(f"输出数量: {result['output_count']}")
    print(f"数据丢失: {result['data_loss_count']}")
    print(f"丢失百分比: {result['data_loss_percentage']:.1f}%")
    print(f"警告: {result['warnings']}")


if __name__ == "__main__":
    debug_integrity_verification()
