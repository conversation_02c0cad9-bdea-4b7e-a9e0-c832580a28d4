# -*- coding: utf-8 -*-
"""
异步合规性检查服务
整合所有异步组件，提供高性能的并发处理能力
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.core.config import settings
from app.core.logger import (
    log,
    performance_logger,
    performance_monitor,
    TimingContext,
    log_function_call,
)
from app.core.exceptions import (
    BusinessLogicError,
    FileProcessingError,
    AIModelError,
    ExternalServiceError,
    FallbackManager,
)
from app.models.schemas import (
    ComplianceCheckRequest,
    ComplianceCheckResponse,
    ProjectInfo,
    SensitiveWordItem,
    CheckResultItem,
)

# 导入异步服务组件
from app.services.async_ai_model_service import async_ai_model_service
from app.services.async_sensitive_word_service import async_sensitive_word_service
from app.services.async_file_processor import async_file_processor
from app.services.result_processor import result_processor


class AsyncComplianceCheckPipeline:
    """异步合规性检查流水线"""

    def __init__(self):
        """初始化异步流水线"""
        self.fallback_manager = FallbackManager()
        self.pipeline_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "fallback_requests": 0,
            "concurrent_requests": 0,
            "max_concurrent_requests": 0,
            "average_processing_time": 0.0,
        }
        self._request_semaphore = asyncio.Semaphore(settings.max_concurrent_requests)

    @log_function_call
    async def validate_prerequisites(self, request_id: str = "") -> Dict[str, bool]:
        """
        异步验证所有服务的前置条件

        Args:
            request_id: 请求ID

        Returns:
            Dict[str, bool]: 各服务的健康状态
        """
        log.info(f"开始异步验证服务前置条件 | ID: {request_id}")

        # 并发检查所有服务健康状态
        health_checks = await asyncio.gather(
            self._check_file_processor_health(),
            self._check_ai_model_health(),
            self._check_sensitive_word_health(request_id),
            self._check_result_processor_health(),
            return_exceptions=True,
        )

        health_status = {
            "file_processor": (
                health_checks[0]
                if not isinstance(health_checks[0], Exception)
                else False
            ),
            "ai_model": (
                health_checks[1]
                if not isinstance(health_checks[1], Exception)
                else False
            ),
            "sensitive_word": (
                health_checks[2]
                if not isinstance(health_checks[2], Exception)
                else False
            ),
            "result_processor": (
                health_checks[3]
                if not isinstance(health_checks[3], Exception)
                else False
            ),
        }

        log.info(f"异步服务健康检查完成 | ID: {request_id} | 状态: {health_status}")
        return health_status

    async def _check_file_processor_health(self) -> bool:
        """检查文件处理器健康状态"""
        return True  # 文件处理器总是可用

    async def _check_ai_model_health(self) -> bool:
        """检查AI模型服务健康状态"""
        try:
            return await async_ai_model_service.health_check()
        except Exception:
            return False

    async def _check_sensitive_word_health(self, request_id: str = "") -> bool:
        """检查敏感词服务健康状态"""
        try:
            return await async_sensitive_word_service.check_health(request_id)
        except Exception:
            return False

    async def _check_result_processor_health(self) -> bool:
        """检查结果处理器健康状态"""
        return True  # 结果处理器总是可用

    @log_function_call
    async def process_file_stage(
        self, file_info, request_id: str = ""
    ) -> Optional[str]:
        """
        异步文件处理阶段

        Args:
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            Optional[str]: 处理后的文件内容，失败时返回None
        """
        start_time = asyncio.get_event_loop().time()
        try:
            with TimingContext("异步文件处理阶段", request_id):
                log.info(
                    f"开始异步文件处理阶段 | ID: {request_id} | 文件: {file_info.filename}"
                )

                # 异步处理文件
                content = await async_file_processor.process_file(file_info, request_id)

                log.info(
                    f"异步文件处理完成 | ID: {request_id} | 内容长度: {len(content)} 字符"
                )

                # 记录性能监控
                duration = asyncio.get_event_loop().time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "async_file_processing", duration, True
                )

                return content

        except FileProcessingError as e:
            # 记录失败的性能监控
            duration = asyncio.get_event_loop().time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "async_file_processing", duration, False
            )
            raise
        except Exception as e:
            # 记录失败的性能监控
            duration = asyncio.get_event_loop().time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "async_file_processing", duration, False
            )
            raise FileProcessingError(
                f"异步文件处理阶段失败: {str(e)}",
                filename=file_info.filename,
                original_error=e,
            )

    @log_function_call
    async def ai_compliance_check_stage(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> List[CheckResultItem]:
        """
        异步AI合规性检查阶段

        Args:
            content: 文件内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            List[CheckResultItem]: 检查结果列表
        """
        start_time = asyncio.get_event_loop().time()
        try:
            with TimingContext("异步AI合规性检查阶段", request_id):
                log.info(f"开始异步AI合规性检查 | ID: {request_id}")

                # 异步调用AI模型服务
                ai_result = await async_ai_model_service.check_compliance(
                    content, project_info, request_id
                )

                log.info(
                    f"异步AI合规性检查完成 | ID: {request_id} | "
                    f"发现问题: {len(ai_result.data.checkResultArr)}个"
                )

                # 记录成功的性能监控
                duration = asyncio.get_event_loop().time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "async_ai_model_call", duration, True
                )

                return ai_result.data.checkResultArr

        except AIModelError as e:
            # 记录失败的性能监控
            duration = asyncio.get_event_loop().time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "async_ai_model_call", duration, False
            )
            raise
        except Exception as e:
            # 记录失败的性能监控
            duration = asyncio.get_event_loop().time() - start_time
            performance_monitor.record_stage_performance(
                request_id, "async_ai_model_call", duration, False
            )
            raise AIModelError(
                f"异步AI合规性检查阶段失败: {str(e)}",
                model_name=settings.model_name,
                original_error=e,
            )

    @log_function_call
    async def sensitive_word_check_stage(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> List[SensitiveWordItem]:
        """
        异步敏感词检测阶段

        Args:
            content: 文件内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            List[SensitiveWordItem]: 敏感词列表
        """
        start_time = asyncio.get_event_loop().time()
        try:
            with TimingContext("异步敏感词检测阶段", request_id):
                log.info(f"开始异步敏感词检测 | ID: {request_id}")

                # 使用带降级机制的异步检测，设置60秒超时
                sensitive_words = (
                    await async_sensitive_word_service.detect_with_fallback(
                        content, project_info, request_id, timeout=60.0
                    )
                )

                log.info(
                    f"异步敏感词检测完成 | ID: {request_id} | "
                    f"发现敏感词: {len(sensitive_words)}个"
                )

                # 记录成功的性能监控
                duration = asyncio.get_event_loop().time() - start_time
                performance_monitor.record_stage_performance(
                    request_id, "async_sensitive_word", duration, True
                )

                return sensitive_words

        except ExternalServiceError as e:
            # 记录失败的性能监控
            duration = asyncio.get_event_loop().time() - start_time
