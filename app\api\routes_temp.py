# -*- coding: utf-8 -*-
"""
临时简化的API路由 - 用于调试超时问题
"""

from fastapi import APIRouter, Request
from typing import Dict, Any
import asyncio

from app.models.schemas import ComplianceCheckResponse
from app.core.logger import log

router_temp = APIRouter()


@router_temp.post("/check-compliance-simple")
async def check_compliance_simple(
    request: Request, request_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    简化的合规性检查接口 - 用于调试
    """
    request_id = getattr(request.state, "request_id", "")

    log.info(f"简化合规性检查开始 | ID: {request_id}")

    try:
        # 模拟处理时间
        await asyncio.sleep(1)

        # 返回模拟结果
        result = {
            "sensitiveWordsArr": [
                {"type": "测试类型", "content": "测试敏感词", "num": 1}
            ],
            "checkResultArr": [
                {
                    "type": "合规性",
                    "content": "这是一个测试问题",
                    "level": "轻微",
                    "suggestion": "这是一个测试建议",
                }
            ],
        }

        log.info(f"简化合规性检查完成 | ID: {request_id}")
        return result

    except Exception as e:
        log.error(f"简化合规性检查失败 | ID: {request_id} | 错误: {str(e)}")
        raise


@router_temp.get("/debug-info")
async def get_debug_info():
    """获取调试信息"""
    return {"message": "调试接口正常", "timestamp": "2025-08-06", "status": "ok"}
