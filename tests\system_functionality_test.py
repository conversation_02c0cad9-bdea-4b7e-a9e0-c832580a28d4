#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
测试招标文件合规性检查助手的各个组件功能
"""

import os
import sys
import traceback
from typing import Dict, Any, List
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.core.logger import log
from app.models.schemas import (
    ComplianceCheckRequest,
    FileInfo,
    ProjectInfo,
    SensitiveWordItem,
    CheckResultItem,
)
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


class SystemFunctionalityTester:
    """系统功能测试器"""

    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        self.passed_tests = 0
        self.total_tests = 0

    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results[test_name] = {
            "success": success,
            "message": message,
        }
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            print(f"✅ {test_name}: 通过")
        else:
            print(f"❌ {test_name}: 失败 - {message}")

    def test_configuration_loading(self) -> bool:
        """测试配置加载"""
        print("\n1. 测试配置加载")
        print("-" * 40)

        try:
            # 测试基本配置
            assert hasattr(settings, "model_name"), "缺少model_name配置"
            assert hasattr(settings, "max_file_size"), "缺少max_file_size配置"
            assert hasattr(settings, "request_timeout"), "缺少request_timeout配置"

            print(f"   模型名称: {settings.model_name}")
            print(f"   最大文件大小: {settings.max_file_size / 1024 / 1024:.0f}MB")
            print(f"   请求超时: {settings.request_timeout}秒")

            self.log_test_result("配置加载", True)
            return True

        except Exception as e:
            self.log_test_result("配置加载", False, str(e))
            return False

    def test_enum_definitions(self) -> bool:
        """测试枚举定义"""
        print("\n2. 测试枚举定义")
        print("-" * 40)

        try:
            # 测试采购项目类型枚举
            procurement_types = list(ProcurementProjectType)
            assert len(procurement_types) > 0, "采购项目类型枚举为空"
            print(f"   采购项目类型: {len(procurement_types)}个")

            # 测试项目类别枚举
            project_categories = list(ProjectCategory)
            assert len(project_categories) > 0, "项目类别枚举为空"
            print(f"   项目类别: {len(project_categories)}个")

            # 测试招标采购方式枚举
            bidding_methods = list(BiddingProcurementMethod)
            assert len(bidding_methods) > 0, "招标采购方式枚举为空"
            print(f"   招标采购方式: {len(bidding_methods)}个")

            # 测试文件扩展名枚举
            file_extensions = list(FileExtension)
            assert FileExtension.DOCX in file_extensions, "缺少DOCX扩展名"
            assert FileExtension.PDF in file_extensions, "缺少PDF扩展名"
            print(f"   支持的文件扩展名: {[ext.value for ext in file_extensions]}")

            self.log_test_result("枚举定义", True)
            return True

        except Exception as e:
            self.log_test_result("枚举定义", False, str(e))
            return False

    def test_data_models(self) -> bool:
        """测试数据模型"""
        print("\n3. 测试数据模型")
        print("-" * 40)

        try:
            # 测试FileInfo模型
            file_info = FileInfo(
                filename="test.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.DOCX,
                size=1024 * 1024,
                url="http://example.com/test.docx",
            )
            assert file_info.filename == "test.docx", "FileInfo模型创建失败"
            print(f"   FileInfo模型: ✅")

            # 测试ProjectInfo模型
            project_info = ProjectInfo(
                procurement_project_type=ProcurementProjectType.GOODS,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            )
            assert (
                project_info.procurement_project_type == ProcurementProjectType.GOODS
            ), "ProjectInfo模型创建失败"
            print(f"   ProjectInfo模型: ✅")

            # 测试ComplianceCheckRequest模型
            request = ComplianceCheckRequest(
                procurement_project_type=ProcurementProjectType.GOODS,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
                bidding_doc=file_info,
            )
            assert request.bidding_doc.filename == "test.docx", "请求模型创建失败"
            print(f"   ComplianceCheckRequest模型: ✅")

            # 测试get_project_info方法
            project_info_from_request = request.get_project_info()
            assert isinstance(
                project_info_from_request, ProjectInfo
            ), "项目信息提取失败"
            print(f"   项目信息提取: ✅")

            self.log_test_result("数据模型", True)
            return True

        except Exception as e:
            self.log_test_result("数据模型", False, str(e))
            return False

    def test_logger_functionality(self) -> bool:
        """测试日志功能"""
        print("\n4. 测试日志功能")
        print("-" * 40)

        try:
            # 测试基本日志记录
            log.info("测试信息日志")
            log.warning("测试警告日志")
            log.error("测试错误日志")
            print(f"   基本日志记录: ✅")

            # 测试结构化日志
            log.info(
                "测试结构化日志", extra={"request_id": "test-001", "user": "tester"}
            )
            print(f"   结构化日志: ✅")

            self.log_test_result("日志功能", True)
            return True

        except Exception as e:
            self.log_test_result("日志功能", False, str(e))
            return False

    def test_file_processor_initialization(self) -> bool:
        """测试文件处理器初始化"""
        print("\n5. 测试文件处理器初始化")
        print("-" * 40)

        try:
            from app.services.file_processor_v2 import optimized_file_processor

            # 测试处理器初始化
            assert optimized_file_processor is not None, "文件处理器未初始化"
            print(f"   文件处理器初始化: ✅")

            # 测试获取处理统计
            stats = optimized_file_processor.get_processing_stats()
            assert isinstance(stats, dict), "处理统计获取失败"
            assert "markitdown_available" in stats, "缺少MarkItDown可用性信息"
            assert "supported_formats" in stats, "缺少支持格式信息"
            print(f"   MarkItDown可用: {stats['markitdown_available']}")
            print(f"   支持格式: {stats['supported_formats']}")

            # 测试文件格式验证
            test_file_info = FileInfo(
                filename="test.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.DOCX,
                size=1024,
                url="http://example.com/test.docx",
            )

            capability = optimized_file_processor.validate_processing_capability(
                test_file_info
            )
            assert isinstance(capability, dict), "处理能力验证失败"
            assert "can_process" in capability, "缺少处理能力信息"
            print(f"   文件处理能力验证: ✅")

            self.log_test_result("文件处理器初始化", True)
            return True

        except Exception as e:
            self.log_test_result("文件处理器初始化", False, str(e))
            return False

    def test_ai_model_service_initialization(self) -> bool:
        """测试AI模型服务初始化"""
        print("\n6. 测试AI模型服务初始化")
        print("-" * 40)

        try:
            from app.services.ai_model_service import ai_model_service

            # 测试服务初始化
            assert ai_model_service is not None, "AI模型服务未初始化"
            print(f"   AI模型服务初始化: ✅")

            # 测试获取模型信息
            model_info = ai_model_service.get_model_info()
            assert isinstance(model_info, dict), "模型信息获取失败"
            assert "model_name" in model_info, "缺少模型名称信息"
            assert "client_initialized" in model_info, "缺少客户端初始化信息"
            print(f"   模型名称: {model_info['model_name']}")
            print(f"   客户端初始化: {model_info['client_initialized']}")

            self.log_test_result("AI模型服务初始化", True)
            return True

        except Exception as e:
            self.log_test_result("AI模型服务初始化", False, str(e))
            return False

    def test_sensitive_word_service_initialization(self) -> bool:
        """测试敏感词服务初始化"""
        print("\n7. 测试敏感词服务初始化")
        print("-" * 40)

        try:
            from app.services.sensitive_word_service import sensitive_word_service

            # 测试服务初始化
            assert sensitive_word_service is not None, "敏感词服务未初始化"
            print(f"   敏感词服务初始化: ✅")

            # 测试获取服务信息
            service_info = sensitive_word_service.get_service_info()
            assert isinstance(service_info, dict), "服务信息获取失败"
            assert "base_url" in service_info, "缺少基础URL信息"
            assert "timeout" in service_info, "缺少超时信息"
            print(f"   基础URL: {service_info['base_url']}")
            print(f"   超时时间: {service_info['timeout']}秒")

            # 测试健康检查（可能失败，因为外部服务可能不可用）
            try:
                health = sensitive_word_service.check_health("test-health")
                print(f"   健康状态: {'✅' if health else '❌'}")
            except Exception as health_error:
                print(f"   健康检查: ⚠️ 外部服务不可用 ({str(health_error)[:50]}...)")

            self.log_test_result("敏感词服务初始化", True)
            return True

        except Exception as e:
            self.log_test_result("敏感词服务初始化", False, str(e))
            return False

    def test_result_processor_initialization(self) -> bool:
        """测试结果处理器初始化"""
        print("\n8. 测试结果处理器初始化")
        print("-" * 40)

        try:
            from app.services.result_processor import result_processor

            # 测试处理器初始化
            assert result_processor is not None, "结果处理器未初始化"
            print(f"   结果处理器初始化: ✅")

            # 测试获取处理统计
            stats = result_processor.get_processing_stats()
            assert isinstance(stats, dict), "处理统计获取失败"
            print(f"   去重启用: {stats.get('deduplication_enabled', False)}")
            print(f"   排序启用: {stats.get('sorting_enabled', False)}")

            # 测试创建空响应
            empty_response = result_processor.create_empty_response("测试原因")
            assert empty_response is not None, "空响应创建失败"
            assert len(empty_response.sensitiveWordsArr) == 0, "空响应应该没有敏感词"
            assert len(empty_response.checkResultArr) == 0, "空响应应该没有检查结果"
            print(f"   空响应创建: ✅")

            self.log_test_result("结果处理器初始化", True)
            return True

        except Exception as e:
            self.log_test_result("结果处理器初始化", False, str(e))
            return False

    def test_compliance_service_initialization(self) -> bool:
        """测试合规性服务初始化"""
        print("\n9. 测试合规性服务初始化")
        print("-" * 40)

        try:
            from app.services.compliance_service import compliance_service

            # 测试服务初始化
            assert compliance_service is not None, "合规性服务未初始化"
            print(f"   合规性服务初始化: ✅")

            # 测试获取服务状态
            service_status = compliance_service.get_service_status()
            assert isinstance(service_status, dict), "服务状态获取失败"
            assert "service_info" in service_status, "缺少服务信息"
            assert "health_status" in service_status, "缺少健康状态"
            assert "pipeline_stats" in service_status, "缺少流水线统计"

            service_info = service_status["service_info"]
            print(f"   服务名称: {service_info['service_name']}")
            print(f"   版本: {service_info['version']}")
            print(f"   支持格式: {service_info['supported_formats']}")

            # 测试获取处理指标
            metrics = compliance_service.get_processing_metrics()
            assert isinstance(metrics, dict), "处理指标获取失败"
            assert "pipeline_metrics" in metrics, "缺少流水线指标"
            assert "component_info" in metrics, "缺少组件信息"
            print(f"   处理指标获取: ✅")

            self.log_test_result("合规性服务初始化", True)
            return True

        except Exception as e:
            self.log_test_result("合规性服务初始化", False, str(e))
            return False

    def test_exception_handling(self) -> bool:
        """测试异常处理"""
        print("\n10. 测试异常处理")
        print("-" * 40)

        try:
            from app.core.exceptions import (
                BusinessLogicError,
                FileProcessingError,
                AIModelError,
                ExternalServiceError,
                ValidationError,
                FallbackManager,
            )

            # 测试异常类创建
            business_error = BusinessLogicError("测试业务逻辑错误")
            assert str(business_error) == "测试业务逻辑错误", "业务逻辑异常创建失败"
            print(f"   业务逻辑异常: ✅")

            file_error = FileProcessingError("测试文件处理错误")
            assert str(file_error) == "测试文件处理错误", "文件处理异常创建失败"
            print(f"   文件处理异常: ✅")

            ai_error = AIModelError("测试AI模型错误")
            assert str(ai_error) == "测试AI模型错误", "AI模型异常创建失败"
            print(f"   AI模型异常: ✅")

            service_error = ExternalServiceError("测试外部服务错误")
            assert str(service_error) == "测试外部服务错误", "外部服务异常创建失败"
            print(f"   外部服务异常: ✅")

            validation_error = ValidationError("测试验证错误")
            assert str(validation_error) == "测试验证错误", "验证异常创建失败"
            print(f"   验证异常: ✅")

            # 测试降级管理器
            fallback_manager = FallbackManager()
            assert fallback_manager is not None, "降级管理器创建失败"
            print(f"   降级管理器: ✅")

            self.log_test_result("异常处理", True)
            return True

        except Exception as e:
            self.log_test_result("异常处理", False, str(e))
            return False

    def test_validators(self) -> bool:
        """测试验证器"""
        print("\n11. 测试验证器")
        print("-" * 40)

        try:
            from app.core.validators import ParameterValidator, get_enum_values

            # 测试枚举值获取
            enum_values = get_enum_values()
            assert isinstance(enum_values, dict), "枚举值获取失败"
            assert "ProcurementProjectType" in enum_values, "缺少采购项目类型枚举"
            assert "ProjectCategory" in enum_values, "缺少项目类别枚举"
            print(f"   枚举值获取: ✅")

            # 测试请求验证
            test_request_data = {
                "procurement_project_type": "货物类",  # 修正枚举值
                "project_category": "政府采购",
                "bidding_procurement_method": "公开招标",
                "bidding_doc": {
                    "filename": "test.docx",
                    "extension": ".docx",
                    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "size": 1024,
                    "url": "http://example.com/test.docx",
                },
            }

            validated_request = ParameterValidator.validate_compliance_request(
                test_request_data
            )
            assert validated_request is not None, "请求验证失败"
            print(f"   请求验证: ✅")

            self.log_test_result("验证器", True)
            return True

        except Exception as e:
            self.log_test_result("验证器", False, str(e))
            return False

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("系统功能测试开始")
        print("=" * 60)

        # 定义测试列表
        tests = [
            ("配置加载", self.test_configuration_loading),
            ("枚举定义", self.test_enum_definitions),
            ("数据模型", self.test_data_models),
            ("日志功能", self.test_logger_functionality),
            ("文件处理器初始化", self.test_file_processor_initialization),
            ("AI模型服务初始化", self.test_ai_model_service_initialization),
            ("敏感词服务初始化", self.test_sensitive_word_service_initialization),
            ("结果处理器初始化", self.test_result_processor_initialization),
            ("合规性服务初始化", self.test_compliance_service_initialization),
            ("异常处理", self.test_exception_handling),
            ("验证器", self.test_validators),
        ]

        # 运行测试
        for test_name, test_func in tests:
            try:
                test_func()
            except Exception as e:
                self.log_test_result(test_name, False, f"测试异常: {str(e)}")
                print(f"   异常详情: {traceback.format_exc()}")

        # 显示测试总结
        self.print_test_summary()

        return self.test_results

    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"总测试数: {self.total_tests}")
        print(f"通过数: {self.passed_tests}")
        print(f"失败数: {self.total_tests - self.passed_tests}")
        print(f"通过率: {self.passed_tests / max(self.total_tests, 1):.1%}")

        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result["success"] else "❌ 失败"
            message = f" - {result['message']}" if result["message"] else ""
            print(f"  {test_name}: {status}{message}")

        # 系统状态评估
        print("\n" + "=" * 60)
        print("系统状态评估")
        print("=" * 60)

        if self.passed_tests == self.total_tests:
            print("🎉 系统功能完全正常，可以进行生产部署！")
        elif self.passed_tests / self.total_tests >= 0.8:
            print("✅ 系统核心功能正常，建议修复失败的测试后部署。")
        elif self.passed_tests / self.total_tests >= 0.6:
            print("⚠️  系统部分功能存在问题，需要修复后才能部署。")
        else:
            print("❌ 系统存在严重问题，不建议部署。")


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 系统功能测试")
    print("=" * 60)
    print("测试系统各个组件的初始化和基本功能")
    print()

    # 创建测试器并运行测试
    tester = SystemFunctionalityTester()
    results = tester.run_all_tests()

    # 根据测试结果返回退出码
    all_passed = all(result["success"] for result in results.values())
    exit_code = 0 if all_passed else 1

    print(f"\n测试完成，退出码: {exit_code}")
    return exit_code


if __name__ == "__main__":
    exit(main())
