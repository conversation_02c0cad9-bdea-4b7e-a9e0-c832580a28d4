#!/usr/bin/env python3
"""
调试真实文件AI响应的脚本
"""

import sys
import os
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def debug_real_file_ai_response():
    """调试真实文件的AI响应"""
    print("开始调试真实文件的AI响应...")

    # 真实文件URL
    file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"

    try:
        # 1. 先下载文件内容
        print("1. 下载文件内容...")
        response = requests.get(file_url, timeout=30)
        if response.status_code != 200:
            print(f"❌ 文件下载失败: {response.status_code}")
            return False

        print(f"✅ 文件下载成功，大小: {len(response.content)} 字节")

        # 2. 处理文件内容
        print("2. 处理文件内容...")
        try:
            from app.services.file_processor_v2 import OptimizedFileProcessor
            from app.models.schemas import FileInfo
            from app.models.enums import FileExtension, MimeType

            processor = OptimizedFileProcessor()

            # 创建正确的FileInfo对象
            file_info = FileInfo(
                filename="debug_file.docx",
                extension=FileExtension.DOCX,
                mime_type=MimeType.DOCX,
                size=len(response.content),
                url=file_url,  # 使用原始URL
            )

            # 处理文件
            processed_content = processor.process_file(file_info, "debug-file")

            print(f"✅ 文件处理成功，内容长度: {len(processed_content)} 字符")
            print(f"内容前500字符: {processed_content[:500]}...")

        except Exception as e:
            print(f"❌ 文件处理失败: {e}")
            return False

        # 3. 直接调用AI模型
        print("3. 直接调用AI模型...")
        try:
            from app.services.ai_model_service import AIModelService
            from app.models.schemas import ProjectInfo
            from app.models.enums import (
                ProcurementProjectType,
                ProjectCategory,
                BiddingProcurementMethod,
            )

            ai_service = AIModelService()

            project_info = ProjectInfo(
                procurement_project_type=ProcurementProjectType.SERVICE,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            )

            # 截取内容前10000字符进行测试
            test_content = (
                processed_content[:10000]
                if len(processed_content) > 10000
                else processed_content
            )
            print(f"测试内容长度: {len(test_content)} 字符")

            result = ai_service.check_compliance(
                test_content, project_info, "debug-real-file"
            )

            print(f"✅ AI模型调用成功!")
            print(f"发现问题数量: {len(result.checkResultArr)}")

            if len(result.checkResultArr) > 0:
                print("发现的问题:")
                for i, item in enumerate(result.checkResultArr[:5]):  # 只显示前5个
                    print(f"  {i+1}. {item.quesType}: {item.quesDesc[:100]}...")
                return True
            else:
                print("❌ 未发现任何问题，需要进一步调试")

                # 尝试直接调用模型获取原始响应
                print("\n4. 获取AI模型原始响应...")

                system_prompt = f"""你是一个专业的招标文件合规性检查助手。请仔细审查招标文件内容，识别其中可能存在的合规性问题。

项目信息：
- 采购项目类型：{project_info.procurement_project_type.value}
- 项目类别：{project_info.project_category.value}
- 招标采购方式：{project_info.bidding_procurement_method.value}

请严格按照以下JSON格式返回结果，问题类型必须是以下之一：合规性、逻辑性、风险管理、规范性、公平性、可操作性

{{
    "checkResultArr": [
        {{
            "quesType": "规范性",
            "quesDesc": "问题描述",
            "originalArr": ["原文内容"],
            "point": "问题要点",
            "advice": "修改建议"
        }}
    ]
}}

如果没有发现问题，请返回：
{{
    "checkResultArr": []
}}"""

                user_prompt = (
                    f"请对以下招标文件进行合规性审查：\n\n{test_content[:5000]}"
                )

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ]

                raw_response = ai_service.call_model(messages, "debug-raw")
                print(f"AI原始响应长度: {len(raw_response)} 字符")
                print(f"AI原始响应内容: {raw_response}")

                # 尝试清理和解析
                cleaned = ai_service.clean_json_data(raw_response)
                print(f"\n清理后响应: {cleaned}")

                try:
                    parsed = json.loads(cleaned)
                    print(f"\n✅ JSON解析成功: {parsed}")
                except Exception as parse_error:
                    print(f"\n❌ JSON解析失败: {parse_error}")

                return False

        except Exception as e:
            print(f"❌ AI模型调用失败: {e}")
            import traceback

            traceback.print_exc()
            return False

    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = debug_real_file_ai_response()
    if success:
        print("\n🎉 调试成功！")
    else:
        print("\n❌ 调试发现问题，需要进一步修复。")
