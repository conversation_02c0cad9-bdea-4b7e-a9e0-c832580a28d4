#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的错误日志记录功能
"""

import sys
import os
import json
from unittest.mock import Mock, patch

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.error_logger import <PERSON>hanced<PERSON><PERSON>r<PERSON>ogger, error_logger
from app.services.ai_model_service import AIModelError
from app.services.sensitive_word_service import SensitiveWordError


def test_service_error_logging():
    """测试服务错误日志记录"""
    print("测试服务错误日志记录...")

    # 创建一个测试异常
    test_error = AIModelError(
        "测试AI模型错误", model_name="test-model", error_code="TEST_ERROR"
    )

    # 测试上下文信息
    context = {"model_name": "test-model", "request_size": 1024, "retry_count": 3}

    # 使用mock来捕获日志输出
    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_service_error(
            "AIModelService", "test_operation", test_error, "test-request-123", context
        )

        # 验证错误日志被调用
        assert mock_log.error.called
        error_call = mock_log.error.call_args[0][0]

        # 验证日志包含关键信息
        assert "AIModelService.test_operation" in error_call
        assert "test-request-123" in error_call
        assert "AIModelError" in error_call
        assert "测试AI模型错误" in error_call

        # 验证调试日志被调用
        assert mock_log.debug.called

    print("✅ 服务错误日志记录测试通过")


def test_data_processing_error_logging():
    """测试数据处理错误日志记录"""
    print("\n测试数据处理错误日志记录...")

    # 测试数据
    input_data = [
        {"type": "政治敏感", "content": "测试词1", "num": 2},
        {"type": "商业敏感", "content": "测试词2", "num": 1},
    ]

    test_error = ValueError("数据格式错误")

    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_data_processing_error(
            "敏感词转换", input_data, test_error, "test-request-456"
        )

        # 验证错误日志被调用
        assert mock_log.error.called
        error_call = mock_log.error.call_args[0][0]

        # 验证日志包含关键信息
        assert "DataProcessor.敏感词转换" in error_call
        assert "test-request-456" in error_call
        assert "ValueError" in error_call

    print("✅ 数据处理错误日志记录测试通过")


def test_api_call_error_logging():
    """测试API调用错误日志记录"""
    print("\n测试API调用错误日志记录...")

    # 模拟API调用错误
    import requests

    api_error = requests.exceptions.Timeout("API调用超时")

    request_data = {"content": "测试内容", "is_government_procurement": True}

    response_data = {"error": "timeout", "message": "请求超时"}

    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_api_call_error(
            "sensitive_word_api",
            "http://example.com/api/detect",
            "POST",
            api_error,
            "test-request-789",
            request_data,
            response_data,
            408,
        )

        # 验证错误日志被调用
        assert mock_log.error.called
        error_call = mock_log.error.call_args[0][0]

        # 验证日志包含关键信息
        assert "APIClient.POST_sensitive_word_api" in error_call
        assert "test-request-789" in error_call
        assert "Timeout" in error_call

    print("✅ API调用错误日志记录测试通过")


def test_performance_issue_logging():
    """测试性能问题日志记录"""
    print("\n测试性能问题日志记录...")

    context = {"file_size": "2MB", "processing_stage": "文件解析"}

    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_performance_issue(
            "file_processing",
            45.5,  # 实际耗时
            30.0,  # 阈值
            "test-request-perf",
            context,
        )

        # 验证警告日志被调用
        assert mock_log.warning.called
        warning_call = mock_log.warning.call_args[0][0]

        # 验证日志包含关键信息
        assert "性能问题" in warning_call
        assert "file_processing" in warning_call
        assert "test-request-perf" in warning_call
        assert "45.500秒" in warning_call
        assert "30.000秒" in warning_call

    print("✅ 性能问题日志记录测试通过")


def test_business_logic_error_logging():
    """测试业务逻辑错误日志记录"""
    print("\n测试业务逻辑错误日志记录...")

    business_error = Exception("业务规则验证失败")

    user_context = {"user_type": "government", "project_type": "服务类"}

    system_context = {
        "service_health": {"ai_model": True, "sensitive_word": False},
        "processing_mode": "fallback",
    }

    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_business_logic_error(
            "compliance_validation",
            business_error,
            "test-request-biz",
            user_context,
            system_context,
        )

        # 验证错误日志被调用
        assert mock_log.error.called
        error_call = mock_log.error.call_args[0][0]

        # 验证日志包含关键信息
        assert "BusinessLogic.compliance_validation" in error_call
        assert "test-request-biz" in error_call
        assert "Exception" in error_call

    print("✅ 业务逻辑错误日志记录测试通过")


def test_error_context_safety():
    """测试错误上下文信息的安全性"""
    print("\n测试错误上下文信息的安全性...")

    # 包含敏感信息的上下文
    sensitive_context = {
        "api_key": "secret-key-12345",
        "password": "user-password",
        "content": "包含敏感信息的长文本" * 100,  # 很长的内容
        "safe_info": "这是安全的信息",
    }

    test_error = Exception("测试错误")

    with patch("app.core.error_logger.log") as mock_log:
        error_logger.log_service_error(
            "TestService",
            "test_operation",
            test_error,
            "test-request-safe",
            sensitive_context,
        )

        # 验证日志被调用但敏感信息被适当处理
        assert mock_log.error.called
        assert mock_log.debug.called

        # 检查调试日志中的上下文信息
        debug_call = mock_log.debug.call_args_list[0][0][0]

        # 验证安全信息被包含，但敏感信息应该被适当处理
        assert "safe_info" in debug_call

    print("✅ 错误上下文信息安全性测试通过")


if __name__ == "__main__":
    print("增强的错误日志记录功能测试")
    print("=" * 50)

    try:
        test_service_error_logging()
        test_data_processing_error_logging()
        test_api_call_error_logging()
        test_performance_issue_logging()
        test_business_logic_error_logging()
        test_error_context_safety()

        print("\n🎉 所有测试通过！增强的错误日志记录功能实现成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
