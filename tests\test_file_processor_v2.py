# -*- coding: utf-8 -*-
"""
优化版文件处理器测试
"""

import pytest
from unittest.mock import patch, MagicMock, mock_open
from io import BytesIO
import requests
import tempfile
import os

from app.services.file_processor_v2 import (
    OptimizedFileProcessor,
    FileDownloadError,
    FileProcessingError,
    FileFormatError,
    create_robust_session,
)
from app.models.schemas import FileInfo
from app.models.enums import FileExtension, MimeType


class TestEnhancedExceptions:
    """增强异常类测试"""

    def test_file_download_error_basic(self):
        """测试基础文件下载异常"""
        error = FileDownloadError("下载失败")
        assert str(error) == "下载失败"
        assert error.error_type == "FILE_DOWNLOAD_ERROR"

    def test_file_download_error_with_details(self):
        """测试带详细信息的文件下载异常"""
        error = FileDownloadError(
            "下载失败",
            url="http://example.com/test.pdf",
            status_code=404,
            file_size=1024,
        )

        error_str = str(error)
        assert "下载失败" in error_str
        assert "http://example.com/test.pdf" in error_str
        assert "404" in error_str
        assert "1024" in error_str

    def test_file_processing_error_basic(self):
        """测试基础文件处理异常"""
        error = FileProcessingError("处理失败")
        assert str(error) == "处理失败"
        assert error.error_type == "FILE_PROCESSING_ERROR"

    def test_file_processing_error_with_details(self):
        """测试带详细信息的文件处理异常"""
        original_error = ValueError("原始错误")
        error = FileProcessingError(
            "处理失败",
            filename="test.docx",
            file_type=".docx",
            processing_stage="内容提取",
            original_error=original_error,
        )

        error_str = str(error)
        assert "处理失败" in error_str
        assert "test.docx" in error_str
        assert ".docx" in error_str
        assert "内容提取" in error_str
        assert "原始错误" in error_str

    def test_file_format_error_basic(self):
        """测试基础文件格式异常"""
        error = FileFormatError("格式错误")
        assert str(error) == "格式错误"
        assert error.error_type == "FILE_FORMAT_ERROR"

    def test_file_format_error_with_details(self):
        """测试带详细信息的文件格式异常"""
        error = FileFormatError(
            "格式不匹配",
            filename="test.txt",
            expected_format=".docx",
            actual_format=".txt",
            file_size=1024,
        )

        error_str = str(error)
        assert "格式不匹配" in error_str
        assert "test.txt" in error_str
        assert ".docx" in error_str
        assert ".txt" in error_str
        assert "1024" in error_str


class TestOptimizedFileProcessor:
    """优化版文件处理器测试"""

    @pytest.fixture
    def processor(self):
        """文件处理器实例"""
        return OptimizedFileProcessor()

    @pytest.fixture
    def docx_file_info(self):
        """DOCX文件信息"""
        return FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

    @pytest.fixture
    def pdf_file_info(self):
        """PDF文件信息"""
        return FileInfo(
            filename="test.pdf",
            extension=FileExtension.PDF,
            mime_type=MimeType.PDF,
            size=2048,
            url="http://example.com/test.pdf",
        )

    def test_validate_file_format_success(self, processor, docx_file_info):
        """测试文件格式验证成功"""
        result = processor.validate_file_format(docx_file_info)
        assert result is True

    def test_validate_file_format_invalid_extension(self, processor):
        """测试无效扩展名异常详情"""
        file_info = FileInfo(
            filename="test.txt",
            extension=".txt",
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.txt",
        )

        with pytest.raises(FileFormatError) as exc_info:
            processor.validate_file_format(file_info)

        error = exc_info.value
        assert error.filename == "test.txt"
        assert error.actual_format == ".txt"
        assert error.expected_format == "docx或pdf"

    def test_validate_file_format_size_mismatch(self, processor, docx_file_info):
        """测试文件大小异常详情"""
        docx_file_info.size = 0

        with pytest.raises(FileFormatError) as exc_info:
            processor.validate_file_format(docx_file_info)

        error = exc_info.value
        assert error.filename == "test.docx"
        assert error.file_size == 0

    @patch("app.services.file_processor_v2.requests.Session.get")
    def test_download_file_success(self, mock_get, processor):
        """测试文件下载成功"""
        mock_response = MagicMock()
        mock_response.headers = {"content-length": "1024"}
        mock_response.status_code = 200
        mock_response.iter_content.return_value = [b"test content"]
        mock_get.return_value = mock_response

        result = processor.download_file("http://example.com/test.docx")
        assert result == b"test content"

    @patch("app.services.file_processor_v2.requests.Session.get")
    def test_download_file_request_exception(self, mock_get, processor):
        """测试下载请求异常详情"""
        mock_response = MagicMock()
        mock_response.status_code = 404

        request_error = requests.exceptions.RequestException("网络错误")
        request_error.response = mock_response
        mock_get.side_effect = request_error

        with pytest.raises(FileDownloadError) as exc_info:
            processor.download_file("http://example.com/test.docx")

        error = exc_info.value
        assert error.url == "http://example.com/test.docx"
        assert error.status_code == 404

    @patch("app.services.file_processor_v2.settings")
    @patch("app.services.file_processor_v2.requests.Session.get")
    def test_download_file_size_limit_exceeded(
        self, mock_get, mock_settings, processor
    ):
        """测试下载大小超限异常详情"""
        mock_settings.max_file_size = 512

        mock_response = MagicMock()
        mock_response.headers = {"content-length": "1024"}
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        with pytest.raises(FileDownloadError) as exc_info:
            processor.download_file("http://example.com/test.docx")

        error = exc_info.value
        assert error.url == "http://example.com/test.docx"
        assert error.status_code == 200
        assert error.file_size == 1024

    @patch("app.services.file_processor_v2.MARKITDOWN_AVAILABLE", True)
    @patch("app.services.file_processor_v2.MarkItDown")
    @patch("tempfile.NamedTemporaryFile")
    def test_convert_with_markitdown_success(
        self, mock_temp_file, mock_markitdown_class, processor
    ):
        """测试MarkItDown转换成功"""
        # 模拟临时文件
        mock_temp = MagicMock()
        mock_temp.name = "/tmp/test.docx"
        mock_temp.__enter__.return_value = mock_temp
        mock_temp.__exit__.return_value = None
        mock_temp_file.return_value = mock_temp

        # 模拟MarkItDown
        mock_markitdown = MagicMock()
        mock_result = MagicMock()
        mock_result.text_content = "# 转换后的内容"
        mock_markitdown.convert.return_value = mock_result
        mock_markitdown_class.return_value = mock_markitdown

        # 模拟os.path.exists和os.unlink
        with patch("os.path.exists", return_value=True), patch(
            "os.unlink"
        ) as mock_unlink:

            result = processor.convert_with_markitdown(b"test content", ".docx")

            assert result == "# 转换后的内容"
            mock_markitdown.convert.assert_called_once_with("/tmp/test.docx")
            mock_unlink.assert_called_once_with("/tmp/test.docx")

    @patch("app.services.file_processor_v2.MARKITDOWN_AVAILABLE", False)
    def test_convert_with_markitdown_unavailable(self, processor):
        """测试MarkItDown不可用"""
        with pytest.raises(ImportError):
            processor.convert_with_markitdown(b"test content", ".docx")

    @patch("app.services.file_processor_v2.docx.Document")
    def test_extract_docx_content_fallback(self, mock_document, processor):
        """测试DOCX备用提取"""
        # 模拟文档对象
        mock_doc = MagicMock()

        # 模拟段落
        mock_paragraph = MagicMock()
        mock_paragraph.text = "测试段落内容"
        mock_doc.paragraphs = [mock_paragraph]

        # 模拟表格
        mock_cell1 = MagicMock()
        mock_cell1.text = "表头1"
        mock_cell2 = MagicMock()
        mock_cell2.text = "表头2"
        mock_row1 = MagicMock()
        mock_row1.cells = [mock_cell1, mock_cell2]

        mock_cell3 = MagicMock()
        mock_cell3.text = "数据1"
        mock_cell4 = MagicMock()
        mock_cell4.text = "数据2"
        mock_row2 = MagicMock()
        mock_row2.cells = [mock_cell3, mock_cell4]

        mock_table = MagicMock()
        mock_table.rows = [mock_row1, mock_row2]
        mock_doc.tables = [mock_table]

        mock_document.return_value = mock_doc

        result = processor.extract_docx_content_fallback(b"test content")

        assert "测试段落内容" in result
        assert "| 表头1 | 表头2 |" in result
        assert "| --- | --- |" in result
        assert "| 数据1 | 数据2 |" in result

    @patch("app.services.file_processor_v2.pdfplumber.open")
    def test_extract_pdf_content_fallback(self, mock_open_pdf, processor):
        """测试PDF备用提取"""
        # 模拟PDF对象
        mock_pdf = MagicMock()
        mock_page = MagicMock()
        mock_page.extract_text.return_value = "PDF页面内容"
        mock_page.extract_tables.return_value = [
            [["表头1", "表头2"], ["数据1", "数据2"]]
        ]
        mock_pdf.pages = [mock_page]
        mock_pdf.__enter__.return_value = mock_pdf
        mock_pdf.__exit__.return_value = None

        mock_open_pdf.return_value = mock_pdf

        result = processor.extract_pdf_content_fallback(b"%PDF test content")

        assert "<!-- page:1 -->" in result
        assert "PDF页面内容" in result
        assert "| 表头1 | 表头2 |" in result
        assert "| --- | --- |" in result
        assert "| 数据1 | 数据2 |" in result

    def test_add_markdown_header(self, processor):
        """测试添加Markdown标题"""
        content = "原始内容"
        result = processor._add_markdown_header(content, "test.docx", ".docx")

        assert "# test.docx" in result
        assert "**文件类型**: DOCX" in result
        assert "---" in result
        assert "原始内容" in result

    @patch.object(OptimizedFileProcessor, "validate_file_format")
    @patch.object(OptimizedFileProcessor, "download_file")
    @patch.object(OptimizedFileProcessor, "convert_with_markitdown")
    @patch("app.services.file_processor_v2.MARKITDOWN_AVAILABLE", True)
    def test_process_file_markitdown_success(
        self, mock_convert, mock_download, mock_validate, processor, docx_file_info
    ):
        """测试使用MarkItDown处理文件成功"""
        mock_validate.return_value = True
        mock_download.return_value = b"test content"
        mock_convert.return_value = "# MarkItDown转换结果"

        result = processor.process_file(docx_file_info, "test-123")

        assert result == "# MarkItDown转换结果"
        mock_validate.assert_called_once_with(docx_file_info)
        mock_download.assert_called_once()
        mock_convert.assert_called_once_with(b"test content", ".docx", "test-123")

    @patch.object(OptimizedFileProcessor, "validate_file_format")
    @patch.object(OptimizedFileProcessor, "download_file")
    @patch.object(OptimizedFileProcessor, "convert_with_markitdown")
    @patch.object(OptimizedFileProcessor, "extract_docx_content_fallback")
    @patch("app.services.file_processor_v2.MARKITDOWN_AVAILABLE", True)
    def test_process_file_fallback_to_traditional(
        self,
        mock_extract,
        mock_convert,
        mock_download,
        mock_validate,
        processor,
        docx_file_info,
    ):
        """测试MarkItDown失败后使用备用方案"""
        mock_validate.return_value = True
        mock_download.return_value = b"test content"
        mock_convert.side_effect = FileProcessingError("MarkItDown失败")
        mock_extract.return_value = "备用提取内容"

        result = processor.process_file(docx_file_info, "test-123")

        assert "# test.docx" in result
        assert "备用提取内容" in result
        mock_extract.assert_called_once_with(b"test content", "test-123")

    @patch.object(OptimizedFileProcessor, "validate_file_format")
    def test_process_file_validation_error(
        self, mock_validate, processor, docx_file_info
    ):
        """测试文件处理验证失败"""
        mock_validate.side_effect = FileFormatError(
            "格式错误", filename=docx_file_info.filename
        )

        with pytest.raises(FileFormatError) as exc_info:
            processor.process_file(docx_file_info)

        error = exc_info.value
        assert error.filename == docx_file_info.filename


class TestCreateRobustSession:
    """健壮会话创建测试"""

    def test_create_robust_session(self):
        """测试创建健壮会话"""
        session = create_robust_session()

        assert isinstance(session, requests.Session)
        assert session.adapters["http://"]
        assert session.adapters["https://"]

    def test_get_processing_stats(self, processor):
        """测试获取处理统计信息"""
        stats = processor.get_processing_stats()

        assert "markitdown_available" in stats
        assert "supported_formats" in stats
        assert "max_file_size_mb" in stats
        assert "request_timeout" in stats
        assert "max_retries" in stats

        assert stats["supported_formats"] == [".docx", ".pdf"]

    def test_validate_processing_capability_success(self, processor, docx_file_info):
        """测试处理能力验证成功"""
        capability = processor.validate_processing_capability(docx_file_info)

        assert capability["can_process"] is True
        assert capability["preferred_method"] in ["MarkItDown", "传统提取"]
        assert capability["estimated_time"] > 0
        assert isinstance(capability["warnings"], list)

    def test_validate_processing_capability_invalid_format(self, processor):
        """测试处理能力验证失败"""
        invalid_file_info = FileInfo(
            filename="test.txt",
            extension=".txt",
            mime_type="text/plain",
            size=1024,
            url="http://example.com/test.txt",
        )

        capability = processor.validate_processing_capability(invalid_file_info)

        assert capability["can_process"] is False
        assert len(capability["warnings"]) > 0

    def test_validate_processing_capability_large_file(self, processor, docx_file_info):
        """测试大文件处理能力验证"""
        docx_file_info.size = 15 * 1024 * 1024  # 15MB

        capability = processor.validate_processing_capability(docx_file_info)

        assert capability["can_process"] is True
        assert any("大文件" in warning for warning in capability["warnings"])

    @patch("app.services.file_processor_v2.MARKITDOWN_AVAILABLE", False)
    def test_validate_processing_capability_no_markitdown(
        self, processor, docx_file_info
    ):
        """测试MarkItDown不可用时的处理能力验证"""
        capability = processor.validate_processing_capability(docx_file_info)

        assert capability["can_process"] is True
        assert capability["preferred_method"] == "传统提取"
        assert any("MarkItDown不可用" in warning for warning in capability["warnings"])


class TestCreateRobustSession:
    """健壮会话创建测试"""

    def test_create_robust_session(self):
        """测试创建健壮会话"""
        session = create_robust_session()

        assert isinstance(session, requests.Session)
        assert session.adapters["http://"]
        assert session.adapters["https://"]
