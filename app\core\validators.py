# -*- coding: utf-8 -*-
"""
参数验证器模块
"""

from typing import Any, Dict, List
from fastapi import HTTPException, status
from pydantic import ValidationError

from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)
from app.models.schemas import ComplianceCheckRequest


class ParameterValidator:
    """参数验证器类"""

    @staticmethod
    def validate_procurement_project_type(value: str) -> ProcurementProjectType:
        """验证采购项目类型"""
        try:
            return ProcurementProjectType(value)
        except ValueError:
            valid_values = [e.value for e in ProcurementProjectType]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的采购项目类型: {value}。有效值: {valid_values}",
            )

    @staticmethod
    def validate_project_category(value: str) -> ProjectCategory:
        """验证项目类别"""
        try:
            return ProjectCategory(value)
        except ValueError:
            valid_values = [e.value for e in ProjectCategory]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的项目类别: {value}。有效值: {valid_values}",
            )

    @staticmethod
    def validate_bidding_procurement_method(value: str) -> BiddingProcurementMethod:
        """验证招标采购方式"""
        try:
            return BiddingProcurementMethod(value)
        except ValueError:
            valid_values = [e.value for e in BiddingProcurementMethod]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的招标采购方式: {value}。有效值: {valid_values}",
            )

    @staticmethod
    def validate_file_extension(value: str) -> FileExtension:
        """验证文件扩展名"""
        try:
            return FileExtension(value)
        except ValueError:
            valid_values = [e.value for e in FileExtension]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件格式: {value}。支持的格式: {valid_values}",
            )

    @staticmethod
    def validate_mime_type(value: str) -> MimeType:
        """验证MIME类型"""
        try:
            return MimeType(value)
        except ValueError:
            valid_values = [e.value for e in MimeType]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的MIME类型: {value}。支持的类型: {valid_values}",
            )

    @staticmethod
    def validate_file_format_consistency(extension: str, mime_type: str) -> bool:
        """验证文件扩展名和MIME类型的一致性"""
        valid_combinations = {
            FileExtension.DOCX.value: MimeType.DOCX.value,
            FileExtension.PDF.value: MimeType.PDF.value,
        }

        expected_mime = valid_combinations.get(extension)
        if expected_mime != mime_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件扩展名 {extension} 与MIME类型 {mime_type} 不匹配",
            )

        return True

    @staticmethod
    def validate_file_size(size: int, max_size: int = 300 * 1024 * 1024) -> bool:
        """验证文件大小"""
        if size <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="文件大小必须大于0"
            )

        if size > max_size:
            max_size_mb = max_size / 1024 / 1024
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小不能超过 {max_size_mb:.0f}MB",
            )

        return True

    @classmethod
    def validate_compliance_request(
        cls, request_data: Dict[str, Any]
    ) -> ComplianceCheckRequest:
        """验证合规性检查请求"""
        try:
            # 使用Pydantic模型进行验证
            request = ComplianceCheckRequest(**request_data)

            # 额外的业务逻辑验证
            cls.validate_file_format_consistency(
                request.bidding_doc.extension.value, request.bidding_doc.mime_type.value
            )

            cls.validate_file_size(request.bidding_doc.size)

            return request

        except ValidationError as e:
            # 转换Pydantic验证错误为HTTP异常
            error_messages = []
            for error in e.errors():
                field = " -> ".join(str(loc) for loc in error["loc"])
                message = error["msg"]
                error_messages.append(f"{field}: {message}")

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"请求参数验证失败: {'; '.join(error_messages)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"请求验证失败: {str(e)}",
            )


def get_enum_values() -> Dict[str, List[str]]:
    """获取所有枚举值，用于API文档"""
    return {
        "ProcurementProjectType": [e.value for e in ProcurementProjectType],
        "ProjectCategory": [e.value for e in ProjectCategory],
        "BiddingProcurementMethod": [e.value for e in BiddingProcurementMethod],
        "FileExtension": [e.value for e in FileExtension],
        "MimeType": [e.value for e in MimeType],
        # 保持向后兼容的复数形式
        "procurement_project_types": [e.value for e in ProcurementProjectType],
        "project_categories": [e.value for e in ProjectCategory],
        "bidding_procurement_methods": [e.value for e in BiddingProcurementMethod],
        "file_extensions": [e.value for e in FileExtension],
        "mime_types": [e.value for e in MimeType],
    }
