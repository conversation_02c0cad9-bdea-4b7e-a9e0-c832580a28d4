#!/usr/bin/env python3
"""
测试高级JSON修复功能
"""

import json
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_advanced_json_fix():
    """测试高级JSON修复功能"""
    
    # 模拟包含各种问题的AI响应
    problematic_responses = [
        # 1. 包含中文引号的响应
        """{
  "checkResultArr": [
    {
      "quesType": "合规性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致。",
      "originalArr": ["测试原文"],
      "point": "项目信息核验",
      "advice": "删除多余的"阿萨德"字样"
    }
  ]
}""",
        
        # 2. 包含制表符的响应
        """{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "目录页码不对应，例如"第二章	投标人须知前附表	4"实际页码为8。",
      "originalArr": ["第二章	投标人须知前附表	4"],
      "point": "格式规范性检查",
      "advice": "重新核对页码"
    }
  ]
}""",
        
        # 3. 包含换行符的响应
        """{
  "checkResultArr": [
    {
      "quesType": "合规性",
      "quesDesc": "文件内容有问题
换行测试",
      "originalArr": ["测试
换行"],
      "point": "内容检查",
      "advice": "修复内容"
    }
  ]
}""",
    ]
    
    print("🔍 测试高级JSON修复功能...")
    
    try:
        from app.services.ai_model_service import ai_model_service
        
        for i, response in enumerate(problematic_responses, 1):
            print(f"\n=== 测试用例 {i} ===")
            print(f"原始响应长度: {len(response)} 字符")
            
            # 检查包含的问题字符
            issues = []
            if '"' in response or '"' in response:
                issues.append("中文引号")
            if '\t' in response:
                issues.append("制表符")
            if '\n' in response:
                issues.append("换行符")
            
            print(f"包含问题: {', '.join(issues) if issues else '无'}")
            
            # 测试JSON清理
            try:
                cleaned = ai_model_service.clean_json_data(response)
                print(f"✅ JSON清理成功，长度: {len(cleaned)} 字符")
                
                # 测试JSON解析
                try:
                    parsed = json.loads(cleaned)
                    print(f"✅ JSON解析成功，结果数量: {len(parsed['checkResultArr'])}")
                    
                    # 验证数据完整性
                    if parsed['checkResultArr']:
                        item = parsed['checkResultArr'][0]
                        print(f"  quesType: '{item.get('quesType', '')}'")
                        print(f"  quesDesc: '{item.get('quesDesc', '')[:50]}...'")
                        print(f"  point: '{item.get('point', '')}'")
                        print(f"  advice: '{item.get('advice', '')[:50]}...'")
                        
                        # 检查是否有空字段
                        empty_fields = []
                        for field in ['quesType', 'quesDesc', 'point', 'advice']:
                            if not item.get(field, '').strip():
                                empty_fields.append(field)
                        
                        if empty_fields:
                            print(f"❌ 发现空字段: {empty_fields}")
                        else:
                            print("✅ 所有字段都有内容")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"清理后内容: {cleaned[:200]}...")
                    
            except Exception as e:
                print(f"❌ JSON清理失败: {e}")
        
        print("\n🎉 高级JSON修复测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_advanced_json_fix()
    if success:
        print("\n✅ 高级JSON修复功能正常工作")
    else:
        print("\n❌ 高级JSON修复功能仍有问题")
    sys.exit(0 if success else 1)
