# -*- coding: utf-8 -*-
"""
性能优化功能测试
测试缓存、队列管理和资源管理功能
"""

import pytest
import pytest_asyncio
import asyncio
import time
from unittest.mock import patch, MagicMock

from app.core.cache_manager import (
    MemoryCache,
    ResourceManager,
    DocumentCache,
    memory_cache,
    resource_manager,
    document_cache,
)
from app.core.queue_manager import (
    RequestQueue,
    RequestPriority,
    RequestStatus,
    QueuedRequest,
    request_queue,
)


class TestMemoryCache:
    """内存缓存测试"""

    @pytest.fixture
    def cache(self):
        """创建测试缓存实例"""
        return MemoryCache(max_size_mb=1, ttl_seconds=60)

    def test_cache_basic_operations(self, cache):
        """测试基本缓存操作"""
        # 测试设置和获取
        assert cache.set("key1", "value1") is True
        assert cache.get("key1") == "value1"

        # 测试不存在的键
        assert cache.get("nonexistent") is None

        # 测试统计信息
        stats = cache.get_stats()
        assert stats["hits"] == 1
        assert stats["misses"] == 1
        assert stats["item_count"] == 1

    def test_cache_ttl_expiration(self, cache):
        """测试缓存过期"""
        # 设置短TTL的缓存
        short_ttl_cache = MemoryCache(max_size_mb=1, ttl_seconds=1)

        short_ttl_cache.set("key1", "value1")
        assert short_ttl_cache.get("key1") == "value1"

        # 等待过期
        time.sleep(1.1)
        assert short_ttl_cache.get("key1") is None

    def test_cache_size_limit(self, cache):
        """测试缓存大小限制"""
        # 添加大量数据直到触发驱逐
        large_value = "x" * 1024  # 1KB数据

        for i in range(1100):  # 超过1MB限制
            cache.set(f"key{i}", large_value)

        stats = cache.get_stats()
        # 应该触发了驱逐
        assert stats["evictions"] > 0
        assert stats["current_size_mb"] <= 1.0

    def test_cache_lru_eviction(self, cache):
        """测试LRU驱逐策略"""
        # 填满缓存
        for i in range(10):
            cache.set(f"key{i}", f"value{i}")

        # 访问某些键以更新LRU
        cache.get("key0")
        cache.get("key1")

        # 添加新项触发驱逐
        large_value = "x" * (1024 * 1024)  # 1MB数据
        cache.set("large_key", large_value)

        # key0和key1应该仍然存在（最近访问过）
        assert cache.get("key0") == "value0"
        assert cache.get("key1") == "value1"

    def test_cache_cleanup(self, cache):
        """测试缓存清理"""
        cache.set("key1", "value1")
        cache.set("key2", "value2")

        assert cache.get_stats()["item_count"] == 2

        cache.clear()
        assert cache.get_stats()["item_count"] == 0
        assert cache.get("key1") is None


class TestResourceManager:
    """资源管理器测试"""

    @pytest.fixture
    def manager(self):
        """创建测试资源管理器实例"""
        return ResourceManager()

    def test_memory_usage_monitoring(self, manager):
        """测试内存使用监控"""
        usage = manager.get_memory_usage()

        assert "rss_mb" in usage
        assert "vms_mb" in usage
        assert "percent" in usage
        assert "available_mb" in usage
        assert usage["rss_mb"] > 0

    def test_memory_pressure_detection(self, manager):
        """测试内存压力检测"""
        # 正常情况下应该没有内存压力
        assert manager.check_memory_pressure() is False

        # 模拟高内存使用
        manager.memory_threshold_mb = 1  # 设置很低的阈值
        # 这可能会触发内存压力检测，取决于当前内存使用

    def test_garbage_collection(self, manager):
        """测试垃圾回收"""
        # 创建一些垃圾对象
        garbage = []
        for i in range(1000):
            garbage.append([i] * 100)

        # 删除引用
        del garbage

        # 执行垃圾回收
        result = manager.force_garbage_collection()

        assert "gen0" in result
        assert "gen1" in result
        assert "gen2" in result
        assert "total_collected" in result

    def test_memory_optimization(self, manager):
        """测试内存优化"""
        result = manager.optimize_memory()

        assert "actions_taken" in result
        assert "memory_usage" in result
        assert isinstance(result["actions_taken"], list)


class TestDocumentCache:
    """文档缓存测试"""

    @pytest.fixture
    def doc_cache(self):
        """创建测试文档缓存实例"""
        cache_manager = MemoryCache(max_size_mb=1, ttl_seconds=60)
        return DocumentCache(cache_manager)

    def test_document_caching(self, doc_cache):
        """测试文档缓存"""
        file_url = "http://example.com/test.docx"
        file_size = 1024
        content = "这是测试文档内容"

        # 首次获取应该为空
        assert doc_cache.get_processed_content(file_url, file_size) is None

        # 缓存内容
        assert doc_cache.cache_processed_content(file_url, file_size, content) is True

        # 再次获取应该返回缓存的内容
        assert doc_cache.get_processed_content(file_url, file_size) == content

    def test_cache_key_generation(self, doc_cache):
        """测试缓存键生成"""
        # 相同URL和大小应该生成相同的键
        key1 = doc_cache._generate_cache_key("http://example.com/test.docx", 1024)
        key2 = doc_cache._generate_cache_key("http://example.com/test.docx", 1024)
        assert key1 == key2

        # 不同URL或大小应该生成不同的键
        key3 = doc_cache._generate_cache_key("http://example.com/test2.docx", 1024)
        key4 = doc_cache._generate_cache_key("http://example.com/test.docx", 2048)
        assert key1 != key3
        assert key1 != key4


class TestRequestQueue:
    """请求队列测试"""

    @pytest_asyncio.fixture
    async def queue(self):
        """创建测试队列实例"""
        test_queue = RequestQueue(max_concurrent=2, max_queue_size=10)
        await test_queue.start_workers(2)
        yield test_queue
        await test_queue.stop_workers()

    async def dummy_handler(self, value: str, delay: float = 0.1) -> str:
        """测试处理函数"""
        await asyncio.sleep(delay)
        return f"processed_{value}"

    async def failing_handler(self, error_msg: str) -> str:
        """失败的处理函数"""
        raise ValueError(error_msg)

    @pytest.mark.asyncio
    async def test_queue_basic_operations(self, queue):
        """测试队列基本操作"""
        # 添加请求到队列
        request_id = await queue.enqueue(
            self.dummy_handler, "test1", priority=RequestPriority.NORMAL
        )

        assert request_id is not None

        # 等待处理完成
        await asyncio.sleep(0.5)

        # 检查请求状态
        status = await queue.get_request_status(request_id)
        assert status is not None
        assert status["status"] == RequestStatus.COMPLETED.value

    @pytest.mark.asyncio
    async def test_queue_priority_handling(self, queue):
        """测试队列优先级处理"""
        # 添加不同优先级的请求
        low_id = await queue.enqueue(
            self.dummy_handler, "low", delay=0.2, priority=RequestPriority.LOW
        )
        high_id = await queue.enqueue(
            self.dummy_handler, "high", delay=0.2, priority=RequestPriority.HIGH
        )
        normal_id = await queue.enqueue(
            self.dummy_handler, "normal", delay=0.2, priority=RequestPriority.NORMAL
        )

        # 等待处理完成
        await asyncio.sleep(1.0)

        # 检查处理顺序（高优先级应该先处理）
        high_status = await queue.get_request_status(high_id)
        normal_status = await queue.get_request_status(normal_id)
        low_status = await queue.get_request_status(low_id)

        assert high_status["status"] == RequestStatus.COMPLETED.value
        assert normal_status["status"] == RequestStatus.COMPLETED.value
        assert low_status["status"] == RequestStatus.COMPLETED.value

    @pytest.mark.asyncio
    async def test_queue_error_handling(self, queue):
        """测试队列错误处理"""
        # 添加会失败的请求
        request_id = await queue.enqueue(self.failing_handler, "test error")

        # 等待处理完成
        await asyncio.sleep(0.5)

        # 检查请求状态
        status = await queue.get_request_status(request_id)
        assert status is not None
        assert status["status"] == RequestStatus.FAILED.value
        assert status["error"] is not None

    @pytest.mark.asyncio
    async def test_queue_timeout_handling(self, queue):
        """测试队列超时处理"""
        # 添加会超时的请求
        request_id = await queue.enqueue(
            self.dummy_handler, "timeout_test", delay=2.0, timeout_seconds=0.5
        )

        # 等待处理完成
        await asyncio.sleep(1.0)

        # 检查请求状态
        status = await queue.get_request_status(request_id)
        assert status is not None
        assert status["status"] == RequestStatus.FAILED.value

    @pytest.mark.asyncio
    async def test_queue_cancellation(self, queue):
        """测试请求取消"""
        # 添加长时间运行的请求
        request_id = await queue.enqueue(self.dummy_handler, "cancel_test", delay=1.0)

        # 等待请求开始处理
        await asyncio.sleep(0.1)

        # 取消请求
        cancelled = await queue.cancel_request(request_id)
        assert cancelled is True

        # 检查请求状态
        status = await queue.get_request_status(request_id)
        assert status is not None
        assert status["status"] == RequestStatus.CANCELLED.value

    @pytest.mark.asyncio
    async def test_queue_stats(self, queue):
        """测试队列统计"""
        # 添加一些请求
        await queue.enqueue(self.dummy_handler, "stats1")
        await queue.enqueue(self.dummy_handler, "stats2")
        await queue.enqueue(self.failing_handler, "error")

        # 等待处理完成
        await asyncio.sleep(0.5)

        # 检查统计信息
        stats = queue.get_queue_stats()
        assert stats["total_requests"] >= 3
        assert stats["completed_requests"] >= 2
        assert stats["failed_requests"] >= 1
        assert "success_rate" in stats
        assert "failure_rate" in stats

    @pytest.mark.asyncio
    async def test_queue_cleanup(self, queue):
        """测试队列清理"""
        # 添加并完成一些请求
        request_id = await queue.enqueue(self.dummy_handler, "cleanup_test")
        await asyncio.sleep(0.5)

        # 确认请求已完成
        status = await queue.get_request_status(request_id)
        assert status is not None

        # 清理已完成的请求（使用很短的最大年龄）
        cleaned = await queue.cleanup_completed(max_age_hours=0)
        assert cleaned >= 0

        # 清理后应该找不到请求
        status_after = await queue.get_request_status(request_id)
        # 根据清理策略，状态可能为None或仍然存在


class TestPerformanceIntegration:
    """性能优化集成测试"""

    def test_global_instances_initialization(self):
        """测试全局实例初始化"""
        assert memory_cache is not None
        assert resource_manager is not None
        assert document_cache is not None
        assert request_queue is not None

    def test_cache_integration_with_document_processing(self):
        """测试缓存与文档处理的集成"""
        # 模拟文档处理缓存
        file_url = "http://example.com/integration_test.docx"
        file_size = 2048
        content = "集成测试文档内容"

        # 缓存文档内容
        success = document_cache.cache_processed_content(file_url, file_size, content)
        assert success is True

        # 获取缓存的内容
        cached_content = document_cache.get_processed_content(file_url, file_size)
        assert cached_content == content

        # 检查缓存统计
        stats = memory_cache.get_stats()
        assert stats["item_count"] >= 1

    @pytest.mark.asyncio
    async def test_queue_with_resource_management(self):
        """测试队列与资源管理的集成"""

        async def resource_intensive_task(task_id: str) -> str:
            """资源密集型任务"""
            # 模拟内存使用
            data = [i for i in range(1000)]

            # 检查内存使用
            memory_usage = resource_manager.get_memory_usage()

            # 模拟处理时间
            await asyncio.sleep(0.1)

            return f"task_{task_id}_completed"

        # 创建临时队列
        test_queue = RequestQueue(max_concurrent=2, max_queue_size=5)
        await test_queue.start_workers(2)

        try:
            # 添加多个资源密集型任务
            task_ids = []
            for i in range(3):
                task_id = await test_queue.enqueue(resource_intensive_task, f"task_{i}")
                task_ids.append(task_id)

            # 等待所有任务完成
            await asyncio.sleep(1.0)

            # 检查任务状态
            completed_count = 0
            for task_id in task_ids:
                status = await test_queue.get_request_status(task_id)
                if status and status["status"] == RequestStatus.COMPLETED.value:
                    completed_count += 1

            assert completed_count == 3

            # 检查队列统计
            stats = test_queue.get_queue_stats()
            assert stats["completed_requests"] >= 3

        finally:
            await test_queue.stop_workers()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
