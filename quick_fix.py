#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本 - 解决当前的依赖和配置问题
"""

import subprocess
import sys
import os


def install_missing_packages():
    """安装缺失的包"""
    packages = [
        "pydantic-settings",
        "filetype",
        "openai",
        "python-docx",
        "pdfplumber",
        "markdownify",
        "loguru",
        "requests",
        "fastapi",
        "uvicorn[standard]",
    ]

    print("安装缺失的包...")
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                check=True,
                capture_output=True,
            )
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")


def create_minimal_env():
    """创建最小化的.env文件"""
    env_content = """# 最小化配置 - 用于测试
ENVIRONMENT=development
DEBUG=true
MODEL_APIKEY=test_key
MODEL_NAME=gemini-2.5-flash
MODEL_URL=https://jccbmkhayojw.ap-southeast-1.clawcloudrun.com/v1
SENSITIVE_WORD_API_URL=http://************:8087
MAX_FILE_SIZE=314572800
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs
REQUEST_TIMEOUT=300
MAX_RETRIES=3
"""

    if not os.path.exists(".env"):
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ 创建了最小化.env文件")
    else:
        print("✅ .env文件已存在")


def create_logs_dir():
    """创建日志目录"""
    os.makedirs("logs", exist_ok=True)
    print("✅ 创建了logs目录")


def main():
    """主函数"""
    print("快速修复脚本")
    print("=" * 50)

    # 1. 安装缺失的包
    install_missing_packages()

    # 2. 创建环境文件
    create_minimal_env()

    # 3. 创建必要目录
    create_logs_dir()

    print("\n" + "=" * 50)
    print("快速修复完成！")
    print("现在可以尝试运行: python test_basic_functionality.py")


if __name__ == "__main__":
    main()
