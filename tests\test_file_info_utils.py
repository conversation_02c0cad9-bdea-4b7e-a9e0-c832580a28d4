#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件信息推断工具测试
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.utils.file_info_utils import (
    extract_filename_from_url,
    infer_file_extension_and_mime_type,
    get_file_size_from_url,
    infer_file_info_from_url,
    validate_inferred_file_info,
    FileInfoInferenceError,
)
from app.models.enums import FileExtension, MimeType


class TestFileInfoUtils(unittest.TestCase):
    """文件信息推断工具测试类"""

    def test_extract_filename_from_url_simple(self):
        """测试从简单URL提取文件名"""

        test_cases = [
            ("http://example.com/document.docx", "document.docx"),
            ("https://example.com/path/to/file.pdf", "file.pdf"),
            ("http://example.com/招标文件.docx", "招标文件.docx"),
            ("https://example.com/测试文档.pdf", "测试文档.pdf"),
        ]

        for url, expected in test_cases:
            with self.subTest(url=url):
                result = extract_filename_from_url(url)
                self.assertEqual(result, expected)

    def test_extract_filename_from_url_with_query(self):
        """测试从带查询参数的URL提取文件名"""

        test_cases = [
            ("http://example.com/download?file=document.docx", "document.docx"),
            ("http://example.com/api?filename=test.pdf&version=1", "test.pdf"),
            ("http://example.com/get?name=招标文件.docx", "招标文件.docx"),
        ]

        for url, expected in test_cases:
            with self.subTest(url=url):
                result = extract_filename_from_url(url)
                self.assertEqual(result, expected)

    def test_extract_filename_from_url_fallback(self):
        """测试URL无文件名时的回退机制"""

        test_cases = [
            "http://example.com/",
            "http://example.com/path/",
            "http://example.com/api/download",
        ]

        for url in test_cases:
            with self.subTest(url=url):
                result = extract_filename_from_url(url)
                self.assertEqual(result, "document.docx")  # 默认文件名

    def test_infer_file_extension_and_mime_type(self):
        """测试文件扩展名和MIME类型推断"""

        test_cases = [
            ("document.docx", (FileExtension.DOCX, MimeType.DOCX)),
            ("file.pdf", (FileExtension.PDF, MimeType.PDF)),
            ("Document.DOCX", (FileExtension.DOCX, MimeType.DOCX)),  # 大小写
            ("FILE.PDF", (FileExtension.PDF, MimeType.PDF)),
            ("招标文件.docx", (FileExtension.DOCX, MimeType.DOCX)),
            ("测试.pdf", (FileExtension.PDF, MimeType.PDF)),
        ]

        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = infer_file_extension_and_mime_type(filename)
                self.assertEqual(result, expected)

    def test_infer_file_extension_unknown(self):
        """测试未知扩展名的处理"""

        test_cases = [
            "document.txt",
            "file.xlsx",
            "unknown",
            "file.unknown",
        ]

        for filename in test_cases:
            with self.subTest(filename=filename):
                result = infer_file_extension_and_mime_type(filename)
                # 应该返回默认的docx格式
                self.assertEqual(result, (FileExtension.DOCX, MimeType.DOCX))

    @patch("app.utils.file_info_utils.requests.head")
    def test_get_file_size_from_url_success(self, mock_head):
        """测试成功获取文件大小"""

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"content-length": "1024"}
        mock_response.raise_for_status.return_value = None
        mock_head.return_value = mock_response

        result = get_file_size_from_url("http://example.com/file.docx")
        self.assertEqual(result, 1024)

        mock_head.assert_called_once_with(
            "http://example.com/file.docx", timeout=30, allow_redirects=True
        )

    @patch("app.utils.file_info_utils.requests.head")
    def test_get_file_size_from_url_no_content_length(self, mock_head):
        """测试响应中没有content-length头的情况"""

        # 模拟没有content-length的响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {}
        mock_response.raise_for_status.return_value = None
        mock_head.return_value = mock_response

        result = get_file_size_from_url("http://example.com/file.docx")
        self.assertIsNone(result)

    @patch("app.utils.file_info_utils.requests.head")
    def test_get_file_size_from_url_error(self, mock_head):
        """测试请求失败的情况"""

        # 模拟请求异常
        mock_head.side_effect = Exception("Connection error")

        result = get_file_size_from_url("http://example.com/file.docx")
        self.assertIsNone(result)

    @patch("app.utils.file_info_utils.get_file_size_from_url")
    def test_infer_file_info_from_url_success(self, mock_get_size):
        """测试成功推断文件信息"""

        # 模拟文件大小获取
        mock_get_size.return_value = 2048

        url = "http://example.com/招标文件.docx"
        result = infer_file_info_from_url(url)

        self.assertEqual(result.filename, "招标文件.docx")
        self.assertEqual(result.extension, FileExtension.DOCX)
        self.assertEqual(result.mime_type, MimeType.DOCX)
        self.assertEqual(result.size, 2048)
        self.assertEqual(str(result.url), url)

    @patch("app.utils.file_info_utils.get_file_size_from_url")
    def test_infer_file_info_from_url_no_size(self, mock_get_size):
        """测试无法获取文件大小时的处理"""

        # 模拟无法获取文件大小
        mock_get_size.return_value = None

        url = "http://example.com/test.pdf"
        result = infer_file_info_from_url(url)

        self.assertEqual(result.filename, "test.pdf")
        self.assertEqual(result.extension, FileExtension.PDF)
        self.assertEqual(result.mime_type, MimeType.PDF)
        self.assertEqual(result.size, 1024 * 1024)  # 默认大小
        self.assertEqual(str(result.url), url)

    def test_infer_file_info_from_url_invalid_url(self):
        """测试无效URL的处理"""

        invalid_urls = [
            "",
            "not-a-url",
            "ftp://example.com/file.docx",
        ]

        for url in invalid_urls:
            with self.subTest(url=url):
                with self.assertRaises(FileInfoInferenceError):
                    infer_file_info_from_url(url)

    def test_validate_inferred_file_info_valid(self):
        """测试有效文件信息的验证"""

        from app.models.schemas import FileInfo

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        result = validate_inferred_file_info(file_info)
        self.assertTrue(result)

    def test_validate_inferred_file_info_invalid_filename(self):
        """测试无效文件名的验证"""

        from app.models.schemas import FileInfo

        file_info = FileInfo(
            filename="",  # 空文件名
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        result = validate_inferred_file_info(file_info)
        self.assertFalse(result)

    def test_validate_inferred_file_info_invalid_size(self):
        """测试无效文件大小的验证"""

        from app.models.schemas import FileInfo

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=0,  # 无效大小
            url="http://example.com/test.docx",
        )

        result = validate_inferred_file_info(file_info)
        self.assertFalse(result)

    def test_validate_inferred_file_info_invalid_url(self):
        """测试无效URL的验证"""

        from app.models.schemas import FileInfo

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="ftp://example.com/test.docx",  # 无效协议
        )

        result = validate_inferred_file_info(file_info)
        self.assertFalse(result)

    def test_validate_inferred_file_info_exception(self):
        """测试验证过程中的异常处理"""

        # 传入None应该返回False
        result = validate_inferred_file_info(None)
        self.assertFalse(result)


def run_tests():
    """运行测试"""

    print("=" * 60)
    print("开始运行文件信息推断工具测试")
    print("=" * 60)

    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestFileInfoUtils)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"总测试数: {total_tests}")
    print(f"通过: {passed}")
    print(f"失败: {failures}")
    print(f"错误: {errors}")
    print(f"成功率: {passed/total_tests*100:.1f}%")

    if failures > 0:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")

    if errors > 0:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")

    success = failures == 0 and errors == 0

    if success:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n⚠️  有 {failures + errors} 个测试失败")

    return success


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
