# -*- coding: utf-8 -*-
"""
验证中间件测试
"""

import json
import time
import pytest
from unittest.mock import MagicMock, patch
from fastapi import FastAP<PERSON>, Request
from fastapi.testclient import TestClient

from app.middleware.validation import (
    RequestValidationMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware,
)


class TestRequestValidationMiddleware:
    """请求验证中间件测试"""

    @pytest.fixture
    def app_with_validation(self):
        """带验证中间件的测试应用"""
        app = FastAPI()
        app.add_middleware(RequestValidationMiddleware, validate_enum_params=True)

        @app.post("/api/v1/check-compliance")
        async def test_endpoint(request: Request):
            return {"message": "success"}

        @app.get("/api/v1/health")
        async def health_endpoint():
            return {"status": "healthy"}

        return app

    @pytest.fixture
    def client_with_validation(self, app_with_validation):
        """带验证中间件的测试客户端"""
        return TestClient(app_with_validation)

    def test_validation_middleware_valid_request(self, client_with_validation):
        """测试有效请求通过验证"""
        valid_request = {
            "bidding_doc": {
                "filename": "test.docx",
                "extension": ".docx",
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 1024,
                "url": "http://example.com/test.docx",
            },
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=valid_request
        )

        # 验证通过，应该到达端点
        assert response.status_code == 200
        assert response.json()["message"] == "success"

    def test_validation_middleware_invalid_json(self, client_with_validation):
        """测试无效JSON"""
        response = client_with_validation.post(
            "/api/v1/check-compliance",
            data="invalid json",
            headers={"Content-Type": "application/json"},
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "JSON格式" in data["message"]
        assert data["error_code"] == "INVALID_JSON"

    def test_validation_middleware_invalid_procurement_type(
        self, client_with_validation
    ):
        """测试无效采购项目类型"""
        invalid_request = {
            "procurement_project_type": "无效类型",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "无效的采购项目类型" in data["message"]
        assert data["error_code"] == "INVALID_PROCUREMENT_PROJECT_TYPE"
        assert data["field"] == "procurement_project_type"

    def test_validation_middleware_invalid_project_category(
        self, client_with_validation
    ):
        """测试无效项目类别"""
        invalid_request = {
            "procurement_project_type": "服务类",
            "project_category": "无效类别",
            "bidding_procurement_method": "公开招标",
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "无效的项目类别" in data["message"]
        assert data["error_code"] == "INVALID_PROJECT_CATEGORY"

    def test_validation_middleware_invalid_bidding_method(self, client_with_validation):
        """测试无效招标采购方式"""
        invalid_request = {
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "无效方式",
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "无效的招标采购方式" in data["message"]
        assert data["error_code"] == "INVALID_BIDDING_PROCUREMENT_METHOD"

    def test_validation_middleware_missing_file_field(self, client_with_validation):
        """测试文件信息缺少字段"""
        invalid_request = {
            "bidding_doc": {
                "filename": "test.docx",
                # 缺少extension字段
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 1024,
                "url": "http://example.com/test.docx",
            }
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "缺少必需字段" in data["message"]
        assert data["error_code"] == "MISSING_FILE_FIELD"

    def test_validation_middleware_unsupported_file_extension(
        self, client_with_validation
    ):
        """测试不支持的文件扩展名"""
        invalid_request = {
            "bidding_doc": {
                "filename": "test.txt",
                "extension": ".txt",  # 不支持的扩展名
                "mime_type": "text/plain",
                "size": 1024,
                "url": "http://example.com/test.txt",
            }
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "不支持的文件扩展名" in data["message"]
        assert data["error_code"] == "UNSUPPORTED_FILE_EXTENSION"

    def test_validation_middleware_invalid_file_size(self, client_with_validation):
        """测试无效文件大小"""
        invalid_request = {
            "bidding_doc": {
                "filename": "test.docx",
                "extension": ".docx",
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 0,  # 无效大小
                "url": "http://example.com/test.docx",
            }
        }

        response = client_with_validation.post(
            "/api/v1/check-compliance", json=invalid_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "文件大小必须是大于0的整数" in data["message"]
        assert data["error_code"] == "INVALID_FILE_SIZE"

    def test_validation_middleware_skip_non_validation_paths(
        self, client_with_validation
    ):
        """测试跳过不需要验证的路径"""
        # 健康检查路径不需要验证
        response = client_with_validation.get("/api/v1/health")

        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

    def test_validation_middleware_skip_get_requests(self, client_with_validation):
        """测试跳过GET请求验证"""
        # GET请求不进行请求体验证
        response = client_with_validation.get("/api/v1/check-compliance")

        # 应该通过验证中间件，但可能在端点处失败（这里会返回405 Method Not Allowed）
        assert response.status_code == 405  # Method Not Allowed


class TestSecurityHeadersMiddleware:
    """安全头中间件测试"""

    @pytest.fixture
    def app_with_security(self):
        """带安全头中间件的测试应用"""
        app = FastAPI()
        app.add_middleware(SecurityHeadersMiddleware)

        @app.get("/test")
        async def test_endpoint():
            return {"message": "success"}

        return app

    @pytest.fixture
    def client_with_security(self, app_with_security):
        """带安全头中间件的测试客户端"""
        return TestClient(app_with_security)

    def test_security_headers_added(self, client_with_security):
        """测试安全头被添加"""
        response = client_with_security.get("/test")

        assert response.status_code == 200

        # 检查安全头
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        assert response.headers["X-Frame-Options"] == "DENY"
        assert response.headers["X-XSS-Protection"] == "1; mode=block"
        assert response.headers["Referrer-Policy"] == "strict-origin-when-cross-origin"
        assert response.headers["Content-Security-Policy"] == "default-src 'self'"

        # 检查Server头被移除
        assert "Server" not in response.headers


class TestRateLimitMiddleware:
    """限流中间件测试"""

    @pytest.fixture
    def app_with_rate_limit(self):
        """带限流中间件的测试应用"""
        app = FastAPI()
        app.add_middleware(RateLimitMiddleware, max_requests=5, window_seconds=60)

        @app.get("/test")
        async def test_endpoint():
            return {"message": "success"}

        return app

    @pytest.fixture
    def client_with_rate_limit(self, app_with_rate_limit):
        """带限流中间件的测试客户端"""
        return TestClient(app_with_rate_limit)

    def test_rate_limit_normal_requests(self, client_with_rate_limit):
        """测试正常请求通过限流"""
        response = client_with_rate_limit.get("/test")

        assert response.status_code == 200

        # 检查限流相关头
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers

        assert response.headers["X-RateLimit-Limit"] == "5"
        assert int(response.headers["X-RateLimit-Remaining"]) == 4  # 5 - 1 = 4

    def test_rate_limit_exceeded(self, client_with_rate_limit):
        """测试超过限流限制"""
        # 发送6个请求（超过限制的5个）
        for i in range(5):
            response = client_with_rate_limit.get("/test")
            assert response.status_code == 200

        # 第6个请求应该被限流
        response = client_with_rate_limit.get("/test")

        assert response.status_code == 429
        data = response.json()
        assert data["success"] is False
        assert "请求过于频繁" in data["message"]
        assert data["error_code"] == "RATE_LIMIT_EXCEEDED"
        assert "Retry-After" in response.headers

    @patch("app.middleware.validation.time.time")
    def test_rate_limit_window_reset(self, mock_time, client_with_rate_limit):
        """测试时间窗口重置"""
        # 模拟时间
        mock_time.return_value = 1000

        # 发送5个请求
        for i in range(5):
            response = client_with_rate_limit.get("/test")
            assert response.status_code == 200

        # 第6个请求被限流
        response = client_with_rate_limit.get("/test")
        assert response.status_code == 429

        # 模拟时间窗口过去
        mock_time.return_value = 1100  # 100秒后，超过60秒窗口

        # 现在应该可以正常请求
        response = client_with_rate_limit.get("/test")
        assert response.status_code == 200

    def test_rate_limit_different_ips(self, app_with_rate_limit):
        """测试不同IP的限流独立性"""
        # 创建两个不同IP的客户端
        client1 = TestClient(app_with_rate_limit)
        client2 = TestClient(app_with_rate_limit)

        # 客户端1发送5个请求
        for i in range(5):
            response = client1.get("/test")
            assert response.status_code == 200

        # 客户端1第6个请求被限流
        response = client1.get("/test")
        assert response.status_code == 429

        # 客户端2应该仍然可以正常请求（不同IP）
        response = client2.get("/test")
        assert response.status_code == 200


class TestMiddlewareIntegration:
    """中间件集成测试"""

    @pytest.fixture
    def app_with_all_middleware(self):
        """带所有中间件的测试应用"""
        app = FastAPI()

        # 按照正确顺序添加中间件
        app.add_middleware(SecurityHeadersMiddleware)
        app.add_middleware(RateLimitMiddleware, max_requests=10, window_seconds=60)
        app.add_middleware(RequestValidationMiddleware, validate_enum_params=True)

        @app.post("/api/v1/check-compliance")
        async def test_endpoint(request: Request):
            return {"message": "success"}

        @app.get("/api/v1/health")
        async def health_endpoint():
            return {"status": "healthy"}

        return app

    @pytest.fixture
    def client_with_all_middleware(self, app_with_all_middleware):
        """带所有中间件的测试客户端"""
        return TestClient(app_with_all_middleware)

    def test_all_middleware_working_together(self, client_with_all_middleware):
        """测试所有中间件协同工作"""
        valid_request = {
            "bidding_doc": {
                "filename": "test.docx",
                "extension": ".docx",
                "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "size": 1024,
                "url": "http://example.com/test.docx",
            },
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
        }

        response = client_with_all_middleware.post(
            "/api/v1/check-compliance", json=valid_request
        )

        assert response.status_code == 200

        # 检查安全头
        assert "X-Content-Type-Options" in response.headers

        # 检查限流头
        assert "X-RateLimit-Limit" in response.headers

        # 检查响应内容
        assert response.json()["message"] == "success"
