#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不依赖AI模型的功能
"""

import requests
import json


def test_components_debug():
    """测试组件调试端点"""
    print("测试组件调试端点")
    print("=" * 40)

    try:
        response = requests.get(
            "http://localhost:8088/api/v1/debug/components", timeout=10
        )

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("组件状态:")

            debug_results = data.get("debug_results", {})
            for component, info in debug_results.items():
                status = info.get("status", "unknown")
                print(f"  {component}: {status}")

                if status == "error":
                    print(f"    错误: {info.get('error', 'N/A')}")

            return debug_results
        else:
            print(f"调试端点失败: {response.text}")
            return None

    except Exception as e:
        print(f"调试端点测试失败: {str(e)}")
        return None


def main():
    """主函数"""
    print("测试系统组件状态")
    print("=" * 50)

    # 测试调试端点
    debug_results = test_components_debug()

    if debug_results:
        print("\n分析:")

        ai_status = debug_results.get("ai_model", {}).get("status")
        sensitive_status = debug_results.get("sensitive_word", {}).get("status")

        if ai_status == "error":
            print("❌ AI模型服务有问题 - 这可能是超时的主要原因")
            print("   建议: 检查 http://localhost:3002 是否运行")

        if sensitive_status == "error":
            print("❌ 敏感词服务有问题")
            print("   建议: 检查网络连接到敏感词API")

        if ai_status == "ok" and sensitive_status == "ok":
            print("✅ 主要组件都正常，问题可能在文件处理或其他地方")


if __name__ == "__main__":
    main()
