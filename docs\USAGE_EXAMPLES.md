# 招标文件合规性检查助手 - 使用示例

## 概述

本文档提供招标文件合规性检查助手的详细使用示例，包括API调用、错误处理、最佳实践等内容。

## 快速开始

### 1. 基础健康检查

```bash
curl http://localhost:8088/health
```

响应示例：
```json
{
  "status": "healthy",
  "service": "bidding-document-compliance-checker",
  "version": "1.0.0"
}
```

### 2. 获取枚举值

```bash
curl http://localhost:8088/api/v1/enums
```

响应示例：
```json
{
  "message": "枚举值列表",
  "data": {
    "procurement_project_type": ["工程类", "服务类", "货物类"],
    "project_category": ["依法招标", "非依法招标", "政府采购"],
    "bidding_procurement_method": [
      "公开招标", "单一来源", "竞争性磋商", "竞争性磋商邀请",
      "邀请招标", "竞争性谈判", "公开竞价", "邀请竞价",
      "询价", "其他", "比选"
    ]
  }
}
```

## API调用示例

### 1. 简化接口调用（推荐）

#### cURL示例

```bash
curl -X POST "http://localhost:8088/api/v1/check-compliance-simple" \
  -H "Content-Type: application/json" \
  -d '{
    "procurement_project_type": "服务类",
    "project_category": "政府采购",
    "bidding_procurement_method": "公开招标",
    "file_url": "https://example.com/bidding-document.docx"
  }'
```

#### Python示例

```python
import requests
import json

def check_compliance_simple(file_url, project_type="服务类", 
                          category="政府采购", method="公开招标"):
    """
    使用简化接口检查合规性
    
    Args:
        file_url: 文件下载URL
        project_type: 采购项目类型
        category: 项目类别
        method: 招标采购方式
    
    Returns:
        dict: 检查结果
    """
    
    url = "http://localhost:8088/api/v1/check-compliance-simple"
    
    data = {
        "procurement_project_type": project_type,
        "project_category": category,
        "bidding_procurement_method": method,
        "file_url": file_url
    }
    
    try:
        response = requests.post(url, json=data, timeout=300)
        response.raise_for_status()
        
        result = response.json()
        
        print(f"✅ 检查完成")
        print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
        print(f"检查结果数量: {len(result.get('checkResultArr', []))}")
        
        return result
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 处理异常: {e}")
        return None

# 使用示例
result = check_compliance_simple(
    file_url="https://example.com/bidding-document.docx",
    project_type="服务类",
    category="政府采购",
    method="公开招标"
)

if result:
    # 处理敏感词结果
    for item in result.get('sensitiveWordsArr', []):
        print(f"敏感词: {item['content']} (类型: {item['type']}, 出现: {item['num']}次)")
    
    # 处理检查结果
    for item in result.get('checkResultArr', []):
        print(f"问题: {item['quesDesc']} (类型: {item['quesType']})")
        print(f"建议: {item['advice']}")
```

#### JavaScript示例

```javascript
async function checkComplianceSimple(fileUrl, options = {}) {
    const {
        projectType = '服务类',
        category = '政府采购',
        method = '公开招标'
    } = options;
    
    const url = 'http://localhost:8088/api/v1/check-compliance-simple';
    
    const data = {
        procurement_project_type: projectType,
        project_category: category,
        bidding_procurement_method: method,
        file_url: fileUrl
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        console.log('✅ 检查完成');
        console.log(`敏感词数量: ${result.sensitiveWordsArr?.length || 0}`);
        console.log(`检查结果数量: ${result.checkResultArr?.length || 0}`);
        
        return result;
        
    } catch (error) {
        console.error('❌ 请求失败:', error);
        return null;
    }
}

// 使用示例
checkComplianceSimple('https://example.com/bidding-document.docx', {
    projectType: '服务类',
    category: '政府采购',
    method: '公开招标'
}).then(result => {
    if (result) {
        // 处理结果
        result.sensitiveWordsArr?.forEach(item => {
            console.log(`敏感词: ${item.content} (${item.type}, ${item.num}次)`);
        });
        
        result.checkResultArr?.forEach(item => {
            console.log(`问题: ${item.quesDesc} (${item.quesType})`);
        });
    }
});
```

### 2. 标准接口调用

#### Python示例

```python
import requests
from urllib.parse import urlparse
import os

def check_compliance_standard(file_url, project_type="服务类", 
                            category="政府采购", method="公开招标"):
    """
    使用标准接口检查合规性
    """
    
    # 构建文件信息对象
    parsed_url = urlparse(file_url)
    filename = os.path.basename(parsed_url.path) or "document.docx"
    
    # 获取文件大小（可选）
    try:
        head_response = requests.head(file_url, timeout=10)
        file_size = int(head_response.headers.get('content-length', 1024*1024))
    except:
        file_size = 1024 * 1024  # 默认1MB
    
    # 确定文件类型
    if filename.lower().endswith('.pdf'):
        extension = ".pdf"
        mime_type = "application/pdf"
    else:
        extension = ".docx"
        mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    
    url = "http://localhost:8088/api/v1/check-compliance"
    
    data = {
        "procurement_project_type": project_type,
        "project_category": category,
        "bidding_procurement_method": method,
        "bidding_doc": {
            "filename": filename,
            "extension": extension,
            "mime_type": mime_type,
            "size": file_size,
            "url": file_url
        }
    }
    
    try:
        response = requests.post(url, json=data, timeout=300)
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None

# 使用示例
result = check_compliance_standard("https://example.com/bidding-document.docx")
```

## 批量处理示例

### Python批量处理脚本

```python
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict

class ComplianceChecker:
    """合规性检查器"""
    
    def __init__(self, base_url="http://localhost:8088"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def check_single_file(self, file_info: Dict) -> Dict:
        """检查单个文件"""
        
        url = f"{self.base_url}/api/v1/check-compliance-simple"
        
        data = {
            "procurement_project_type": file_info.get("project_type", "服务类"),
            "project_category": file_info.get("category", "政府采购"),
            "bidding_procurement_method": file_info.get("method", "公开招标"),
            "file_url": file_info["file_url"]
        }
        
        try:
            response = self.session.post(url, json=data, timeout=300)
            response.raise_for_status()
            
            result = response.json()
            
            return {
                "file_url": file_info["file_url"],
                "success": True,
                "result": result,
                "sensitive_words_count": len(result.get("sensitiveWordsArr", [])),
                "check_results_count": len(result.get("checkResultArr", []))
            }
            
        except Exception as e:
            return {
                "file_url": file_info["file_url"],
                "success": False,
                "error": str(e),
                "sensitive_words_count": 0,
                "check_results_count": 0
            }
    
    def check_multiple_files(self, file_list: List[Dict], max_workers=3) -> List[Dict]:
        """批量检查多个文件"""
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(self.check_single_file, file_info): file_info
                for file_info in file_list
            }
            
            # 收集结果
            for future in as_completed(future_to_file):
                file_info = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result["success"]:
                        print(f"✅ {file_info['file_url']}: "
                              f"敏感词{result['sensitive_words_count']}个, "
                              f"问题{result['check_results_count']}个")
                    else:
                        print(f"❌ {file_info['file_url']}: {result['error']}")
                        
                except Exception as e:
                    print(f"❌ {file_info['file_url']}: 处理异常 {e}")
                    results.append({
                        "file_url": file_info["file_url"],
                        "success": False,
                        "error": str(e)
                    })
        
        return results
    
    def generate_report(self, results: List[Dict], output_file="compliance_report.json"):
        """生成检查报告"""
        
        # 统计信息
        total_files = len(results)
        successful_files = sum(1 for r in results if r["success"])
        failed_files = total_files - successful_files
        
        total_sensitive_words = sum(r.get("sensitive_words_count", 0) for r in results)
        total_check_results = sum(r.get("check_results_count", 0) for r in results)
        
        report = {
            "summary": {
                "total_files": total_files,
                "successful_files": successful_files,
                "failed_files": failed_files,
                "success_rate": successful_files / total_files * 100 if total_files > 0 else 0,
                "total_sensitive_words": total_sensitive_words,
                "total_check_results": total_check_results
            },
            "results": results,
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 报告已生成: {output_file}")
        print(f"📊 统计: {successful_files}/{total_files} 成功, "
              f"敏感词{total_sensitive_words}个, 问题{total_check_results}个")
        
        return report

# 使用示例
if __name__ == "__main__":
    checker = ComplianceChecker()
    
    # 定义要检查的文件列表
    files_to_check = [
        {
            "file_url": "https://example.com/doc1.docx",
            "project_type": "服务类",
            "category": "政府采购",
            "method": "公开招标"
        },
        {
            "file_url": "https://example.com/doc2.pdf",
            "project_type": "工程类",
            "category": "依法招标",
            "method": "邀请招标"
        },
        {
            "file_url": "https://example.com/doc3.docx",
            "project_type": "货物类",
            "category": "政府采购",
            "method": "竞争性磋商"
        }
    ]
    
    # 批量检查
    results = checker.check_multiple_files(files_to_check, max_workers=2)
    
    # 生成报告
    report = checker.generate_report(results)
```

## 错误处理示例

### 完整的错误处理

```python
import requests
import json
import time
from typing import Optional, Dict, Any

class ComplianceAPIClient:
    """合规性检查API客户端"""
    
    def __init__(self, base_url="http://localhost:8088", timeout=300, max_retries=3):
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
    
    def check_compliance(self, file_url: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """
        检查合规性（带完整错误处理）
        
        Args:
            file_url: 文件URL
            **kwargs: 其他参数
            
        Returns:
            检查结果或None
        """
        
        data = {
            "procurement_project_type": kwargs.get("project_type", "服务类"),
            "project_category": kwargs.get("category", "政府采购"),
            "bidding_procurement_method": kwargs.get("method", "公开招标"),
            "file_url": file_url
        }
        
        url = f"{self.base_url}/api/v1/check-compliance-simple"
        
        for attempt in range(self.max_retries):
            try:
                print(f"🔄 尝试 {attempt + 1}/{self.max_retries}: {file_url}")
                
                response = self.session.post(url, json=data, timeout=self.timeout)
                
                # 处理HTTP错误
                if response.status_code == 400:
                    error_data = response.json()
                    print(f"❌ 请求参数错误: {error_data.get('message', '未知错误')}")
                    return None
                elif response.status_code == 422:
                    error_data = response.json()
                    print(f"❌ 参数验证失败:")
                    for detail in error_data.get('detail', []):
                        print(f"   - {detail.get('msg', '')}")
                    return None
                elif response.status_code == 429:
                    print(f"⏳ 请求频率限制，等待重试...")
                    time.sleep(60)  # 等待1分钟
                    continue
                elif response.status_code == 500:
                    print(f"❌ 服务器内部错误，尝试重试...")
                    time.sleep(5)
                    continue
                elif response.status_code != 200:
                    print(f"❌ HTTP错误 {response.status_code}: {response.text}")
                    time.sleep(2)
                    continue
                
                # 解析响应
                result = response.json()
                
                # 验证响应格式
                if not isinstance(result, dict):
                    print(f"❌ 响应格式错误: 期望dict，得到{type(result)}")
                    return None
                
                if 'sensitiveWordsArr' not in result or 'checkResultArr' not in result:
                    print(f"❌ 响应缺少必需字段")
                    return None
                
                print(f"✅ 检查完成: 敏感词{len(result['sensitiveWordsArr'])}个, "
                      f"问题{len(result['checkResultArr'])}个")
                
                return result
                
            except requests.exceptions.Timeout:
                print(f"⏰ 请求超时 ({self.timeout}秒)")
                if attempt < self.max_retries - 1:
                    time.sleep(5)
                    continue
                else:
                    print(f"❌ 超时重试次数已用完")
                    return None
                    
            except requests.exceptions.ConnectionError:
                print(f"🔌 连接错误，检查服务是否运行")
                if attempt < self.max_retries - 1:
                    time.sleep(10)
                    continue
                else:
                    print(f"❌ 连接重试次数已用完")
                    return None
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求异常: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return None
                    
            except json.JSONDecodeError:
                print(f"❌ 响应JSON解析失败")
                return None
                
            except Exception as e:
                print(f"❌ 未知异常: {e}")
                return None
        
        print(f"❌ 所有重试都失败了")
        return None
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200
        except:
            return False

# 使用示例
client = ComplianceAPIClient()

# 检查服务健康状态
if not client.health_check():
    print("❌ 服务不可用，请检查服务状态")
    exit(1)

# 检查合规性
result = client.check_compliance(
    file_url="https://example.com/bidding-document.docx",
    project_type="服务类",
    category="政府采购",
    method="公开招标"
)

if result:
    print("🎉 检查成功完成")
else:
    print("💥 检查失败")
```

## 性能优化示例

### 异步处理

```python
import asyncio
import aiohttp
import json
from typing import List, Dict

class AsyncComplianceChecker:
    """异步合规性检查器"""
    
    def __init__(self, base_url="http://localhost:8088", max_concurrent=5):
        self.base_url = base_url
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def check_single_file(self, session: aiohttp.ClientSession, 
                               file_info: Dict) -> Dict:
        """异步检查单个文件"""
        
        async with self.semaphore:  # 限制并发数
            url = f"{self.base_url}/api/v1/check-compliance-simple"
            
            data = {
                "procurement_project_type": file_info.get("project_type", "服务类"),
                "project_category": file_info.get("category", "政府采购"),
                "bidding_procurement_method": file_info.get("method", "公开招标"),
                "file_url": file_info["file_url"]
            }
            
            try:
                async with session.post(url, json=data, timeout=300) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "file_url": file_info["file_url"],
                            "success": True,
                            "result": result
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "file_url": file_info["file_url"],
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
            except Exception as e:
                return {
                    "file_url": file_info["file_url"],
                    "success": False,
                    "error": str(e)
                }
    
    async def check_multiple_files(self, file_list: List[Dict]) -> List[Dict]:
        """异步批量检查"""
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.check_single_file(session, file_info)
                for file_info in file_list
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "file_url": file_list[i]["file_url"],
                        "success": False,
                        "error": str(result)
                    })
                else:
                    processed_results.append(result)
            
            return processed_results

# 使用示例
async def main():
    checker = AsyncComplianceChecker(max_concurrent=3)
    
    files = [
        {"file_url": f"https://example.com/doc{i}.docx"}
        for i in range(1, 11)  # 10个文件
    ]
    
    print("🚀 开始异步批量检查...")
    start_time = asyncio.get_event_loop().time()
    
    results = await checker.check_multiple_files(files)
    
    end_time = asyncio.get_event_loop().time()
    
    successful = sum(1 for r in results if r["success"])
    print(f"✅ 完成: {successful}/{len(results)} 成功")
    print(f"⏱️  耗时: {end_time - start_time:.2f}秒")

# 运行异步示例
# asyncio.run(main())
```

## 监控和调试

### 请求日志记录

```python
import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('compliance_client.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class LoggingComplianceClient:
    """带日志记录的合规性检查客户端"""
    
    def __init__(self, base_url="http://localhost:8088"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def check_compliance(self, file_url: str, **kwargs) -> dict:
        """检查合规性（带日志记录）"""
        
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        logger.info(f"[{request_id}] 开始检查: {file_url}")
        
        data = {
            "procurement_project_type": kwargs.get("project_type", "服务类"),
            "project_category": kwargs.get("category", "政府采购"),
            "bidding_procurement_method": kwargs.get("method", "公开招标"),
            "file_url": file_url
        }
        
        url = f"{self.base_url}/api/v1/check-compliance-simple"
        
        try:
            start_time = datetime.now()
            
            logger.debug(f"[{request_id}] 发送请求: {url}")
            logger.debug(f"[{request_id}] 请求数据: {data}")
            
            response = self.session.post(url, json=data, timeout=300)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"[{request_id}] 响应状态: {response.status_code}, 耗时: {duration:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                
                sensitive_count = len(result.get('sensitiveWordsArr', []))
                check_count = len(result.get('checkResultArr', []))
                
                logger.info(f"[{request_id}] 检查完成: 敏感词{sensitive_count}个, 问题{check_count}个")
                
                return result
            else:
                logger.error(f"[{request_id}] 请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"[{request_id}] 异常: {e}")
            return None

# 使用示例
client = LoggingComplianceClient()
result = client.check_compliance("https://example.com/test.docx")
```

## 最佳实践

### 1. 参数验证

```python
def validate_parameters(project_type, category, method):
    """验证参数"""
    
    valid_project_types = ["工程类", "服务类", "货物类"]
    valid_categories = ["依法招标", "非依法招标", "政府采购"]
    valid_methods = [
        "公开招标", "单一来源", "竞争性磋商", "竞争性磋商邀请",
        "邀请招标", "竞争性谈判", "公开竞价", "邀请竞价",
        "询价", "其他", "比选"
    ]
    
    if project_type not in valid_project_types:
        raise ValueError(f"无效的项目类型: {project_type}")
    
    if category not in valid_categories:
        raise ValueError(f"无效的项目类别: {category}")
    
    if method not in valid_methods:
        raise ValueError(f"无效的招标方式: {method}")
    
    return True
```

### 2. 结果处理

```python
def process_compliance_result(result):
    """处理合规性检查结果"""
    
    if not result:
        return None
    
    # 分类敏感词
    sensitive_words_by_type = {}
    for item in result.get('sensitiveWordsArr', []):
        word_type = item.get('type', '未知')
        if word_type not in sensitive_words_by_type:
            sensitive_words_by_type[word_type] = []
        sensitive_words_by_type[word_type].append(item)
    
    # 分类检查结果
    issues_by_type = {}
    for item in result.get('checkResultArr', []):
        issue_type = item.get('quesType', '未知')
        if issue_type not in issues_by_type:
            issues_by_type[issue_type] = []
        issues_by_type[issue_type].append(item)
    
    return {
        'sensitive_words_by_type': sensitive_words_by_type,
        'issues_by_type': issues_by_type,
        'total_sensitive_words': len(result.get('sensitiveWordsArr', [])),
        'total_issues': len(result.get('checkResultArr', []))
    }
```

### 3. 缓存机制

```python
import hashlib
import json
import os
from datetime import datetime, timedelta

class CachedComplianceChecker:
    """带缓存的合规性检查器"""
    
    def __init__(self, cache_dir="cache", cache_ttl_hours=24):
        self.cache_dir = cache_dir
        self.cache_ttl = timedelta(hours=cache_ttl_hours)
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_key(self, file_url, project_type, category, method):
        """生成缓存键"""
        data = f"{file_url}_{project_type}_{category}_{method}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{cache_key}.json")
    
    def _is_cache_valid(self, cache_path):
        """检查缓存是否有效"""
        if not os.path.exists(cache_path):
            return False
        
        mtime = datetime.fromtimestamp(os.path.getmtime(cache_path))
        return datetime.now() - mtime < self.cache_ttl
    
    def check_compliance(self, file_url, **kwargs):
        """检查合规性（带缓存）"""
        
        project_type = kwargs.get("project_type", "服务类")
        category = kwargs.get("category", "政府采购")
        method = kwargs.get("method", "公开招标")
        
        # 生成缓存键
        cache_key = self._get_cache_key(file_url, project_type, category, method)
        cache_path = self._get_cache_path(cache_key)
        
        # 检查缓存
        if self._is_cache_valid(cache_path):
            print(f"📦 使用缓存结果: {cache_key}")
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # 调用API
        print(f"🌐 调用API: {file_url}")
        # 这里调用实际的API...
        result = self._call_api(file_url, **kwargs)
        
        # 保存缓存
        if result:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"💾 结果已缓存: {cache_key}")
        
        return result
    
    def _call_api(self, file_url, **kwargs):
        """实际的API调用"""
        # 实现API调用逻辑...
        pass
```

---

以上示例涵盖了招标文件合规性检查助手的主要使用场景。根据实际需求选择合适的调用方式和错误处理策略。