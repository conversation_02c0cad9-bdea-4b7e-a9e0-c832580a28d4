#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用的docx文件
"""

import os
from docx import Document


def create_test_docx():
    """创建一个简单的测试docx文件"""

    # 创建文档
    doc = Document()

    # 添加标题
    doc.add_heading("招标文件测试样本", 0)

    # 添加段落
    doc.add_paragraph("这是一个用于测试的招标文件样本。")

    # 添加项目信息
    doc.add_heading("项目信息", level=1)
    doc.add_paragraph(
        "项目名称：南区汽水厂、生活垃圾一体化处置服务项目管理服务项目（发包版）"
    )
    doc.add_paragraph("采购项目类型：服务类")
    doc.add_paragraph("项目类别：政府采购")
    doc.add_paragraph("招标采购方式：公开招标")

    # 添加技术要求
    doc.add_heading("技术要求", level=1)
    doc.add_paragraph("1. 服务商应具备相关资质")
    doc.add_paragraph("2. 服务期限为一年")
    doc.add_paragraph("3. 服务范围包括设备维护和运营管理")

    # 添加评分标准
    doc.add_heading("评分标准", level=1)

    # 创建表格
    table = doc.add_table(rows=1, cols=3)
    table.style = "Table Grid"

    # 添加表头
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = "评分项目"
    hdr_cells[1].text = "分值"
    hdr_cells[2].text = "评分标准"

    # 添加评分项目
    row_cells = table.add_row().cells
    row_cells[0].text = "技术方案"
    row_cells[1].text = "40分"
    row_cells[2].text = "技术方案完整性和可行性"

    row_cells = table.add_row().cells
    row_cells[0].text = "商务报价"
    row_cells[1].text = "30分"
    row_cells[2].text = "价格合理性评估"

    row_cells = table.add_row().cells
    row_cells[0].text = "企业资质"
    row_cells[1].text = "30分"
    row_cells[2].text = "企业资质和业绩评价"

    # 添加投标要求
    doc.add_heading("投标要求", level=1)
    doc.add_paragraph("1. 投标人应具有独立法人资格")
    doc.add_paragraph("2. 投标人应具有相关行业经验")
    doc.add_paragraph("3. 投标文件应包含完整的技术方案")

    # 保存文件
    test_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(test_dir, "test_bidding_document.docx")

    doc.save(file_path)

    print(f"测试docx文件已创建: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path)} 字节")

    return file_path


if __name__ == "__main__":
    create_test_docx()
