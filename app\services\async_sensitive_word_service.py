# -*- coding: utf-8 -*-
"""
异步敏感词检测服务
提供异步的敏感词检测功能，提升并发性能
"""

import asyncio
import json
from typing import List, Optional, Dict, Any
import aiohttp
from aiohttp import ClientTimeout, ClientSession

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.core.error_logger import error_logger
from app.models.schemas import (
    SensitiveWordItem,
    SensitiveWordRequest,
    SensitiveWordDetectionResponse,
    ProjectInfo,
)
from app.core.exceptions import ExternalServiceError


class AsyncSensitiveWordService:
    """异步敏感词检测服务"""

    def __init__(self):
        """初始化异步敏感词检测服务"""
        self.api_config = {
            "base_url": settings.sensitive_word_api_url,
            "timeout": settings.sensitive_word_api_timeout,
            "max_retries": settings.sensitive_word_api_retries,
        }
        self.service_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_words_detected": 0,
            "average_response_time": 0.0,
        }

    @log_function_call
    async def detect_sensitive_words(
        self,
        content: str,
        project_info: ProjectInfo,
        request_id: str = "",
        timeout: float = None,
    ) -> List[SensitiveWordItem]:
        """
        异步检测敏感词

        Args:
            content: 要检测的内容
            project_info: 项目信息
            request_id: 请求ID
            timeout: 超时时间（秒）

        Returns:
            List[SensitiveWordItem]: 敏感词列表
        """
        if timeout is None:
            timeout = self.api_config["timeout"]

        self.service_stats["total_requests"] += 1
        start_time = asyncio.get_event_loop().time()

        try:
            with TimingContext("异步敏感词检测", request_id):
                # 判断是否为政府采购
                is_government_procurement = self._is_government_procurement(
                    project_info
                )

                # 构建请求数据
                request_data = {
                    "content": content,
                    "is_government_procurement": is_government_procurement,
                }

                log.info(
                    f"开始异步敏感词检测 | ID: {request_id} | "
                    f"内容长度: {len(content)} 字符 | "
                    f"政府采购: {is_government_procurement}"
                )

                # 异步调用API
                response_data = await self._call_api_async(
                    "/detect", request_data, timeout, request_id
                )

                # 解析响应
                sensitive_words = self._parse_response(response_data, request_id)

                # 更新统计信息
                duration = asyncio.get_event_loop().time() - start_time
                self._update_success_stats(duration, len(sensitive_words))

                log.info(
                    f"异步敏感词检测完成 | ID: {request_id} | "
                    f"发现敏感词: {len(sensitive_words)}个 | 耗时: {duration:.3f}秒"
                )

                return sensitive_words

        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self._update_failure_stats(duration)

            if isinstance(e, ExternalServiceError):
                raise
            else:
                raise ExternalServiceError(
                    f"异步敏感词检测失败: {str(e)}",
                    service_name="sensitive_word_api",
                    original_error=e,
                )

    async def detect_with_fallback(
        self,
        content: str,
        project_info: ProjectInfo,
        request_id: str = "",
        timeout: float = None,
    ) -> List[SensitiveWordItem]:
        """
        带降级机制的异步敏感词检测

        Args:
            content: 要检测的内容
            project_info: 项目信息
            request_id: 请求ID
            timeout: 超时时间（秒）

        Returns:
            List[SensitiveWordItem]: 敏感词列表，失败时返回空列表
        """
        try:
            return await self.detect_sensitive_words(
                content, project_info, request_id, timeout
            )
        except Exception as e:
            log.warning(
                f"敏感词检测失败，使用降级策略 | ID: {request_id} | 错误: {str(e)}"
            )
            return []

    async def _call_api_async(
        self,
        endpoint: str,
        data: Dict[str, Any],
        timeout: float,
        request_id: str = "",
    ) -> Dict[str, Any]:
        """
        异步调用敏感词API

        Args:
            endpoint: API端点
            data: 请求数据
            timeout: 超时时间
            request_id: 请求ID

        Returns:
            Dict[str, Any]: API响应数据
        """
        url = f"{self.api_config['base_url']}{endpoint}"

        # 配置超时
        client_timeout = ClientTimeout(total=timeout)

        # 配置重试
        max_retries = self.api_config["max_retries"]

        for attempt in range(max_retries + 1):
            try:
                async with ClientSession(timeout=client_timeout) as session:
                    async with session.post(
                        url,
                        json=data,
                        headers={"Content-Type": "application/json"},
                    ) as response:
                        if response.status == 200:
                            response_data = await response.json()
                            log.debug(
                                f"敏感词API调用成功 | ID: {request_id} | "
                                f"尝试: {attempt + 1}/{max_retries + 1}"
                            )
                            return response_data
                        else:
                            error_text = await response.text()
                            raise ExternalServiceError(
                                f"敏感词API返回错误状态: {response.status}",
                                service_name="sensitive_word_api",
                                status_code=response.status,
                                response_data=error_text,
                            )

            except asyncio.TimeoutError:
                if attempt < max_retries:
                    wait_time = 2**attempt  # 指数退避
                    log.warning(
                        f"敏感词API超时，{wait_time}秒后重试 | ID: {request_id} | "
                        f"尝试: {attempt + 1}/{max_retries + 1}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise ExternalServiceError(
                        f"敏感词API调用超时（{timeout}秒）",
                        service_name="sensitive_word_api",
                        api_url=url,
                    )

            except Exception as e:
                if attempt < max_retries:
                    wait_time = 2**attempt
                    log.warning(
                        f"敏感词API调用失败，{wait_time}秒后重试 | ID: {request_id} | "
                        f"尝试: {attempt + 1}/{max_retries + 1} | 错误: {str(e)}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise ExternalServiceError(
                        f"敏感词API调用失败: {str(e)}",
                        service_name="sensitive_word_api",
                        api_url=url,
                        original_error=e,
                    )

    def _is_government_procurement(self, project_info: ProjectInfo) -> bool:
        """判断是否为政府采购"""
        from app.models.enums import ProjectCategory

        return project_info.project_category == ProjectCategory.GOVERNMENT_PROCUREMENT

    def _parse_response(
        self, response_data: Dict[str, Any], request_id: str = ""
    ) -> List[SensitiveWordItem]:
        """解析API响应"""
        try:
            if not response_data.get("success", False):
                log.warning(
                    f"敏感词API返回失败状态 | ID: {request_id} | "
                    f"消息: {response_data.get('message', '未知错误')}"
                )
                return []

            results = response_data.get("results", [])
            sensitive_words = []

            for item in results:
                try:
                    # 转换API响应格式到内部格式
                    sensitive_word = SensitiveWordItem(
                        type=item.get("敏感词类型", "未知"),
                        content=item.get("敏感词内容", ""),
                        num=item.get("出现次数", 0),
                    )
                    sensitive_words.append(sensitive_word)
                except Exception as e:
                    log.warning(
                        f"解析敏感词项失败 | ID: {request_id} | 错误: {str(e)} | 数据: {item}"
                    )
                    continue

            log.debug(
                f"敏感词响应解析成功 | ID: {request_id} | "
                f"总数: {response_data.get('total_words', 0)} | "
                f"解析成功: {len(sensitive_words)}个"
            )

            return sensitive_words

        except Exception as e:
            log.error(f"敏感词响应解析异常 | ID: {request_id} | 错误: {str(e)}")
            return []

    async def check_health(self, request_id: str = "") -> bool:
        """异步健康检查"""
        try:
            url = f"{self.api_config['base_url']}/health"
            timeout = ClientTimeout(total=5.0)  # 5秒超时

            async with ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    return response.status == 200

        except Exception as e:
            log.warning(f"敏感词服务健康检查失败 | ID: {request_id} | 错误: {str(e)}")
            return False

    async def get_stats(self, request_id: str = "") -> Optional[Dict[str, Any]]:
        """异步获取统计信息"""
        try:
            url = f"{self.api_config['base_url']}/stats"
            timeout = ClientTimeout(total=5.0)

            async with ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return None

        except Exception as e:
            log.warning(f"获取敏感词服务统计失败 | ID: {request_id} | 错误: {str(e)}")
            return None

    async def reload_sensitive_words(self, request_id: str = "") -> bool:
        """异步重载敏感词库"""
        try:
            url = f"{self.api_config['base_url']}/reload"
            timeout = ClientTimeout(total=30.0)  # 重载可能需要更长时间

            async with ClientSession(timeout=timeout) as session:
                async with session.post(url) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success", False)
                    else:
                        return False

        except Exception as e:
            log.error(f"重载敏感词库失败 | ID: {request_id} | 错误: {str(e)}")
            return False

    def _update_success_stats(self, duration: float, words_count: int):
        """更新成功统计"""
        self.service_stats["successful_requests"] += 1
        self.service_stats["total_words_detected"] += words_count

        # 更新平均响应时间
        total_requests = self.service_stats["total_requests"]
        current_avg = self.service_stats["average_response_time"]
        self.service_stats["average_response_time"] = (
            current_avg * (total_requests - 1) + duration
        ) / total_requests

    def _update_failure_stats(self, duration: float):
        """更新失败统计"""
        self.service_stats["failed_requests"] += 1

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        success_rate = 0.0
        if self.service_stats["total_requests"] > 0:
            success_rate = (
                self.service_stats["successful_requests"]
                / self.service_stats["total_requests"]
            )

        return {
            "service_type": "async_sensitive_word",
            "api_base_url": self.api_config["base_url"],
            "timeout": self.api_config["timeout"],
            "max_retries": self.api_config["max_retries"],
            "stats": {
                **self.service_stats,
                "success_rate": success_rate,
                "average_words_per_request": (
                    self.service_stats["total_words_detected"]
                    / max(self.service_stats["successful_requests"], 1)
                ),
            },
        }


# 创建全局异步敏感词检测服务实例
async_sensitive_word_service = AsyncSensitiveWordService()
