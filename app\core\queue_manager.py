# -*- coding: utf-8 -*-
"""
请求队列管理器
实现请求队列和并发控制功能
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import uuid

from app.core.logger import log, log_function_call
from app.core.config import settings


class RequestPriority(Enum):
    """请求优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class RequestStatus(Enum):
    """请求状态"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class QueuedRequest:
    """队列中的请求"""

    request_id: str
    priority: RequestPriority
    created_at: float
    handler: Callable[..., Awaitable[Any]]
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    status: RequestStatus = RequestStatus.PENDING
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Any = None
    error: Optional[Exception] = None
    timeout_seconds: float = 300.0  # 5分钟默认超时

    def __post_init__(self):
        """初始化后处理"""
        if not self.request_id:
            self.request_id = str(uuid.uuid4())

    @property
    def processing_time(self) -> Optional[float]:
        """获取处理时间"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None

    @property
    def wait_time(self) -> Optional[float]:
        """获取等待时间"""
        if self.started_at:
            return self.started_at - self.created_at
        return None

    @property
    def is_expired(self) -> bool:
        """检查是否超时"""
        return time.time() - self.created_at > self.timeout_seconds


class RequestQueue:
    """请求队列管理器"""

    def __init__(self, max_concurrent: int = 10, max_queue_size: int = 100):
        """
        初始化请求队列

        Args:
            max_concurrent: 最大并发处理数
            max_queue_size: 最大队列大小
        """
        self.max_concurrent = max_concurrent
        self.max_queue_size = max_queue_size
        self.queue: asyncio.PriorityQueue = asyncio.PriorityQueue(
            maxsize=max_queue_size
        )
        self.processing: Dict[str, QueuedRequest] = {}
        self.completed: Dict[str, QueuedRequest] = {}
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.stats = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "cancelled_requests": 0,
            "current_queue_size": 0,
            "current_processing": 0,
            "average_wait_time": 0.0,
            "average_processing_time": 0.0,
        }
        self._worker_tasks: list = []
        self._shutdown = False

    async def start_workers(self, num_workers: Optional[int] = None):
        """启动工作线程"""
        if num_workers is None:
            num_workers = min(self.max_concurrent, 4)

        log.info(f"启动请求队列工作线程: {num_workers}个")

        for i in range(num_workers):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self._worker_tasks.append(task)

    async def stop_workers(self):
        """停止工作线程"""
        log.info("停止请求队列工作线程")
        self._shutdown = True

        # 取消所有工作任务
        for task in self._worker_tasks:
            task.cancel()

        # 等待任务完成
        if self._worker_tasks:
            await asyncio.gather(*self._worker_tasks, return_exceptions=True)

        self._worker_tasks.clear()

    @log_function_call
    async def enqueue(
        self,
        handler: Callable[..., Awaitable[Any]],
        *args,
        priority: RequestPriority = RequestPriority.NORMAL,
        request_id: Optional[str] = None,
        timeout_seconds: float = 300.0,
        **kwargs,
    ) -> str:
        """
        将请求加入队列

        Args:
            handler: 处理函数
            *args: 位置参数
            priority: 请求优先级
            request_id: 请求ID
            timeout_seconds: 超时时间
            **kwargs: 关键字参数

        Returns:
            str: 请求ID

        Raises:
            asyncio.QueueFull: 队列已满
        """
        if request_id is None:
            request_id = str(uuid.uuid4())

        request = QueuedRequest(
            request_id=request_id,
            priority=priority,
            created_at=time.time(),
            handler=handler,
            args=args,
            kwargs=kwargs,
            timeout_seconds=timeout_seconds,
        )

        # 优先级队列：数值越小优先级越高
        priority_value = -priority.value  # 反转优先级
        queue_item = (priority_value, time.time(), request)

        try:
            await self.queue.put(queue_item)
            self.stats["total_requests"] += 1
            self.stats["current_queue_size"] = self.queue.qsize()

            log.info(
                f"请求已加入队列 | ID: {request_id} | 优先级: {priority.name} | "
                f"队列大小: {self.queue.qsize()}"
            )

            return request_id

        except asyncio.QueueFull:
            log.error(f"请求队列已满 | ID: {request_id}")
            raise

    @log_function_call
    async def get_request_status(self, request_id: str) -> Optional[Dict[str, Any]]:
        """获取请求状态"""
        # 检查正在处理的请求
        if request_id in self.processing:
            request = self.processing[request_id]
            return {
                "request_id": request_id,
                "status": request.status.value,
                "priority": request.priority.name,
                "created_at": datetime.fromtimestamp(request.created_at).isoformat(),
                "started_at": (
                    datetime.fromtimestamp(request.started_at).isoformat()
                    if request.started_at
                    else None
                ),
                "wait_time": request.wait_time,
                "processing_time": request.processing_time,
            }

        # 检查已完成的请求
        if request_id in self.completed:
            request = self.completed[request_id]
            return {
                "request_id": request_id,
                "status": request.status.value,
                "priority": request.priority.name,
                "created_at": datetime.fromtimestamp(request.created_at).isoformat(),
                "started_at": (
                    datetime.fromtimestamp(request.started_at).isoformat()
                    if request.started_at
                    else None
                ),
                "completed_at": (
                    datetime.fromtimestamp(request.completed_at).isoformat()
                    if request.completed_at
                    else None
                ),
                "wait_time": request.wait_time,
                "processing_time": request.processing_time,
                "error": str(request.error) if request.error else None,
            }

        return None

    @log_function_call
    async def cancel_request(self, request_id: str) -> bool:
        """取消请求"""
        # 只能取消正在处理的请求
        if request_id in self.processing:
            request = self.processing[request_id]
            request.status = RequestStatus.CANCELLED
            request.completed_at = time.time()

            # 移动到已完成列表
            self.completed[request_id] = request
            del self.processing[request_id]

            self.stats["cancelled_requests"] += 1
            self.stats["current_processing"] = len(self.processing)

            log.info(f"请求已取消 | ID: {request_id}")
            return True

        return False

    async def _worker(self, worker_name: str):
        """工作线程"""
        log.info(f"工作线程启动: {worker_name}")

        while not self._shutdown:
            try:
                # 获取队列中的请求（带超时）
                try:
                    priority_value, timestamp, request = await asyncio.wait_for(
                        self.queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # 检查请求是否过期
                if request.is_expired:
                    log.warning(f"请求已过期，跳过处理 | ID: {request.request_id}")
                    request.status = RequestStatus.FAILED
                    request.error = TimeoutError("请求在队列中等待超时")
                    request.completed_at = time.time()
                    self.completed[request.request_id] = request
                    self.stats["failed_requests"] += 1
                    continue

                # 获取信号量（控制并发）
                async with self.semaphore:
                    await self._process_request(request, worker_name)

                # 更新队列统计
                self.stats["current_queue_size"] = self.queue.qsize()

            except asyncio.CancelledError:
                log.info(f"工作线程被取消: {worker_name}")
                break
            except Exception as e:
                log.error(f"工作线程异常 | {worker_name} | 错误: {str(e)}")

        log.info(f"工作线程停止: {worker_name}")

    async def _process_request(self, request: QueuedRequest, worker_name: str):
        """处理单个请求"""
        request.status = RequestStatus.PROCESSING
        request.started_at = time.time()

        # 添加到处理中列表
        self.processing[request.request_id] = request
        self.stats["current_processing"] = len(self.processing)

        log.info(
            f"开始处理请求 | ID: {request.request_id} | 工作线程: {worker_name} | "
            f"等待时间: {request.wait_time:.3f}秒"
        )

        try:
            # 设置超时处理
            result = await asyncio.wait_for(
                request.handler(*request.args, **request.kwargs),
                timeout=request.timeout_seconds,
            )

            request.result = result
            request.status = RequestStatus.COMPLETED
            self.stats["completed_requests"] += 1

            log.info(
                f"请求处理完成 | ID: {request.request_id} | "
                f"处理时间: {request.processing_time:.3f}秒"
            )

        except asyncio.TimeoutError:
            request.error = TimeoutError(f"请求处理超时: {request.timeout_seconds}秒")
            request.status = RequestStatus.FAILED
            self.stats["failed_requests"] += 1

            log.error(
                f"请求处理超时 | ID: {request.request_id} | "
                f"超时时间: {request.timeout_seconds}秒"
            )

        except Exception as e:
            request.error = e
            request.status = RequestStatus.FAILED
            self.stats["failed_requests"] += 1

            log.error(f"请求处理失败 | ID: {request.request_id} | 错误: {str(e)}")

        finally:
            request.completed_at = time.time()

            # 移动到已完成列表
            self.completed[request.request_id] = request
            if request.request_id in self.processing:
                del self.processing[request.request_id]

            self.stats["current_processing"] = len(self.processing)

            # 更新平均时间统计
            self._update_time_stats(request)

    def _update_time_stats(self, request: QueuedRequest):
        """更新时间统计"""
        if request.wait_time is not None:
            current_avg_wait = self.stats["average_wait_time"]
            completed_count = (
                self.stats["completed_requests"] + self.stats["failed_requests"]
            )
            self.stats["average_wait_time"] = (
                current_avg_wait * (completed_count - 1) + request.wait_time
            ) / completed_count

        if request.processing_time is not None:
            current_avg_processing = self.stats["average_processing_time"]
            completed_count = (
                self.stats["completed_requests"] + self.stats["failed_requests"]
            )
            self.stats["average_processing_time"] = (
                current_avg_processing * (completed_count - 1) + request.processing_time
            ) / completed_count

    @log_function_call
    def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        return {
            **self.stats,
            "max_concurrent": self.max_concurrent,
            "max_queue_size": self.max_queue_size,
            "success_rate": (
                self.stats["completed_requests"] / max(self.stats["total_requests"], 1)
            ),
            "failure_rate": (
                self.stats["failed_requests"] / max(self.stats["total_requests"], 1)
            ),
            "queue_utilization": (
                self.stats["current_queue_size"] / self.max_queue_size
            ),
            "processing_utilization": (
                self.stats["current_processing"] / self.max_concurrent
            ),
        }

    @log_function_call
    async def cleanup_completed(self, max_age_hours: int = 24):
        """清理已完成的请求记录"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleanup_count = 0

        # 清理过期的已完成请求
        expired_ids = []
        for request_id, request in self.completed.items():
            if (
                request.completed_at
                and current_time - request.completed_at > max_age_seconds
            ):
                expired_ids.append(request_id)

        for request_id in expired_ids:
            del self.completed[request_id]
            cleanup_count += 1

        if cleanup_count > 0:
            log.info(f"清理过期请求记录: {cleanup_count}个")

        return cleanup_count


# 创建全局请求队列实例
request_queue = RequestQueue(
    max_concurrent=getattr(settings, "max_concurrent_requests", 10),
    max_queue_size=getattr(settings, "max_queue_size", 100),
)
