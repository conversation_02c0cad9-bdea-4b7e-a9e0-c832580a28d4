#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标信息处理功能使用示例
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.bidding_info_processor import bidding_info_processor, BiddingInfoError
from app.services.file_processor_v2 import optimized_file_processor
from app.services.async_file_processor import async_file_processor
from app.models.schemas import FileInfo
from app.models.enums import FileExtension, MimeType


def example_bidding_info_analysis():
    """示例：招标信息分析"""
    print("=== 招标信息分析示例 ===")

    # 模拟招标文件内容
    sample_content = """
    招标公告
    
    项目名称：某某信息化建设项目
    项目编号：ZBGG-2024-001
    
    一、招标人信息
    招标人名称：某某市政府采购中心
    招标人联系人：王主任
    招标人联系电话：0551-87654321
    招标人地址：某某市政务区某某路88号
    招标人邮箱：<EMAIL>
    
    二、项目概况
    本项目为某某市政府信息化建设项目，包括硬件采购、软件开发、系统集成等内容。
    
    三、投标人资格要求
    1. 具有独立承担民事责任的能力
    2. 具有良好的商业信誉和健全的财务会计制度
    3. 具有履行合同所必需的设备和专业技术能力
    """

    try:
        # 分析招标信息
        result = bidding_info_processor.analyze_bidding_info(
            sample_content, "example-001"
        )
        print("提取的招标信息：")
        for key, value in result.items():
            print(f"  {key}: {value}")

        # 演示信息替换
        if any(info.strip() for info in result.values()):
            print("\n=== 信息替换示例 ===")
            replaced_content = bidding_info_processor.replace_bidding_info(
                sample_content, result, "example-001"
            )
            print("替换后的内容（前500字符）：")
            print(
                replaced_content[:500] + "..."
                if len(replaced_content) > 500
                else replaced_content
            )

    except BiddingInfoError as e:
        print(f"招标信息处理错误: {e}")
    except Exception as e:
        print(f"处理异常: {e}")


def example_file_processing_with_bidding_info():
    """示例：文件处理集成招标信息处理"""
    print("\n=== 文件处理集成示例 ===")

    # 创建模拟文件信息
    file_info = FileInfo(
        filename="招标文件示例.pdf",
        url="https://example.com/bidding-document.pdf",  # 这是一个示例URL
        extension=FileExtension.PDF,
        mime_type=MimeType.PDF,
        size=1024 * 1024,  # 1MB
    )

    try:
        # 验证处理能力
        capability = optimized_file_processor.validate_processing_capability(file_info)
        print("处理能力评估：")
        print(f"  可以处理: {capability['can_process']}")
        print(f"  首选方法: {capability.get('preferred_method', 'N/A')}")
        print(f"  预估时间: {capability.get('estimated_time', 0):.2f}秒")

        if capability["warnings"]:
            print("  警告信息:")
            for warning in capability["warnings"]:
                print(f"    - {warning}")

        # 注意：这里不实际处理文件，因为URL是示例
        print("\n注意：实际文件处理需要有效的文件URL")

    except Exception as e:
        print(f"处理能力评估失败: {e}")


async def example_async_file_processing():
    """示例：异步文件处理"""
    print("\n=== 异步文件处理示例 ===")

    # 获取异步处理器统计信息
    stats = async_file_processor.get_processing_stats()
    print("异步处理器状态：")
    print(f"  服务类型: {stats['service_type']}")
    print(f"  MarkItDown可用: {stats['markitdown_available']}")
    print(f"  支持的格式: {', '.join(stats['supported_extensions'])}")
    print(f"  最大文件大小: {stats['max_file_size'] / 1024 / 1024:.0f}MB")

    # 处理统计
    processing_stats = stats["stats"]
    print(f"  已处理文件: {processing_stats['total_files_processed']}")
    print(f"  成功率: {processing_stats['success_rate']:.2%}")
    print(f"  平均处理时间: {processing_stats['average_processing_time']:.3f}秒")


def example_configuration_info():
    """示例：配置信息展示"""
    print("\n=== 配置信息示例 ===")

    # 显示替换规则
    rules = bidding_info_processor.replacement_rules
    print("招标信息替换规则：")
    for field, replacement in rules.items():
        print(f"  {field} -> {replacement}")

    # 显示处理器统计
    stats = optimized_file_processor.get_processing_stats()
    print(f"\n文件处理器配置：")
    print(f"  MarkItDown可用: {stats['markitdown_available']}")
    print(f"  支持格式: {', '.join(stats['supported_formats'])}")
    print(f"  最大文件大小: {stats['max_file_size_mb']:.0f}MB")
    print(f"  请求超时: {stats['request_timeout']}秒")
    print(f"  最大重试次数: {stats['max_retries']}")


def example_error_handling():
    """示例：错误处理"""
    print("\n=== 错误处理示例 ===")

    try:
        # 测试空内容分析
        result = bidding_info_processor.analyze_bidding_info("", "error-test")
        print(f"空内容分析结果: {result}")

    except BiddingInfoError as e:
        print(f"招标信息错误: {e}")
        print(f"  错误阶段: {e.stage}")
        print(f"  错误类型: {e.error_type}")
    except Exception as e:
        print(f"其他错误: {e}")

    try:
        # 测试无效内容替换
        invalid_bidding_info = {"无效字段": "无效值"}
        result = bidding_info_processor.replace_bidding_info(
            "测试内容", invalid_bidding_info, "error-test"
        )
        print(f"无效信息替换结果: 内容长度 {len(result)}")

    except Exception as e:
        print(f"替换错误: {e}")


async def main():
    """主函数"""
    print("招标信息处理功能使用示例")
    print("=" * 60)

    # 基础功能示例
    example_bidding_info_analysis()

    # 文件处理集成示例
    example_file_processing_with_bidding_info()

    # 异步处理示例
    await example_async_file_processing()

    # 配置信息示例
    example_configuration_info()

    # 错误处理示例
    example_error_handling()

    print("\n" + "=" * 60)
    print("示例演示完成")
    print("\n使用说明：")
    print("1. 确保本地大模型服务运行在 http://172.18.10.23:8000/v1")
    print("2. 模型使用 Qwen/Qwen2.5-7B-Instruct")
    print("3. 支持PDF和DOCX格式的招标文件")
    print("4. 自动提取前10页内容进行分析")
    print("5. 自动替换招标单位信息为指定内容")


if __name__ == "__main__":
    asyncio.run(main())
