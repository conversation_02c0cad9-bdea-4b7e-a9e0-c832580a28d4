# -*- coding: utf-8 -*-
"""
API路由定义
"""

from fastapi import APIRouter, Request, Depends
from typing import Dict, Any

from app.models.schemas import (
    ComplianceCheckRequest,
    SimpleComplianceCheckRequest,
    ComplianceCheckResponse,
    HealthCheckResponse,
)
from app.utils.file_info_utils import infer_file_info_from_url, FileInfoInferenceError
from app.core.validators import get_enum_values, ParameterValidator
from app.core.exceptions import api_error_handler
from app.core.logger import log, request_logger, performance_logger
from app.services.compliance_service import compliance_service
from app.services.ai_model_service import ai_model_service
from app.services.sensitive_word_service import sensitive_word_service
from app.services.result_processor import result_processor

# 导入文件处理器（优先使用优化版本）
try:
    from app.services.file_processor_v2 import (
        optimized_file_processor as file_processor,
    )
except ImportError:
    from app.services.file_processor import file_processor

router = APIRouter()


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查接口"""
    return HealthCheckResponse(
        status="healthy", service="bidding-document-compliance-checker", version="1.0.0"
    )


@router.get("/enums")
async def get_enums():
    """获取所有枚举值"""
    return {"message": "枚举值列表", "data": get_enum_values()}


@router.get("/service-status")
async def get_service_status(request: Request):
    """获取服务状态"""
    request_id = getattr(request.state, "request_id", "")

    # 使用集成的合规性服务获取状态
    service_status = compliance_service.get_service_status()

    return {
        "status": "operational",
        "compliance_service": service_status,
        "request_id": request_id,
    }


@router.post("/check-compliance", response_model=ComplianceCheckResponse)
async def check_compliance(
    request: Request, compliance_request: ComplianceCheckRequest
) -> ComplianceCheckResponse:
    """
    招标文件合规性检查主接口

    Args:
        request: FastAPI请求对象
        compliance_request: 合规性检查请求

    Returns:
        ComplianceCheckResponse: 合规性检查结果
    """
    request_id = getattr(request.state, "request_id", "")

    log.info(
        f"开始合规性检查 | ID: {request_id} | 文件: {compliance_request.bidding_doc.filename}"
    )

    try:
        # 1. 验证请求参数
        validated_request = ParameterValidator.validate_compliance_request(
            compliance_request.dict()
        )

        # 2. 使用集成的合规性检查服务
        final_result = compliance_service.check_compliance(
            validated_request, request_id
        )

        log.info(
            f"合规性检查完成 | ID: {request_id} | "
            f"敏感词: {len(final_result.data.sensitiveWordsArr)}个 | "
            f"检查结果: {len(final_result.data.checkResultArr)}个"
        )

        return final_result

    except Exception as e:
        log.error(f"合规性检查失败 | ID: {request_id} | 错误: {str(e)}")
        # 异常会被api_error_handler装饰器处理
        raise


@router.post("/check-compliance-simple", response_model=ComplianceCheckResponse)
async def check_compliance_simple(
    request: Request, simple_request: SimpleComplianceCheckRequest
) -> ComplianceCheckResponse:
    """
    简化的招标文件合规性检查接口

    Args:
        request: FastAPI请求对象
        simple_request: 简化的合规性检查请求

    Returns:
        ComplianceCheckResponse: 合规性检查结果
    """
    request_id = getattr(request.state, "request_id", "")

    log.info(f"开始简化合规性检查 | ID: {request_id} | URL: {simple_request.file_url}")

    try:
        # 1. 从URL推断文件信息
        try:
            file_info = infer_file_info_from_url(simple_request.file_url)
            log.info(
                f"文件信息推断成功 | ID: {request_id} | 文件: {file_info.filename}"
            )
        except FileInfoInferenceError as e:
            log.error(f"文件信息推断失败 | ID: {request_id} | 错误: {str(e)}")
            raise ValueError(f"无法从URL推断文件信息: {str(e)}")

        # 2. 构建完整的合规性检查请求
        full_request = ComplianceCheckRequest(
            bidding_doc=file_info,
            procurement_project_type=simple_request.procurement_project_type,
            project_category=simple_request.project_category,
            bidding_procurement_method=simple_request.bidding_procurement_method,
        )

        # 3. 验证请求参数
        validated_request = ParameterValidator.validate_compliance_request(
            full_request.dict()
        )

        # 4. 使用集成的合规性检查服务
        final_result = compliance_service.check_compliance(
            validated_request, request_id
        )

        log.info(
            f"简化合规性检查完成 | ID: {request_id} | "
            f"文件: {file_info.filename} | "
            f"敏感词: {len(final_result.data.sensitiveWordsArr)}个 | "
            f"检查结果: {len(final_result.data.checkResultArr)}个"
        )

        return final_result

    except Exception as e:
        log.error(f"简化合规性检查失败 | ID: {request_id} | 错误: {str(e)}")
        # 异常会被api_error_handler装饰器处理
        raise


@router.get("/processing-capability")
async def get_processing_capability(request: Request):
    """获取文件处理能力信息"""
    request_id = getattr(request.state, "request_id", "")

    return {
        "file_processor": file_processor.get_processing_stats(),
        "ai_model": ai_model_service.get_model_info(),
        "sensitive_word_service": sensitive_word_service.get_service_info(),
        "result_processor": result_processor.get_processing_stats(),
    }


@router.get("/processing-metrics")
async def get_processing_metrics(request: Request):
    """获取处理指标和性能监控数据"""
    request_id = getattr(request.state, "request_id", "")

    metrics = compliance_service.get_processing_metrics()

    return {
        "message": "处理指标获取成功",
        "data": metrics,
        "request_id": request_id,
    }


@router.post("/validate-file")
async def validate_file_info(
    request: Request, file_info_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    验证文件信息和处理能力

    Args:
        request: FastAPI请求对象
        file_info_data: 文件信息数据

    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        request_id = getattr(request.state, "request_id", "unknown")
    except Exception as e:
        print(f"[DEBUG] 无法获取request_id: {str(e)}")
        request_id = "fallback"

    print(f"[DEBUG] 开始处理文件验证请求 | ID: {request_id}")

    try:
        print(f"[DEBUG] 导入FileInfo模块...")
        from app.models.schemas import FileInfo

        print(f"[DEBUG] 创建FileInfo对象...")
        file_info = FileInfo(**file_info_data)
        print(f"[DEBUG] FileInfo创建成功: {file_info.filename}")

        print(f"[DEBUG] 调用处理器...")
        capability = file_processor.validate_processing_capability(file_info)
        print(f"[DEBUG] 处理器调用成功")

        print(f"[DEBUG] 准备返回结果...")
        try:
            print(f"[DEBUG] 序列化filename: {file_info.filename}")
            filename = file_info.filename

            print(f"[DEBUG] 序列化extension...")
            extension = (
                str(file_info.extension.value)
                if hasattr(file_info.extension, "value")
                else str(file_info.extension)
            )

            print(f"[DEBUG] 序列化mime_type...")
            mime_type = (
                str(file_info.mime_type.value)
                if hasattr(file_info.mime_type, "value")
                else str(file_info.mime_type)
            )

            print(f"[DEBUG] 序列化size: {file_info.size}")
            size = file_info.size

            print(f"[DEBUG] 序列化url...")
            url = str(file_info.url)

            file_info_dict = {
                "filename": filename,
                "extension": extension,
                "mime_type": mime_type,
                "size": size,
                "url": url,
            }
            print(f"[DEBUG] 序列化完成")
        except Exception as serialize_error:
            print(f"[DEBUG] 序列化异常: {str(serialize_error)}")
            # 使用最简单的序列化方式
            file_info_dict = {
                "filename": str(file_info.filename),
                "extension": str(file_info.extension),
                "mime_type": str(file_info.mime_type),
                "size": int(file_info.size),
                "url": str(file_info.url),
            }

        print(f"[DEBUG] 返回结果")
        return {"file_info": file_info_dict, "processing_capability": capability}

    except Exception as e:
        print(f"[DEBUG] 异常发生: {str(e)}")
        return {"error": str(e), "status": "failed"}


@router.get("/sensitive-word-stats")
async def get_sensitive_word_stats(request: Request):
    """获取敏感词服务统计信息"""
    request_id = getattr(request.state, "request_id", "")

    stats = sensitive_word_service.get_stats(request_id)
    health = sensitive_word_service.check_health(request_id)

    return {
        "health": health,
        "stats": stats,
        "service_info": sensitive_word_service.get_service_info(),
    }


@router.post("/reload-sensitive-words")
async def reload_sensitive_words(request: Request):
    """重载敏感词库"""
    request_id = getattr(request.state, "request_id", "")

    success = sensitive_word_service.reload_sensitive_words(request_id)

    return {
        "success": success,
        "message": "敏感词库重载成功" if success else "敏感词库重载失败",
    }


@router.get("/debug/components")
async def debug_components():
    """调试组件状态"""
    try:
        results = {}

        # 测试AI模型服务
        try:
            ai_info = ai_model_service.get_model_info()
            results["ai_model"] = {
                "status": "ok" if ai_info.get("client_initialized") else "error",
                "info": ai_info,
            }
        except Exception as e:
            results["ai_model"] = {"status": "error", "error": str(e)}

        # 测试敏感词服务
        try:
            health = sensitive_word_service.check_health("debug")
            results["sensitive_word"] = {
                "status": "ok" if health else "error",
                "health": health,
            }
        except Exception as e:
            results["sensitive_word"] = {"status": "error", "error": str(e)}

        # 测试文件处理器
        try:
            stats = file_processor.get_processing_stats()
            results["file_processor"] = {"status": "ok", "stats": stats}
        except Exception as e:
            results["file_processor"] = {"status": "error", "error": str(e)}

        # 测试结果处理器
        try:
            stats = result_processor.get_processing_stats()
            results["result_processor"] = {"status": "ok", "stats": stats}
        except Exception as e:
            results["result_processor"] = {"status": "error", "error": str(e)}

        return {"debug_results": results, "timestamp": "2025-08-06"}

    except Exception as e:
        return {"error": str(e), "timestamp": "2025-08-06"}


@router.post("/debug/validate-file-direct")
async def validate_file_direct(file_info_data: dict):
    """直接验证文件，绕过中间件"""
    try:
        from app.models.schemas import FileInfo

        # 直接创建FileInfo对象
        file_info = FileInfo(**file_info_data)

        # 直接调用处理器
        capability = file_processor.validate_processing_capability(file_info)

        return {
            "status": "success",
            "file_info": file_info.dict(),
            "capability": capability,
            "message": "直接验证成功",
        }

    except Exception as e:
        return {"status": "error", "error": str(e), "message": "直接验证失败"}


@router.post("/debug/simple-test")
async def simple_test(data: dict):
    """最简单的测试端点"""
    return {
        "status": "success",
        "message": "简单测试成功",
        "received_data": data,
        "timestamp": "2025-08-06",
    }


@router.post("/debug/minimal-validate")
async def minimal_validate(data: dict):
    """最小化的文件验证端点"""
    try:
        # 只做最基本的处理，不调用任何复杂组件
        filename = data.get("filename", "unknown")
        size = data.get("size", 0)

        return {
            "status": "success",
            "message": "最小化验证成功",
            "filename": filename,
            "size": size,
            "can_process": True,
            "estimated_time": 1.0,
        }
    except Exception as e:
        return {"status": "error", "message": f"最小化验证失败: {str(e)}"}


@router.post("/debug/test-fileinfo")
async def test_fileinfo_creation(file_info_data: dict):
    """只测试FileInfo创建"""
    try:
        from app.models.schemas import FileInfo

        file_info = FileInfo(**file_info_data)
        return {
            "status": "success",
            "message": "FileInfo创建成功",
            "filename": file_info.filename,
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}


@router.post("/debug/test-processor")
async def test_processor_call(file_info_data: dict):
    """测试处理器调用"""
    try:
        from app.models.schemas import FileInfo

        file_info = FileInfo(**file_info_data)

        # 调用处理器
        capability = file_processor.validate_processing_capability(file_info)
        return {
            "status": "success",
            "message": "处理器调用成功",
            "can_process": capability.get("can_process", False),
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}
