#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查服务是否正在运行
"""

import requests
import time


def check_service_running(url="http://localhost:8088", max_attempts=5):
    """检查服务是否正在运行"""
    print(f"检查服务是否在 {url} 运行...")

    for attempt in range(1, max_attempts + 1):
        try:
            print(f"尝试 {attempt}/{max_attempts}...")

            # 尝试访问健康检查接口
            response = requests.get(f"{url}/health", timeout=5)

            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务正在运行！")
                print(f"   状态: {data.get('status', 'unknown')}")
                print(f"   服务: {data.get('service', 'unknown')}")
                print(f"   版本: {data.get('version', 'unknown')}")
                return True
            else:
                print(f"⚠️  服务响应异常: HTTP {response.status_code}")

        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败 (尝试 {attempt}/{max_attempts})")
            if attempt < max_attempts:
                print("等待2秒后重试...")
                time.sleep(2)
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时 (尝试 {attempt}/{max_attempts})")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

    print(f"❌ 服务未运行或无法访问")
    return False


def main():
    """主函数"""
    print("服务运行状态检查")
    print("=" * 50)

    if check_service_running():
        print("\n🎉 服务正常运行，可以进行API测试")
        print("运行命令: python examples/api_integration_test.py")
        return 0
    else:
        print("\n❌ 服务未运行，请先启动服务")
        print("启动命令: python main.py")
        print("或者: python start_service.py")
        return 1


if __name__ == "__main__":
    exit(main())
