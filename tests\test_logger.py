# -*- coding: utf-8 -*-
"""
日志系统测试
"""

import os
import tempfile
import time
from unittest.mock import patch, MagicMock
import pytest

from app.core.logger import (
    log,
    RequestLogger,
    PerformanceLogger,
    error_handler,
    async_error_handler,
    TimingContext,
    log_function_call,
    log_async_function_call,
)


class TestRequestLogger:
    """请求日志记录器测试"""

    def test_log_request(self, caplog):
        """测试请求日志记录"""
        request_id = "test-123"
        method = "POST"
        url = "/api/v1/check"
        request_data = {"test": "data"}

        RequestLogger.log_request(request_id, method, url, request_data)

        # 验证日志内容
        assert "请求开始" in caplog.text
        assert request_id in caplog.text
        assert method in caplog.text
        assert url in caplog.text

    def test_log_response(self, caplog):
        """测试响应日志记录"""
        request_id = "test-123"
        status_code = 200
        response_size = 1024
        duration = 1.5

        RequestLogger.log_response(request_id, status_code, response_size, duration)

        # 验证日志内容
        assert "请求完成" in caplog.text
        assert request_id in caplog.text
        assert str(status_code) in caplog.text
        assert str(response_size) in caplog.text
        assert "1.500" in caplog.text

    def test_log_error(self, caplog):
        """测试错误日志记录"""
        request_id = "test-123"
        error = ValueError("测试错误")
        context = {"key": "value"}

        RequestLogger.log_error(request_id, error, context)

        # 验证日志内容
        assert "请求错误" in caplog.text
        assert request_id in caplog.text
        assert "ValueError" in caplog.text
        assert "测试错误" in caplog.text


class TestPerformanceLogger:
    """性能日志记录器测试"""

    def test_log_operation_start(self, caplog):
        """测试操作开始日志"""
        operation = "文件处理"
        request_id = "test-123"
        details = {"filename": "test.docx"}

        PerformanceLogger.log_operation_start(operation, request_id, details)

        assert "操作开始" in caplog.text
        assert operation in caplog.text
        assert request_id in caplog.text
        assert "test.docx" in caplog.text

    def test_log_operation_end(self, caplog):
        """测试操作结束日志"""
        operation = "文件处理"
        request_id = "test-123"
        duration = 2.5

        PerformanceLogger.log_operation_end(operation, request_id, duration, True)

        assert "操作结束" in caplog.text
        assert operation in caplog.text
        assert request_id in caplog.text
        assert "成功" in caplog.text
        assert "2.500" in caplog.text

    def test_log_file_processing(self, caplog):
        """测试文件处理日志"""
        request_id = "test-123"
        filename = "test.docx"
        file_size = 1024
        processing_time = 1.2

        PerformanceLogger.log_file_processing(
            request_id, filename, file_size, processing_time
        )

        assert "文件处理" in caplog.text
        assert request_id in caplog.text
        assert filename in caplog.text
        assert str(file_size) in caplog.text
        assert "1.200" in caplog.text

    def test_log_api_call(self, caplog):
        """测试API调用日志"""
        request_id = "test-123"
        api_name = "OpenAI API"
        duration = 3.0

        PerformanceLogger.log_api_call(request_id, api_name, duration, True)

        assert "API调用" in caplog.text
        assert api_name in caplog.text
        assert request_id in caplog.text
        assert "成功" in caplog.text
        assert "3.000" in caplog.text


class TestErrorHandler:
    """错误处理装饰器测试"""

    def test_error_handler_success(self):
        """测试错误处理装饰器 - 成功情况"""

        @error_handler
        def test_function():
            return "success"

        result = test_function()
        assert result == "success"

    def test_error_handler_exception(self, caplog):
        """测试错误处理装饰器 - 异常情况"""

        @error_handler
        def test_function():
            raise ValueError("测试异常")

        result = test_function()

        # 验证返回的错误响应
        assert isinstance(result, dict)
        assert result["status"] == "failure"
        assert "测试异常" in result["message"]
        assert result["function"] == "test_function"
        assert "timestamp" in result

        # 验证日志记录
        assert "函数 test_function 执行失败" in caplog.text

    @pytest.mark.asyncio
    async def test_async_error_handler_success(self):
        """测试异步错误处理装饰器 - 成功情况"""

        @async_error_handler
        async def test_async_function():
            return "async success"

        result = await test_async_function()
        assert result == "async success"

    @pytest.mark.asyncio
    async def test_async_error_handler_exception(self, caplog):
        """测试异步错误处理装饰器 - 异常情况"""

        @async_error_handler
        async def test_async_function():
            raise ValueError("异步测试异常")

        result = await test_async_function()

        # 验证返回的错误响应
        assert isinstance(result, dict)
        assert result["status"] == "failure"
        assert "异步测试异常" in result["message"]
        assert result["function"] == "test_async_function"

        # 验证日志记录
        assert "异步函数 test_async_function 执行失败" in caplog.text


class TestTimingContext:
    """计时上下文管理器测试"""

    def test_timing_context_success(self, caplog):
        """测试计时上下文 - 成功情况"""
        operation = "测试操作"
        request_id = "test-123"

        with TimingContext(operation, request_id) as timer:
            time.sleep(0.1)  # 模拟操作耗时

        # 验证日志记录
        assert "操作开始" in caplog.text
        assert "操作结束" in caplog.text
        assert operation in caplog.text
        assert request_id in caplog.text
        assert "成功" in caplog.text

        # 验证计时
        assert timer.duration > 0.1
        assert timer.duration < 0.2  # 允许一定的时间误差

    def test_timing_context_exception(self, caplog):
        """测试计时上下文 - 异常情况"""
        operation = "测试操作"
        request_id = "test-123"

        with pytest.raises(ValueError):
            with TimingContext(operation, request_id) as timer:
                time.sleep(0.1)
                raise ValueError("测试异常")

        # 验证日志记录
        assert "操作开始" in caplog.text
        assert "操作结束" in caplog.text
        assert "失败" in caplog.text
        assert "发生异常" in caplog.text

        # 验证计时
        assert timer.duration > 0.1


class TestFunctionCallLogger:
    """函数调用日志装饰器测试"""

    def test_log_function_call_success(self, caplog):
        """测试函数调用日志 - 成功情况"""

        @log_function_call
        def test_function():
            time.sleep(0.1)
            return "success"

        result = test_function()

        assert result == "success"
        assert "函数调用开始: test_function" in caplog.text
        assert "函数调用成功: test_function" in caplog.text

    def test_log_function_call_exception(self, caplog):
        """测试函数调用日志 - 异常情况"""

        @log_function_call
        def test_function():
            time.sleep(0.1)
            raise ValueError("测试异常")

        with pytest.raises(ValueError):
            test_function()

        assert "函数调用开始: test_function" in caplog.text
        assert "函数调用失败: test_function" in caplog.text
        assert "测试异常" in caplog.text

    @pytest.mark.asyncio
    async def test_log_async_function_call_success(self, caplog):
        """测试异步函数调用日志 - 成功情况"""

        @log_async_function_call
        async def test_async_function():
            await asyncio.sleep(0.1)
            return "async success"

        import asyncio

        result = await test_async_function()

        assert result == "async success"
        assert "异步函数调用开始: test_async_function" in caplog.text
        assert "异步函数调用成功: test_async_function" in caplog.text

    @pytest.mark.asyncio
    async def test_log_async_function_call_exception(self, caplog):
        """测试异步函数调用日志 - 异常情况"""

        @log_async_function_call
        async def test_async_function():
            import asyncio

            await asyncio.sleep(0.1)
            raise ValueError("异步测试异常")

        with pytest.raises(ValueError):
            await test_async_function()

        assert "异步函数调用开始: test_async_function" in caplog.text
        assert "异步函数调用失败: test_async_function" in caplog.text
        assert "异步测试异常" in caplog.text


class TestLoggerConfig:
    """日志配置测试"""

    def test_logger_config_initialization(self):
        """测试日志配置初始化"""
        # 这个测试主要验证日志配置不会抛出异常
        from app.core.logger import LoggerConfig

        config = LoggerConfig()
        assert config is not None

    @patch("app.core.logger.settings")
    def test_logger_config_with_custom_settings(self, mock_settings):
        """测试自定义设置的日志配置"""
        mock_settings.debug = False
        mock_settings.log_level = "ERROR"
        mock_settings.log_file_path = "/tmp/test_logs"

        from app.core.logger import LoggerConfig

        config = LoggerConfig()
        assert config is not None
