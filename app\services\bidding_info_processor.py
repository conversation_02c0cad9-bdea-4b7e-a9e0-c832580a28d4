# -*- coding: utf-8 -*-
"""
招标信息处理服务
用于提取和替换招标文件中的关键信息
"""

import json
import re
import tempfile
import os
from io import BytesIO
from typing import Dict, List, Optional, Tuple
from openai import OpenAI

# 文档处理库
import docx
import pdfplumber

from app.core.config import settings
from app.core.logger import log, TimingContext, log_function_call
from app.models.enums import FileExtension


class BiddingInfoError(Exception):
    """招标信息处理异常"""

    def __init__(self, message: str, stage: str = "", original_error: Exception = None):
        super().__init__(message)
        self.stage = stage
        self.original_error = original_error
        self.error_type = "BIDDING_INFO_ERROR"


class BiddingInfoProcessor:
    """招标信息处理器"""

    def __init__(self):
        """初始化招标信息处理器"""
        self.client = None
        self._initialize_client()

        # 替换规则配置
        self.replacement_rules = {
            "招标单位名称": "安徽省一二三四科技有限公司",
            "招标单位联系人": "张三",
            "招标单位联系电话": "13333333333",
            "招标单位地址": "安徽省合肥市庐阳区长江路123号",
            "招标单位邮箱": "<EMAIL>",
        }

    def _initialize_client(self):
        """初始化本地大模型客户端"""
        try:
            # 使用指定的本地大模型接口
            self.client = OpenAI(
                api_key="dummy-key",  # 本地模型通常不需要真实的API key
                base_url="http://172.18.10.23:8000/v1",
            )
            log.info("招标信息处理器：本地大模型客户端初始化成功")
        except Exception as e:
            log.error(f"招标信息处理器：本地大模型客户端初始化失败: {str(e)}")
            raise BiddingInfoError(
                f"本地大模型客户端初始化失败: {str(e)}",
                stage="客户端初始化",
                original_error=e,
            )

    @log_function_call
    def extract_first_10_pages(
        self, file_content: bytes, file_extension: FileExtension, request_id: str = ""
    ) -> str:
        """
        提取文件前10页内容

        Args:
            file_content: 文件二进制内容
            file_extension: 文件扩展名
            request_id: 请求ID

        Returns:
            str: 前10页的文本内容
        """
        try:
            with TimingContext("提取前10页内容", request_id):
                if file_extension == FileExtension.PDF:
                    return self._extract_pdf_first_10_pages(file_content, request_id)
                elif file_extension == FileExtension.DOCX:
                    return self._extract_docx_first_10_pages(file_content, request_id)
                else:
                    raise BiddingInfoError(
                        f"不支持的文件类型: {file_extension}", stage="文件类型检查"
                    )
        except Exception as e:
            if isinstance(e, BiddingInfoError):
                raise
            raise BiddingInfoError(
                f"提取前10页内容失败: {str(e)}", stage="内容提取", original_error=e
            )

    def _extract_pdf_first_10_pages(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """提取PDF前10页内容"""
        try:
            file_obj = BytesIO(file_content)
            content_parts = []

            with pdfplumber.open(file_obj) as pdf:
                # 只处理前10页
                pages_to_process = min(10, len(pdf.pages))
                log.info(f"PDF总页数: {len(pdf.pages)}, 将处理前{pages_to_process}页")

                for page_num in range(pages_to_process):
                    try:
                        page = pdf.pages[page_num]
                        text = page.extract_text()
                        if text:
                            content_parts.append(
                                f"=== 第{page_num + 1}页 ===\n{text.strip()}"
                            )
                    except Exception as page_error:
                        log.warning(f"PDF第{page_num + 1}页提取失败: {str(page_error)}")
                        continue

            content = "\n\n".join(content_parts)
            log.info(f"PDF前10页内容提取完成: {len(content)} 字符")
            return content

        except Exception as e:
            raise BiddingInfoError(
                f"PDF前10页提取失败: {str(e)}", stage="PDF内容提取", original_error=e
            )

    def _extract_docx_first_10_pages(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """提取DOCX前10页内容（近似处理）"""
        try:
            file_obj = BytesIO(file_content)
            doc = docx.Document(file_obj)

            content_parts = []
            paragraph_count = 0
            # DOCX没有明确的页面概念，我们按段落数量估算前10页的内容
            # 假设每页约20-30个段落
            max_paragraphs = 250  # 约10页的段落数

            for paragraph in doc.paragraphs:
                if paragraph_count >= max_paragraphs:
                    break

                text = paragraph.text.strip()
                if text:
                    content_parts.append(text)
                    paragraph_count += 1

            # 处理表格（也计入段落数）
            for table in doc.tables:
                if paragraph_count >= max_paragraphs:
                    break

                for row in table.rows:
                    if paragraph_count >= max_paragraphs:
                        break
                    row_text = " | ".join(cell.text.strip() for cell in row.cells)
                    if row_text.strip():
                        content_parts.append(row_text)
                        paragraph_count += 1

            content = "\n\n".join(content_parts)
            log.info(
                f"DOCX前10页内容提取完成: {len(content)} 字符, 处理了{paragraph_count}个段落"
            )
            return content

        except Exception as e:
            raise BiddingInfoError(
                f"DOCX前10页提取失败: {str(e)}", stage="DOCX内容提取", original_error=e
            )

    @log_function_call
    def analyze_bidding_info(
        self, content: str, request_id: str = ""
    ) -> Dict[str, str]:
        """
        使用本地大模型分析招标信息

        Args:
            content: 文档内容
            request_id: 请求ID

        Returns:
            Dict[str, str]: 提取的招标信息字典
        """
        try:
            with TimingContext("大模型分析招标信息", request_id):
                # 构建分析提示词
                system_prompt = self._build_analysis_prompt()

                messages = [
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": f"请分析以下招标文件内容，提取相关信息：\n\n{content}",
                    },
                ]

                # 调用本地大模型
                response = self._call_local_model(messages, request_id)

                # 解析响应
                bidding_info = self._parse_analysis_response(response)

                log.info(f"招标信息分析完成: {bidding_info}")
                return bidding_info

        except Exception as e:
            if isinstance(e, BiddingInfoError):
                raise
            raise BiddingInfoError(
                f"招标信息分析失败: {str(e)}", stage="大模型分析", original_error=e
            )

    def _build_analysis_prompt(self) -> str:
        """构建分析提示词"""
        return """你是一个专业的招标文件信息提取专家。请从给定的招标文件内容中提取以下信息：

1. 招标单位名称（可能的字段名：招标人名称、招标单位名称、采购人名称、采购单位名称）
2. 招标单位联系人（可能的字段名：招标人联系人姓名、招标单位联系人姓名、采购人联系人姓名、采购单位联系人姓名）  
3. 招标单位联系电话（可能的字段名：招标人联系电话、招标单位联系电话、采购人联系电话、采购单位联系电话）
4. 招标单位地址（可能的字段名：招标人地址、招标单位地址、采购人地址、采购单位地址）
5. 招标单位邮箱（如果有的话）

请仔细阅读文档内容，找到这些信息的具体值。如果某个信息没有找到，请返回空字符串。

请严格按照以下JSON格式返回结果，不要包含其他文本：

{
    "招标单位名称": "提取到的招标单位名称",
    "招标单位联系人": "提取到的联系人姓名", 
    "招标单位联系电话": "提取到的联系电话",
    "招标单位地址": "提取到的地址",
    "招标单位邮箱": "提取到的邮箱（如果有）"
}"""

    def _call_local_model(
        self, messages: List[Dict[str, str]], request_id: str = ""
    ) -> str:
        """调用本地大模型"""
        try:
            log.info(f"调用本地大模型分析招标信息 | 请求ID: {request_id}")

            response = self.client.chat.completions.create(
                model="Qwen/Qwen2.5-7B-Instruct",
                messages=messages,
                temperature=0.1,  # 稍微提高一点随机性，避免过于死板
                max_tokens=1000,  # 招标信息提取只需要很少的输出tokens
                timeout=60,
            )

            result = response.choices[0].message.content
            log.info(f"本地大模型响应长度: {len(result)} 字符")
            log.debug(f"本地大模型原始响应: {result}")

            return result

        except Exception as e:
            raise BiddingInfoError(
                f"本地大模型调用失败: {str(e)}", stage="模型调用", original_error=e
            )

    def _parse_analysis_response(self, response: str) -> Dict[str, str]:
        """解析大模型分析响应"""
        try:
            # 清理响应内容
            cleaned_response = response.strip()

            # 移除可能的markdown代码块标记
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[3:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]

            cleaned_response = cleaned_response.strip()

            # 尝试解析JSON
            try:
                result = json.loads(cleaned_response)

                # 验证必需的字段
                expected_fields = [
                    "招标单位名称",
                    "招标单位联系人",
                    "招标单位联系电话",
                    "招标单位地址",
                    "招标单位邮箱",
                ]
                for field in expected_fields:
                    if field not in result:
                        result[field] = ""

                return result

            except json.JSONDecodeError as e:
                log.warning(f"JSON解析失败，尝试手动提取: {str(e)}")
                return self._manual_parse_response(cleaned_response)

        except Exception as e:
            log.error(f"响应解析失败: {str(e)}")
            # 返回空结果
            return {
                "招标单位名称": "",
                "招标单位联系人": "",
                "招标单位联系电话": "",
                "招标单位地址": "",
                "招标单位邮箱": "",
            }

    def _manual_parse_response(self, response: str) -> Dict[str, str]:
        """手动解析响应内容"""
        result = {
            "招标单位名称": "",
            "招标单位联系人": "",
            "招标单位联系电话": "",
            "招标单位地址": "",
            "招标单位邮箱": "",
        }

        try:
            # 使用正则表达式提取信息
            patterns = {
                "招标单位名称": r'"招标单位名称"[:\s]*"([^"]*)"',
                "招标单位联系人": r'"招标单位联系人"[:\s]*"([^"]*)"',
                "招标单位联系电话": r'"招标单位联系电话"[:\s]*"([^"]*)"',
                "招标单位地址": r'"招标单位地址"[:\s]*"([^"]*)"',
                "招标单位邮箱": r'"招标单位邮箱"[:\s]*"([^"]*)"',
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, response)
                if match:
                    result[field] = match.group(1).strip()

        except Exception as e:
            log.warning(f"手动解析失败: {str(e)}")

        return result

    @log_function_call
    def replace_bidding_info(
        self, content: str, bidding_info: Dict[str, str], request_id: str = ""
    ) -> str:
        """
        替换招标信息

        Args:
            content: 原始内容
            bidding_info: 提取的招标信息
            request_id: 请求ID

        Returns:
            str: 替换后的内容
        """
        try:
            with TimingContext("替换招标信息", request_id):
                modified_content = content
                replacement_count = 0

                # 遍历提取到的信息，进行替换
                for field, extracted_value in bidding_info.items():
                    if extracted_value and extracted_value.strip():
                        replacement_value = self.replacement_rules.get(field, "")
                        if replacement_value:
                            # 执行替换
                            before_count = modified_content.count(extracted_value)
                            modified_content = modified_content.replace(
                                extracted_value, replacement_value
                            )
                            after_count = modified_content.count(extracted_value)
                            replaced_count = before_count - after_count

                            if replaced_count > 0:
                                replacement_count += replaced_count
                                log.info(
                                    f"替换 {field}: '{extracted_value}' -> '{replacement_value}' ({replaced_count}处)"
                                )

                log.info(f"招标信息替换完成，共替换 {replacement_count} 处")
                return modified_content

        except Exception as e:
            raise BiddingInfoError(
                f"招标信息替换失败: {str(e)}", stage="信息替换", original_error=e
            )

    @log_function_call
    def process_bidding_document(
        self, file_content: bytes, file_extension: FileExtension, request_id: str = ""
    ) -> bytes:
        """
        处理招标文档，提取并替换关键信息

        Args:
            file_content: 文件二进制内容
            file_extension: 文件扩展名
            request_id: 请求ID

        Returns:
            bytes: 处理后的文件内容
        """
        try:
            with TimingContext("处理招标文档", request_id):
                # 1. 提取前10页内容
                first_10_pages = self.extract_first_10_pages(
                    file_content, file_extension, request_id
                )

                # 2. 分析招标信息
                bidding_info = self.analyze_bidding_info(first_10_pages, request_id)

                # 3. 如果没有提取到有效信息，直接返回原文件
                if not any(info.strip() for info in bidding_info.values()):
                    log.info("未提取到有效招标信息，返回原文件")
                    return file_content

                # 4. 提取完整文档内容
                full_content = self._extract_full_content(
                    file_content, file_extension, request_id
                )

                # 5. 替换招标信息
                modified_content = self.replace_bidding_info(
                    full_content, bidding_info, request_id
                )

                # 6. 重新生成文件
                modified_file_content = self._regenerate_file(
                    modified_content, file_extension, request_id
                )

                return modified_file_content

        except Exception as e:
            if isinstance(e, BiddingInfoError):
                raise
            raise BiddingInfoError(
                f"招标文档处理失败: {str(e)}", stage="整体处理", original_error=e
            )

    def _extract_full_content(
        self, file_content: bytes, file_extension: FileExtension, request_id: str = ""
    ) -> str:
        """提取完整文档内容"""
        try:
            if file_extension == FileExtension.PDF:
                return self._extract_full_pdf_content(file_content, request_id)
            elif file_extension == FileExtension.DOCX:
                return self._extract_full_docx_content(file_content, request_id)
            else:
                raise BiddingInfoError(f"不支持的文件类型: {file_extension}")
        except Exception as e:
            if isinstance(e, BiddingInfoError):
                raise
            raise BiddingInfoError(
                f"完整内容提取失败: {str(e)}", stage="完整内容提取", original_error=e
            )

    def _extract_full_pdf_content(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """提取完整PDF内容"""
        try:
            file_obj = BytesIO(file_content)
            content_parts = []

            with pdfplumber.open(file_obj) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        text = page.extract_text()
                        if text:
                            content_parts.append(text.strip())
                    except Exception as page_error:
                        log.warning(f"PDF第{page_num}页提取失败: {str(page_error)}")
                        continue

            content = "\n\n".join(content_parts)
            log.info(f"PDF完整内容提取完成: {len(content)} 字符")
            return content

        except Exception as e:
            raise BiddingInfoError(
                f"PDF完整内容提取失败: {str(e)}", stage="PDF完整提取", original_error=e
            )

    def _extract_full_docx_content(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """提取完整DOCX内容"""
        try:
            file_obj = BytesIO(file_content)
            doc = docx.Document(file_obj)

            content_parts = []

            # 提取所有段落
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    content_parts.append(text)

            # 提取所有表格
            for table in doc.tables:
                for row in table.rows:
                    row_text = " | ".join(cell.text.strip() for cell in row.cells)
                    if row_text.strip():
                        content_parts.append(row_text)

            content = "\n\n".join(content_parts)
            log.info(f"DOCX完整内容提取完成: {len(content)} 字符")
            return content

        except Exception as e:
            raise BiddingInfoError(
                f"DOCX完整内容提取失败: {str(e)}",
                stage="DOCX完整提取",
                original_error=e,
            )

    def _regenerate_file(
        self, content: str, file_extension: FileExtension, request_id: str = ""
    ) -> bytes:
        """重新生成文件"""
        # 注意：这里简化处理，实际上重新生成原格式文件比较复杂
        # 目前返回原始内容的字节形式，实际应用中可能需要更复杂的文档重建逻辑
        log.warning("文件重新生成功能简化实现，返回文本内容的字节形式")
        return content.encode("utf-8")


# 创建全局招标信息处理器实例
bidding_info_processor = BiddingInfoProcessor()
