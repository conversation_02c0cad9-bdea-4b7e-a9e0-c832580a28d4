#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的文件服务器，用于测试文件下载
"""

import os
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import unquote


class TestFileHandler(SimpleHTTPRequestHandler):
    """测试文件处理器"""

    def __init__(self, *args, **kwargs):
        # 设置服务目录为tests目录
        super().__init__(
            *args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs
        )

    def end_headers(self):
        # 添加CORS头
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        self.send_header("Access-Control-Allow-Headers", "*")
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[文件服务器] {format % args}")


def start_file_server(port=9999):
    """启动文件服务器"""

    server_address = ("", port)
    httpd = HTTPServer(server_address, TestFileHandler)

    print(f"🚀 测试文件服务器启动在端口 {port}")
    print(f"📁 服务目录: {os.path.dirname(os.path.abspath(__file__))}")
    print(f"🌐 访问地址: http://localhost:{port}/")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  文件服务器已停止")
        httpd.shutdown()


def start_server_in_background(port=9999):
    """在后台启动文件服务器"""

    def server_thread():
        start_file_server(port)

    thread = threading.Thread(target=server_thread, daemon=True)
    thread.start()

    # 等待服务器启动
    time.sleep(1)

    print(f"✅ 后台文件服务器已启动在端口 {port}")
    return thread


if __name__ == "__main__":
    start_file_server()
