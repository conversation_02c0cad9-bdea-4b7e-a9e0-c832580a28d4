#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复合规性检查超时问题
"""

import requests
import json
import time
import signal
from contextlib import contextmanager


@contextmanager
def timeout_handler(seconds):
    """超时处理上下文管理器"""

    def timeout_signal_handler(signum, frame):
        raise TimeoutError(f"操作超时 ({seconds}秒)")

    # 设置信号处理器
    old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)


def test_step_by_step():
    """分步测试合规性检查"""
    print("分步测试合规性检查")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 1. 测试参数验证
    print("\n1. 测试参数验证...")
    request_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://httpbin.org/status/200",  # 简单的200响应
        },
    }

    try:
        with timeout_handler(10):  # 10秒超时
            print("发送请求...")
            response = requests.post(
                f"{base_url}/api/v1/check-compliance", json=request_data, timeout=5
            )

            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")

    except TimeoutError as e:
        print(f"⏰ 操作超时: {e}")
    except requests.exceptions.Timeout:
        print("⏰ HTTP请求超时")
    except Exception as e:
        print(f"❌ 异常: {str(e)}")


def create_mock_compliance_endpoint():
    """创建一个模拟的合规性检查端点用于测试"""
    print("\n创建模拟端点测试...")

    # 创建一个简单的Flask应用来模拟
    mock_response = {
        "sensitiveWordsArr": [{"type": "测试类型", "content": "测试敏感词", "num": 1}],
        "checkResultArr": [
            {
                "type": "合规性",
                "content": "测试问题",
                "level": "轻微",
                "suggestion": "测试建议",
            }
        ],
    }

    print(f"模拟响应: {json.dumps(mock_response, indent=2, ensure_ascii=False)}")


def test_individual_services():
    """测试各个服务组件"""
    print("\n测试各个服务组件...")

    base_url = "http://localhost:8088"

    # 测试文件验证（这个之前超时了）
    print("\n测试文件验证接口（短超时）...")
    file_info = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info, timeout=3  # 3秒超时
        )

        print(f"文件验证状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            capability = data.get("processing_capability", {})
            print(f"可处理: {capability.get('can_process', False)}")
        else:
            print(f"文件验证失败: {response.text[:100]}...")

    except requests.exceptions.Timeout:
        print("⏰ 文件验证超时 - 这里有问题！")
    except Exception as e:
        print(f"❌ 文件验证异常: {str(e)}")


def main():
    """主函数"""
    print("合规性检查超时问题修复")
    print("=" * 60)

    # 测试各个组件
    test_individual_services()

    # 分步测试
    test_step_by_step()

    # 创建模拟响应
    create_mock_compliance_endpoint()

    print("\n" + "=" * 60)
    print("建议的修复方案:")
    print("=" * 60)
    print("1. 文件验证接口存在性能问题，需要优化")
    print("2. 合规性检查接口可能在文件下载阶段卡住")
    print("3. 需要添加更多的超时控制和错误处理")
    print("4. 考虑异步处理大文件")


if __name__ == "__main__":
    main()
