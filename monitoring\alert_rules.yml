# Prometheus告警规则 - 招标文件合规性检查助手

groups:
  - name: compliance_checker_alerts
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up{job="compliance-checker"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "合规性检查服务不可用"
          description: "合规性检查服务已停止响应超过1分钟"

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "高错误率检测"
          description: "5分钟内错误率超过10%"

      # 响应时间过长告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 60
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过60秒"

      # 内存使用率过高告警
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes{name="compliance-checker"} / container_spec_memory_limit_bytes{name="compliance-checker"}) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "容器内存使用率超过80%"

      # CPU使用率过高告警
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{name="compliance-checker"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "容器CPU使用率超过80%"

      # 磁盘空间不足告警
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 20
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "根分区可用空间少于20%"

      # 请求队列积压告警
      - alert: RequestQueueBacklog
        expr: request_queue_size > 50
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "请求队列积压"
          description: "请求队列中有超过50个待处理请求"

      # AI模型调用失败率过高
      - alert: HighAIModelFailureRate
        expr: rate(ai_model_requests_failed_total[5m]) / rate(ai_model_requests_total[5m]) > 0.2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "AI模型调用失败率过高"
          description: "AI模型调用失败率超过20%"

      # 敏感词服务不可用
      - alert: SensitiveWordServiceDown
        expr: sensitive_word_service_health == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "敏感词服务不可用"
          description: "敏感词检测服务连接失败"

  - name: infrastructure_alerts
    rules:
      # Redis连接失败（如果使用Redis）
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis服务不可用"
          description: "Redis缓存服务连接失败"

      # Nginx错误率过高
      - alert: NginxHighErrorRate
        expr: rate(nginx_http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Nginx错误率过高"
          description: "Nginx 5xx错误率超过5%"

      # 容器重启频繁
      - alert: ContainerRestartingFrequently
        expr: rate(container_last_seen[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器频繁重启"
          description: "容器在5分钟内重启次数异常"