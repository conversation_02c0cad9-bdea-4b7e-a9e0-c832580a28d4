#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速API测试脚本
"""

import requests
import json
import time


def test_basic_endpoints():
    """测试基础端点"""
    base_url = "http://localhost:8088"

    endpoints = [
        ("健康检查", "GET", "/health"),
        ("根路径", "GET", "/"),
        ("枚举值", "GET", "/api/v1/enums"),
        ("服务状态", "GET", "/api/v1/service-status"),
        ("处理能力", "GET", "/api/v1/processing-capability"),
        ("处理指标", "GET", "/api/v1/processing-metrics"),
        ("敏感词统计", "GET", "/api/v1/sensitive-word-stats"),
    ]

    print("快速API测试")
    print("=" * 50)

    passed = 0
    total = len(endpoints)

    for name, method, path in endpoints:
        print(f"\n测试 {name}: {method} {path}")
        print("-" * 30)

        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=10)
            else:
                response = requests.post(f"{base_url}{path}", timeout=10)

            if response.status_code == 200:
                print(f"✅ {name}: 成功")

                # 显示部分响应内容
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        # 只显示前几个键
                        keys = list(data.keys())[:3]
                        preview = {k: data[k] for k in keys}
                        print(f"   响应预览: {preview}")
                    else:
                        print(f"   响应类型: {type(data)}")
                except:
                    print(f"   响应长度: {len(response.text)} 字符")

                passed += 1
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                print(f"   错误: {response.text[:100]}...")

        except requests.exceptions.Timeout:
            print(f"⏰ {name}: 超时")
        except Exception as e:
            print(f"❌ {name}: 异常 - {str(e)}")

    print(f"\n{'='*50}")
    print("测试总结")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total:.1%}")

    if passed == total:
        print("\n🎉 所有基础API测试通过！")
        return 0
    else:
        print(f"\n⚠️  有 {total-passed} 个API测试失败")
        return 1


def test_file_validation_simple():
    """简单的文件验证测试"""
    print(f"\n{'='*50}")
    print("文件验证测试（简化版）")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 简单的文件信息
    file_info = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,  # 1KB，很小的文件
        "url": "http://example.com/test.docx",
    }

    try:
        print("发送文件验证请求...")
        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info, timeout=5  # 5秒超时
        )

        if response.status_code == 200:
            data = response.json()
            print("✅ 文件验证成功")

            capability = data.get("processing_capability", {})
            print(f"   可处理: {capability.get('can_process', False)}")
            print(f"   首选方法: {capability.get('preferred_method', 'N/A')}")

            return True
        else:
            print(f"❌ 文件验证失败: HTTP {response.status_code}")
            print(f"   错误: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("⏰ 文件验证超时（可能有性能问题）")
        return False
    except Exception as e:
        print(f"❌ 文件验证异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 快速API测试")
    print("=" * 60)

    # 测试基础端点
    basic_result = test_basic_endpoints()

    # 测试文件验证（简化版）
    file_result = test_file_validation_simple()

    print(f"\n{'='*60}")
    print("最终总结")
    print("=" * 60)

    if basic_result == 0:
        print("✅ 基础API功能正常")
    else:
        print("❌ 基础API存在问题")

    if file_result:
        print("✅ 文件验证功能正常")
    else:
        print("⚠️  文件验证功能可能有性能问题")

    if basic_result == 0:
        print("\n🎉 系统核心功能正常，可以使用！")
        print("\n可用的API接口:")
        print("- 健康检查: GET /health")
        print("- 服务状态: GET /api/v1/service-status")
        print("- 合规性检查: POST /api/v1/check-compliance")
        print("- API文档: http://localhost:8088/docs")
        return 0
    else:
        print("\n❌ 系统存在问题，需要进一步调试")
        return 1


if __name__ == "__main__":
    exit(main())
