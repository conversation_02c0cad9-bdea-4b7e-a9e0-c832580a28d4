#!/usr/bin/env python3
"""
快速健康检查
"""

import requests

def test_health():
    try:
        response = requests.get("http://localhost:8088/health", timeout=5)
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")

if __name__ == "__main__":
    test_health()
