#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务启动检查脚本
"""

import sys
import traceback


def check_imports():
    """检查关键模块导入"""
    print("检查关键模块导入...")

    try:
        print("  导入FastAPI...")
        from fastapi import FastAPI

        print("  导入配置...")
        from app.core.config import settings

        print("  导入日志...")
        from app.core.logger import log

        print("  导入路由...")
        from app.api.routes import router

        print("  导入中间件...")
        from app.middleware.logging import LoggingMiddleware
        from app.middleware.validation import RequestValidationMiddleware

        print("✅ 所有关键模块导入成功")
        return True

    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False


def check_main_app():
    """检查主应用创建"""
    print("\n检查主应用创建...")

    try:
        print("  导入主应用...")
        from main import app

        print("  检查应用配置...")
        print(f"    应用标题: {app.title}")
        print(f"    应用版本: {app.version}")

        print("✅ 主应用创建成功")
        return True

    except Exception as e:
        print(f"❌ 主应用创建失败: {e}")
        traceback.print_exc()
        return False


def check_config():
    """检查配置"""
    print("\n检查配置...")

    try:
        from app.core.config import settings, validate_config

        print(f"  环境: {settings.environment}")
        print(f"  调试模式: {settings.debug}")
        print(f"  模型名称: {settings.model_name}")
        print(f"  API密钥: {'已设置' if settings.model_apikey else '未设置'}")

        # 验证配置
        if validate_config():
            print("✅ 配置验证成功")
            return True
        else:
            print("❌ 配置验证失败")
            return False

    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        traceback.print_exc()
        return False


def check_services():
    """检查服务初始化"""
    print("\n检查服务初始化...")

    try:
        print("  检查合规性服务...")
        from app.services.compliance_service import compliance_service

        status = compliance_service.get_service_status()
        print(f"    服务名称: {status['service_info']['service_name']}")
        print(f"    版本: {status['service_info']['version']}")

        health = status["health_status"]
        for service_name, healthy in health.items():
            status_text = "✅" if healthy else "❌"
            print(f"    {service_name}: {status_text}")

        print("✅ 服务初始化成功")
        return True

    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("服务启动检查")
    print("=" * 50)

    checks = [
        ("模块导入", check_imports),
        ("配置检查", check_config),
        ("服务初始化", check_services),
        ("主应用创建", check_main_app),
    ]

    passed = 0
    total = len(checks)

    for check_name, check_func in checks:
        print(f"\n{'='*50}")
        print(f"检查: {check_name}")
        print("=" * 50)

        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name}: 通过")
            else:
                print(f"❌ {check_name}: 失败")
        except Exception as e:
            print(f"❌ {check_name}: 异常 - {e}")

    print(f"\n{'='*50}")
    print("检查总结")
    print("=" * 50)
    print(f"总检查数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total:.1%}")

    if passed == total:
        print("\n🎉 所有检查通过！可以启动服务")
        print("运行命令: python main.py")
        return 0
    else:
        print(f"\n❌ 有 {total-passed} 个检查失败，请先解决问题")
        return 1


if __name__ == "__main__":
    exit(main())
