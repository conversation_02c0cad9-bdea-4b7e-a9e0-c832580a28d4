#!/usr/bin/env python3
"""
测试所有属性访问是否正确使用新的响应格式
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.schemas import (
    ComplianceCheckResponse,
    ComplianceCheckData,
    SensitiveWordItem,
    CheckResultItem,
)
from app.models.enums import QuestionType


def test_attribute_access():
    """测试属性访问"""
    print("🔍 测试属性访问...")

    # 创建测试数据
    sensitive_words = [SensitiveWordItem(type="错漏词汇", content="测试词", num=1)]

    check_results = [
        CheckResultItem(
            quesType=QuestionType.COMPLIANCE,
            quesDesc="测试问题描述",
            originalArr=["测试原文"],
            point="测试要点",
            advice="测试建议",
        )
    ]

    # 创建新格式的响应
    response = ComplianceCheckResponse(
        code=200,
        message="测试成功",
        data=ComplianceCheckData(
            sensitiveWordsArr=sensitive_words, checkResultArr=check_results
        ),
    )

    # 测试正确的属性访问
    try:
        print(f"✅ 正确访问方式:")
        print(f"   敏感词数量: {len(response.data.sensitiveWordsArr)}")
        print(f"   检查结果数量: {len(response.data.checkResultArr)}")

        # 测试错误的属性访问（应该失败）
        try:
            len(response.sensitiveWordsArr)
            print("❌ 错误：旧的属性访问方式仍然有效！")
            return False
        except AttributeError:
            print("✅ 正确：旧的属性访问方式已被阻止")

        try:
            len(response.checkResultArr)
            print("❌ 错误：旧的属性访问方式仍然有效！")
            return False
        except AttributeError:
            print("✅ 正确：旧的属性访问方式已被阻止")

        return True

    except Exception as e:
        print(f"❌ 属性访问测试失败: {e}")
        return False


def test_ai_model_service_response():
    """测试AI模型服务响应格式"""
    print("\n🔍 测试AI模型服务响应格式...")

    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo

        # 创建测试项目信息
        project_info = ProjectInfo(
            project_name="测试项目",
            procurement_project_type="政府采购",
            project_category="服务类",
            bidding_procurement_method="公开招标",
            budget_amount=1000000.0,
        )

        ai_service = AIModelService()

        # 测试空响应创建
        empty_response = ComplianceCheckResponse(
            code=200,
            message="AI检查完成，未发现问题",
            data=ComplianceCheckData(checkResultArr=[]),
        )

        # 测试属性访问
        check_count = len(empty_response.data.checkResultArr)
        print(f"✅ AI模型服务响应格式正确，检查结果数量: {check_count}")

        return True
    except Exception as e:
        print(f"❌ AI模型服务测试失败: {e}")
        return False


def test_compliance_service_integration():
    """测试合规性服务集成"""
    print("\n🔍 测试合规性服务集成...")

    try:
        from app.services.compliance_service import ComplianceService

        # 创建服务实例
        service = ComplianceService()

        # 测试服务健康检查
        health_status = service.validate_prerequisites("test-request")
        print(f"✅ 服务健康检查完成: {health_status}")

        return True
    except Exception as e:
        print(f"❌ 合规性服务集成测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试属性访问...")

    success = True
    success &= test_attribute_access()
    success &= test_ai_model_service_response()
    success &= test_compliance_service_integration()

    if success:
        print("\n🎉 所有测试通过！属性访问修复成功！")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
        sys.exit(1)
