#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的端点
"""

import requests
import json
import time


def test_fixed_validate_file():
    """测试修复后的文件验证端点"""
    print("测试修复后的文件验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    print("发送文件验证请求...")

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info_data, timeout=10
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 文件验证成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 文件验证失败")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 文件验证超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 文件验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_compliance_check():
    """测试合规性检查端点"""
    print("\n测试合规性检查端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 使用一个会快速失败的URL进行测试
    request_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://httpbin.org/status/404",  # 会快速返回404
        },
    }

    print("发送合规性检查请求...")

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/check-compliance",
            json=request_data,
            timeout=30,  # 30秒超时
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code in [200, 400]:  # 400也是可接受的，说明请求被处理了
            print("✅ 合规性检查请求被处理")
            print(f"响应: {response.text[:300]}...")
            return True
        else:
            print(f"❌ 合规性检查失败")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 合规性检查超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 合规性检查异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def main():
    """主函数"""
    print("测试修复后的API端点")
    print("=" * 60)

    print("请确保服务已重启")
    print("=" * 60)

    # 测试文件验证端点
    validate_ok = test_fixed_validate_file()

    # 如果文件验证正常，测试合规性检查
    if validate_ok:
        compliance_ok = test_compliance_check()
    else:
        compliance_ok = False

    print(f"\n{'='*60}")
    print("最终结果")
    print("=" * 60)

    if validate_ok:
        print("🎉 文件验证端点修复成功！")

        if compliance_ok:
            print("🎉 合规性检查端点也正常工作！")
            print("\n✅ 系统现在完全可以使用了！")
            print("你可以使用真实的文件URL进行完整的合规性检查测试")
        else:
            print("⚠️  合规性检查端点仍有问题，但文件验证已修复")
    else:
        print("❌ 文件验证端点仍有问题")
        print("需要进一步调试")


if __name__ == "__main__":
    main()
