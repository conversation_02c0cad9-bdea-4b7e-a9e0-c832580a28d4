# -*- coding: utf-8 -*-
"""
API路由测试
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from app.api.routes import router
from app.models.schemas import ComplianceCheckRequest, FileInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


@pytest.fixture
def app():
    """创建测试应用"""
    test_app = FastAPI()
    test_app.include_router(router, prefix="/api/v1")
    return test_app


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def sample_compliance_request():
    """示例合规性检查请求"""
    return {
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://example.com/test.docx",
        },
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
    }


class TestHealthAndStatus:
    """健康检查和状态接口测试"""

    def test_health_check(self, client):
        """测试健康检查接口"""
        response = client.get("/api/v1/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "bidding-document-compliance-checker"
        assert data["version"] == "1.0.0"

    def test_get_enums(self, client):
        """测试获取枚举值接口"""
        response = client.get("/api/v1/enums")

        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "data" in data
        assert "procurement_project_types" in data["data"]
        assert "project_categories" in data["data"]
        assert "bidding_procurement_methods" in data["data"]

    @patch("app.api.routes.ai_model_service")
    @patch("app.api.routes.sensitive_word_service")
    @patch("app.api.routes.file_processor")
    @patch("app.api.routes.result_processor")
    def test_service_status(
        self,
        mock_result_processor,
        mock_file_processor,
        mock_sensitive_word_service,
        mock_ai_model_service,
        client,
    ):
        """测试服务状态接口"""
        # 模拟各服务的响应
        mock_ai_model_service.get_model_info.return_value = {"client_initialized": True}
        mock_sensitive_word_service.check_health.return_value = True
        mock_sensitive_word_service.get_service_info.return_value = {
            "base_url": "http://test"
        }
        mock_file_processor.get_processing_stats.return_value = {
            "supported_formats": [".docx", ".pdf"]
        }
        mock_result_processor.get_processing_stats.return_value = {
            "max_check_results": 15
        }

        response = client.get("/api/v1/service-status")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "operational"
        assert "services" in data
        assert "ai_model" in data["services"]
        assert "sensitive_word" in data["services"]
        assert "file_processor" in data["services"]
        assert "result_processor" in data["services"]

    @patch("app.api.routes.ai_model_service")
    @patch("app.api.routes.sensitive_word_service")
    @patch("app.api.routes.file_processor")
    @patch("app.api.routes.result_processor")
    def test_processing_capability(
        self,
        mock_result_processor,
        mock_file_processor,
        mock_sensitive_word_service,
        mock_ai_model_service,
        client,
    ):
        """测试处理能力接口"""
        # 模拟各服务的响应
        mock_file_processor.get_processing_stats.return_value = {
            "supported_formats": [".docx", ".pdf"]
        }
        mock_ai_model_service.get_model_info.return_value = {"model_name": "test-model"}
        mock_sensitive_word_service.get_service_info.return_value = {
            "base_url": "http://test"
        }
        mock_result_processor.get_processing_stats.return_value = {
            "max_check_results": 15
        }

        response = client.get("/api/v1/processing-capability")

        assert response.status_code == 200
        data = response.json()
        assert "file_processor" in data
        assert "ai_model" in data
        assert "sensitive_word_service" in data
        assert "result_processor" in data


class TestComplianceCheck:
    """合规性检查接口测试"""

    @patch("app.api.routes.result_processor")
    @patch("app.api.routes.sensitive_word_service")
    @patch("app.api.routes.ai_model_service")
    @patch("app.api.routes.file_processor")
    @patch("app.core.validators.ParameterValidator.validate_compliance_request")
    def test_check_compliance_success(
        self,
        mock_validate,
        mock_file_processor,
        mock_ai_service,
        mock_sensitive_service,
        mock_result_processor,
        client,
        sample_compliance_request,
    ):
        """测试合规性检查成功"""
        # 模拟验证器
        mock_request = MagicMock()
        mock_request.get_project_info.return_value = MagicMock()
        mock_request.bidding_doc = MagicMock()
        mock_validate.return_value = mock_request

        # 模拟文件处理器
        mock_file_processor.process_file.return_value = "文档内容"

        # 模拟AI服务
        mock_ai_result = MagicMock()
        mock_ai_result.checkResultArr = []
        mock_ai_service.check_compliance.return_value = mock_ai_result

        # 模拟敏感词服务
        mock_sensitive_service.detect_with_fallback.return_value = []

        # 模拟结果处理器
        mock_final_result = MagicMock()
        mock_final_result.sensitiveWordsArr = []
        mock_final_result.checkResultArr = []
        mock_result_processor.process_with_fallback.return_value = mock_final_result

        response = client.post(
            "/api/v1/check-compliance", json=sample_compliance_request
        )

        assert response.status_code == 200

        # 验证各服务被调用
        mock_validate.assert_called_once()
        mock_file_processor.process_file.assert_called_once()
        mock_ai_service.check_compliance.assert_called_once()
        mock_sensitive_service.detect_with_fallback.assert_called_once()
        mock_result_processor.process_with_fallback.assert_called_once()

    def test_check_compliance_invalid_request(self, client):
        """测试无效请求"""
        invalid_request = {
            "bidding_doc": {
                "filename": "test.txt",  # 无效扩展名
                "extension": ".txt",
                "mime_type": "text/plain",
                "size": 1024,
                "url": "http://example.com/test.txt",
            },
            "procurement_project_type": "无效类型",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
        }

        response = client.post("/api/v1/check-compliance", json=invalid_request)

        # 应该返回验证错误
        assert response.status_code == 400

    @patch("app.core.validators.ParameterValidator.validate_compliance_request")
    def test_check_compliance_validation_error(
        self, mock_validate, client, sample_compliance_request
    ):
        """测试验证异常"""
        from app.core.exceptions import ValidationError

        mock_validate.side_effect = ValidationError("验证失败", field="test")

        response = client.post(
            "/api/v1/check-compliance", json=sample_compliance_request
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "验证失败" in data["message"]


class TestFileValidation:
    """文件验证接口测试"""

    @patch("app.api.routes.file_processor")
    def test_validate_file_success(self, mock_file_processor, client):
        """测试文件验证成功"""
        file_info_data = {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://example.com/test.docx",
        }

        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": True,
            "preferred_method": "MarkItDown",
            "estimated_time": 2.0,
            "warnings": [],
        }

        response = client.post("/api/v1/validate-file", json=file_info_data)

        assert response.status_code == 200
        data = response.json()
        assert "file_info" in data
        assert "processing_capability" in data
        assert data["processing_capability"]["can_process"] is True

    def test_validate_file_invalid_data(self, client):
        """测试无效文件数据"""
        invalid_file_data = {
            "filename": "test.txt",
            "extension": ".txt",  # 不支持的扩展名
            "mime_type": "text/plain",
            "size": 1024,
            "url": "http://example.com/test.txt",
        }

        response = client.post("/api/v1/validate-file", json=invalid_file_data)

        assert response.status_code == 400


class TestSensitiveWordEndpoints:
    """敏感词相关接口测试"""

    @patch("app.api.routes.sensitive_word_service")
    def test_sensitive_word_stats(self, mock_service, client):
        """测试敏感词统计接口"""
        mock_service.get_stats.return_value = {"total_words": 1000}
        mock_service.check_health.return_value = True
        mock_service.get_service_info.return_value = {"base_url": "http://test"}

        response = client.get("/api/v1/sensitive-word-stats")

        assert response.status_code == 200
        data = response.json()
        assert "health" in data
        assert "stats" in data
        assert "service_info" in data
        assert data["health"] is True

    @patch("app.api.routes.sensitive_word_service")
    def test_reload_sensitive_words_success(self, mock_service, client):
        """测试重载敏感词成功"""
        mock_service.reload_sensitive_words.return_value = True

        response = client.post("/api/v1/reload-sensitive-words")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "成功" in data["message"]

    @patch("app.api.routes.sensitive_word_service")
    def test_reload_sensitive_words_failure(self, mock_service, client):
        """测试重载敏感词失败"""
        mock_service.reload_sensitive_words.return_value = False

        response = client.post("/api/v1/reload-sensitive-words")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert "失败" in data["message"]


class TestErrorHandling:
    """错误处理测试"""

    @patch("app.api.routes.file_processor")
    def test_api_error_handler_file_processing_error(
        self, mock_file_processor, client, sample_compliance_request
    ):
        """测试API错误处理器处理文件处理错误"""
        from app.core.exceptions import FileProcessingError

        mock_file_processor.process_file.side_effect = FileProcessingError(
            "文件处理失败", filename="test.docx"
        )

        response = client.post(
            "/api/v1/check-compliance", json=sample_compliance_request
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False
        assert "文件处理失败" in data["message"]

    @patch("app.api.routes.ai_model_service")
    def test_api_error_handler_ai_model_error(
        self, mock_ai_service, client, sample_compliance_request
    ):
        """测试API错误处理器处理AI模型错误"""
        from app.core.exceptions import AIModelError

        mock_ai_service.check_compliance.side_effect = AIModelError(
            "模型调用失败", model_name="test-model"
        )

        response = client.post(
            "/api/v1/check-compliance", json=sample_compliance_request
        )

        assert response.status_code == 502
        data = response.json()
        assert data["success"] is False
        assert "AI模型服务暂时不可用" in data["message"]
