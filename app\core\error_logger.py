# -*- coding: utf-8 -*-
"""
增强的错误日志记录工具
"""

import json
import traceback
from typing import Any, Dict, Optional
from datetime import datetime

from app.core.logger import log


class EnhancedErrorLogger:
    """增强的错误日志记录器"""

    @staticmethod
    def log_service_error(
        service_name: str,
        operation: str,
        error: Exception,
        request_id: str = "",
        context: Optional[Dict[str, Any]] = None,
        include_traceback: bool = True,
    ):
        """
        记录服务错误的详细信息

        Args:
            service_name: 服务名称
            operation: 操作名称
            error: 异常对象
            request_id: 请求ID
            context: 上下文信息
            include_traceback: 是否包含堆栈跟踪
        """
        error_info = {
            "service": service_name,
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
        }

        # 添加上下文信息
        if context:
            error_info["context"] = context

        # 添加异常特有的属性
        if hasattr(error, "error_code"):
            error_info["error_code"] = error.error_code
        if hasattr(error, "status_code"):
            error_info["status_code"] = error.status_code
        if hasattr(error, "api_url"):
            error_info["api_url"] = error.api_url

        # 记录错误日志
        log.error(
            f"服务错误 | {service_name}.{operation} | "
            f"ID: {request_id} | 错误: {error_info['error_type']}: {error_info['error_message']}"
        )

        # 记录详细的错误信息（调试级别）
        log.debug(
            f"详细错误信息: {json.dumps(error_info, ensure_ascii=False, indent=2)}"
        )

        # 记录堆栈跟踪（调试级别）
        if include_traceback:
            log.debug(f"堆栈跟踪:\n{traceback.format_exc()}")

    @staticmethod
    def log_data_processing_error(
        stage: str,
        input_data: Any,
        error: Exception,
        request_id: str = "",
        partial_output: Any = None,
    ):
        """
        记录数据处理错误

        Args:
            stage: 处理阶段
            input_data: 输入数据
            error: 异常对象
            request_id: 请求ID
            partial_output: 部分输出数据
        """
        context = {
            "stage": stage,
            "input_type": type(input_data).__name__,
            "input_size": len(str(input_data)) if input_data else 0,
        }

        # 安全地记录输入数据的摘要
        if input_data:
            if isinstance(input_data, (list, tuple)):
                context["input_length"] = len(input_data)
                context["input_sample"] = (
                    str(input_data[:3]) if len(input_data) > 0 else "[]"
                )
            elif isinstance(input_data, dict):
                context["input_keys"] = list(input_data.keys())
                context["input_sample"] = {
                    k: str(v)[:100] for k, v in list(input_data.items())[:3]
                }
            elif isinstance(input_data, str):
                context["input_length"] = len(input_data)
                context["input_sample"] = (
                    input_data[:200] + "..." if len(input_data) > 200 else input_data
                )

        # 记录部分输出信息
        if partial_output is not None:
            context["partial_output_type"] = type(partial_output).__name__
            context["partial_output_size"] = len(str(partial_output))

        EnhancedErrorLogger.log_service_error(
            "DataProcessor", stage, error, request_id, context
        )

    @staticmethod
    def log_api_call_error(
        api_name: str,
        url: str,
        method: str,
        error: Exception,
        request_id: str = "",
        request_data: Any = None,
        response_data: Any = None,
        status_code: Optional[int] = None,
    ):
        """
        记录API调用错误

        Args:
            api_name: API名称
            url: 请求URL
            method: HTTP方法
            error: 异常对象
            request_id: 请求ID
            request_data: 请求数据
            response_data: 响应数据
            status_code: HTTP状态码
        """
        context = {
            "api_name": api_name,
            "url": url,
            "method": method,
            "status_code": status_code,
        }

        # 安全地记录请求数据
        if request_data:
            context["request_size"] = len(str(request_data))
            if isinstance(request_data, dict):
                # 只记录关键字段，避免敏感信息
                safe_fields = [
                    "content_length",
                    "is_government_procurement",
                    "filename",
                    "size",
                ]
                context["request_summary"] = {
                    k: v for k, v in request_data.items() if k in safe_fields
                }

        # 安全地记录响应数据
        if response_data:
            context["response_size"] = len(str(response_data))
            context["response_sample"] = (
                str(response_data)[:500] + "..."
                if len(str(response_data)) > 500
                else str(response_data)
            )

        EnhancedErrorLogger.log_service_error(
            "APIClient", f"{method}_{api_name}", error, request_id, context
        )

    @staticmethod
    def log_performance_issue(
        operation: str,
        duration: float,
        threshold: float,
        request_id: str = "",
        context: Optional[Dict[str, Any]] = None,
    ):
        """
        记录性能问题

        Args:
            operation: 操作名称
            duration: 实际耗时
            threshold: 阈值
            request_id: 请求ID
            context: 上下文信息
        """
        performance_info = {
            "operation": operation,
            "duration": duration,
            "threshold": threshold,
            "excess": duration - threshold,
            "severity": "critical" if duration > threshold * 2 else "warning",
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
        }

        if context:
            performance_info["context"] = context

        log.warning(
            f"性能问题 | {operation} | ID: {request_id} | "
            f"耗时: {duration:.3f}秒 > 阈值: {threshold:.3f}秒 | "
            f"超出: {duration - threshold:.3f}秒"
        )

        log.debug(
            f"性能问题详情: {json.dumps(performance_info, ensure_ascii=False, indent=2)}"
        )

    @staticmethod
    def log_business_logic_error(
        business_operation: str,
        error: Exception,
        request_id: str = "",
        user_context: Optional[Dict[str, Any]] = None,
        system_context: Optional[Dict[str, Any]] = None,
    ):
        """
        记录业务逻辑错误

        Args:
            business_operation: 业务操作名称
            error: 异常对象
            request_id: 请求ID
            user_context: 用户上下文
            system_context: 系统上下文
        """
        context = {}

        if user_context:
            context["user_context"] = user_context

        if system_context:
            context["system_context"] = system_context

        EnhancedErrorLogger.log_service_error(
            "BusinessLogic", business_operation, error, request_id, context
        )


# 创建全局实例
error_logger = EnhancedErrorLogger()
