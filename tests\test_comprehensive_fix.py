#!/usr/bin/env python3
"""
测试全面的JSON修复功能
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_comprehensive_fix():
    """测试全面的JSON修复功能"""
    print("🔍 测试全面的JSON修复功能...")

    try:
        from app.services.ai_model_service import AIModelService

        ai_service = AIModelService()

        # 模拟有问题的JSON（基于实际日志中的问题）
        problematic_json = """{
  "checkResultArr": [
    {
      "quesType": "逻辑性/规范性",
      "quesDesc": "招标文件目录中"第二章 投标人须知前附表"的页码标注为"4"，但实际内容位于"第八章 电子招投标相关要求"中，从第85页开始，导致目录与实际内容不符。",
      "originalArr": [
        "目 录 第二章 投标人须知前附表 4"
      ],
      "point": "文件结构检查",
      "advice": "修正目录页码，或将"投标人须知前附表"独立成章并放置在正确页码，确保目录与实际内容严格对应。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "招标文件在定义"近X年内"时使用了"X"作为占位符，未明确具体的年限，导致该条款不清晰。",
      "originalArr": [
        "第三章 投标人须知 2.5 近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。"
      ],
      "point": "文本准确性",
      "advice": "明确"X"的具体数值，例如"近3年内"或"近5年内"，以消除歧义。"
    },
    {
      "quesType": "风险管理",
      "quesDesc": "招标文件将"降幅过小"作为围标、串标嫌疑的认定标准，此表述过于主观，缺乏量化标准，可能导致评标委员会在实际操作中自由裁量权过大，引发争议。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.4 (12) 投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的。"
      ],
      "point": "风险管理",
      "advice": "细化"降幅过小"的具体量化标准，或将其与"低于成本报价"等更客观的认定标准相结合，以减少主观判断，确保公平公正。"
    }
  ]
}"""

        print("原始有问题的JSON长度:", len(problematic_json))
        print("包含的引号数量:")
        left_quote = '"'
        right_quote = '"'
        print(f"  中文引号左: {problematic_json.count(left_quote)}")
        print(f"  中文引号右: {problematic_json.count(right_quote)}")

        # 测试全面修复
        fixed_json = ai_service._comprehensive_json_fix(problematic_json)

        print(f"\n修复后JSON长度: {len(fixed_json)}")
        print("修复后的引号数量:")
        print(f"  中文引号左: {fixed_json.count(left_quote)}")
        print(f"  中文引号右: {fixed_json.count(right_quote)}")

        # 尝试解析修复后的JSON
        try:
            parsed = json.loads(fixed_json)
            print(f"\n✅ JSON解析成功!")
            print(f"   检查结果数量: {len(parsed.get('checkResultArr', []))}")

            if parsed.get("checkResultArr"):
                for i, item in enumerate(parsed["checkResultArr"][:3]):
                    print(
                        f"   问题{i+1}: {item.get('quesType')} - {item.get('quesDesc', '')[:50]}..."
                    )

            return True

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析仍然失败: {e}")
            print(f"   错误位置: 第{e.lineno}行，第{e.colno}列")
            print(f"   错误附近内容: {fixed_json[max(0, e.pos-50):e.pos+50]}")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_clean_json_data():
    """测试完整的clean_json_data方法"""
    print("\n🔍 测试完整的clean_json_data方法...")

    try:
        from app.services.ai_model_service import AIModelService

        ai_service = AIModelService()

        # 模拟AI模型的完整响应
        ai_response = """```json
{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件在定义"近X年内"时使用了"X"作为占位符，未明确具体的年限。",
      "originalArr": [
        "第三章 投标人须知 2.5 近X年内：按招标文件要求。"
      ],
      "point": "文本准确性",
      "advice": "明确"X"的具体数值，例如"近3年内"。"
    }
  ]
}
```"""

        print("原始AI响应长度:", len(ai_response))

        # 测试完整的清理流程
        cleaned_json = ai_service.clean_json_data(ai_response)

        print("清理后JSON长度:", len(cleaned_json))

        # 尝试解析
        try:
            parsed = json.loads(cleaned_json)
            print(f"✅ 完整流程解析成功!")
            print(f"   检查结果数量: {len(parsed.get('checkResultArr', []))}")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ 完整流程解析失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试全面JSON修复功能...")

    success = True
    success &= test_comprehensive_fix()
    success &= test_clean_json_data()

    if success:
        print("\n🎉 全面JSON修复功能测试通过!")
        print("现在应该能正确解析包含大量中文引号的JSON了。")
    else:
        print("\n❌ 全面JSON修复功能仍有问题")
        sys.exit(1)
