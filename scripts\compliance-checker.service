# systemd服务配置文件 - 招标文件合规性检查助手
# 安装位置: /etc/systemd/system/compliance-checker.service

[Unit]
Description=Bidding Document Compliance Checker API Service
Documentation=https://github.com/example/compliance-checker
After=network.target network-online.target
Wants=network-online.target
Requires=network.target

[Service]
Type=exec
User=appuser
Group=appuser
WorkingDirectory=/opt/compliance-checker
Environment=PATH=/opt/compliance-checker/venv/bin
Environment=PYTHONPATH=/opt/compliance-checker
Environment=PYTHONUNBUFFERED=1

# 主执行命令
ExecStart=/opt/compliance-checker/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8088 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /opt/compliance-checker/logs/access.log \
    --error-logfile /opt/compliance-checker/logs/error.log \
    --log-level info \
    --pid /var/run/compliance-checker.pid

# 重载命令
ExecReload=/bin/kill -s HUP $MAINPID

# 停止命令
ExecStop=/bin/kill -s TERM $MAINPID

# 启动前检查
ExecStartPre=/bin/mkdir -p /opt/compliance-checker/logs
ExecStartPre=/bin/chown -R appuser:appuser /opt/compliance-checker/logs

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/compliance-checker/logs /opt/compliance-checker/temp
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 环境变量文件
EnvironmentFile=-/opt/compliance-checker/.env

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=compliance-checker

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target