# -*- coding: utf-8 -*-
"""
负载测试和性能验证
测试系统在高负载下的性能表现
"""

import pytest
import asyncio
import time
import concurrent.futures
from typing import List
from unittest.mock import patch, MagicMock

from app.core.cache_manager import MemoryCache, ResourceManager
from app.core.queue_manager import RequestQueue, RequestPriority
from app.services.compliance_service import compliance_service
from app.models.schemas import (
    ComplianceCheckRequest,
    FileInfo,
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


class TestLoadPerformance:
    """负载性能测试"""

    def test_cache_concurrent_access(self):
        """测试缓存并发访问性能"""
        cache = MemoryCache(max_size_mb=10, ttl_seconds=60)

        def cache_worker(worker_id: int, operations: int = 100):
            """缓存工作线程"""
            results = {"hits": 0, "misses": 0, "sets": 0}

            for i in range(operations):
                key = f"worker_{worker_id}_key_{i}"
                value = f"worker_{worker_id}_value_{i}"

                # 设置缓存
                if cache.set(key, value):
                    results["sets"] += 1

                # 获取缓存
                cached_value = cache.get(key)
                if cached_value == value:
                    results["hits"] += 1
                else:
                    results["misses"] += 1

            return results

        # 启动多个并发工作线程
        num_workers = 10
        operations_per_worker = 50

        start_time = time.time()

        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(cache_worker, i, operations_per_worker)
                for i in range(num_workers)
            ]

            results = [
                future.result() for future in concurrent.futures.as_completed(futures)
            ]

        end_time = time.time()

        # 验证结果
        total_operations = num_workers * operations_per_worker
        total_hits = sum(r["hits"] for r in results)
        total_sets = sum(r["sets"] for r in results)

        assert total_sets > 0
        assert total_hits > 0

        # 性能指标
        duration = end_time - start_time
        ops_per_second = (total_operations * 2) / duration  # 每个操作包含set和get

        print(f"缓存并发测试: {num_workers}个工作线程, {total_operations}次操作")
        print(f"总耗时: {duration:.3f}秒, 操作/秒: {ops_per_second:.1f}")
        print(f"缓存命中率: {total_hits/total_operations:.2%}")

        # 性能要求：至少1000 ops/sec
        assert ops_per_second > 1000

    def test_memory_manager_under_pressure(self):
        """测试内存管理器在压力下的表现"""
        manager = ResourceManager()

        # 创建大量对象模拟内存压力
        memory_hogs = []

        try:
            # 分配内存直到达到阈值
            for i in range(100):
                # 每次分配10MB数据
                data = [0] * (10 * 1024 * 1024 // 8)  # 10MB的整数列表
                memory_hogs.append(data)

                # 检查内存使用
                usage = manager.get_memory_usage()
                if usage.get("rss_mb", 0) > 500:  # 超过500MB时停止
                    break

            # 测试内存优化
            start_time = time.time()
            optimization_result = manager.optimize_memory()
            optimization_time = time.time() - start_time

            # 验证优化结果
            assert "actions_taken" in optimization_result
            assert "memory_usage" in optimization_result
            assert optimization_time < 5.0  # 优化应该在5秒内完成

            print(f"内存优化耗时: {optimization_time:.3f}秒")
            print(f"执行的优化操作: {optimization_result['actions_taken']}")

        finally:
            # 清理内存
            del memory_hogs

    @pytest.mark.asyncio
    async def test_queue_high_concurrency(self):
        """测试队列高并发处理"""
        queue = RequestQueue(max_concurrent=5, max_queue_size=50)
        await queue.start_workers(5)

        try:

            async def simple_task(task_id: str, delay: float = 0.01) -> str:
                """简单任务"""
                await asyncio.sleep(delay)
                return f"completed_{task_id}"

            # 提交大量任务
            num_tasks = 30
            task_ids = []

            start_time = time.time()

            for i in range(num_tasks):
                task_id = await queue.enqueue(
                    simple_task, f"task_{i}", priority=RequestPriority.NORMAL
                )
                task_ids.append(task_id)

            # 等待所有任务完成
            max_wait_time = 10.0  # 最多等待10秒
            wait_start = time.time()

            while time.time() - wait_start < max_wait_time:
                completed_count = 0
                for task_id in task_ids:
                    status = await queue.get_request_status(task_id)
                    if status and status["status"] == "completed":
                        completed_count += 1

                if completed_count == num_tasks:
                    break

                await asyncio.sleep(0.1)

            end_time = time.time()

            # 验证结果
            final_completed = 0
            for task_id in task_ids:
                status = await queue.get_request_status(task_id)
                if status and status["status"] == "completed":
                    final_completed += 1

            # 获取队列统计
            stats = queue.get_queue_stats()

            duration = end_time - start_time
            throughput = final_completed / duration

            print(f"队列并发测试: {num_tasks}个任务")
            print(f"完成任务: {final_completed}/{num_tasks}")
            print(f"总耗时: {duration:.3f}秒, 吞吐量: {throughput:.1f} tasks/sec")
            print(f"成功率: {stats['success_rate']:.2%}")

            # 性能要求
            assert final_completed >= num_tasks * 0.9  # 至少90%成功
            assert throughput > 10  # 至少10 tasks/sec

        finally:
            await queue.stop_workers()

    def test_cache_memory_efficiency(self):
        """测试缓存内存效率"""
        cache = MemoryCache(max_size_mb=5, ttl_seconds=60)

        # 测试不同大小的数据缓存效率
        test_cases = [
            ("small", "x" * 100),  # 100字节
            ("medium", "x" * 10000),  # 10KB
            ("large", "x" * 1000000),  # 1MB
        ]

        results = {}

        for size_name, data in test_cases:
            start_time = time.time()

            # 缓存100个该大小的项目
            successful_sets = 0
            for i in range(100):
                key = f"{size_name}_{i}"
                if cache.set(key, data):
                    successful_sets += 1

            # 读取所有项目
            successful_gets = 0
            for i in range(100):
                key = f"{size_name}_{i}"
                if cache.get(key) == data:
                    successful_gets += 1

            end_time = time.time()

            results[size_name] = {
                "successful_sets": successful_sets,
                "successful_gets": successful_gets,
                "duration": end_time - start_time,
                "data_size": len(data),
            }

            # 清理缓存
            cache.clear()

        # 验证结果
        for size_name, result in results.items():
            print(f"{size_name}数据 ({result['data_size']}字节):")
            print(f"  成功缓存: {result['successful_sets']}/100")
            print(f"  成功读取: {result['successful_gets']}/100")
            print(f"  耗时: {result['duration']:.3f}秒")

            # 小数据应该能完全缓存
            if size_name == "small":
                assert result["successful_sets"] == 100
                assert result["successful_gets"] == 100

    @pytest.mark.asyncio
    async def test_queue_priority_performance(self):
        """测试队列优先级处理性能"""
        queue = RequestQueue(max_concurrent=3, max_queue_size=30)
        await queue.start_workers(3)

        try:

            async def priority_task(task_id: str, priority_name: str) -> str:
                """优先级任务"""
                await asyncio.sleep(0.05)  # 50ms处理时间
                return f"{priority_name}_{task_id}"

            # 提交不同优先级的任务
            task_info = []

            # 低优先级任务
            for i in range(5):
                task_id = await queue.enqueue(
                    priority_task, f"low_{i}", "LOW", priority=RequestPriority.LOW
                )
                task_info.append((task_id, "LOW", time.time()))

            # 普通优先级任务
            for i in range(5):
                task_id = await queue.enqueue(
                    priority_task,
                    f"normal_{i}",
                    "NORMAL",
                    priority=RequestPriority.NORMAL,
                )
                task_info.append((task_id, "NORMAL", time.time()))

            # 高优先级任务
            for i in range(5):
                task_id = await queue.enqueue(
                    priority_task, f"high_{i}", "HIGH", priority=RequestPriority.HIGH
                )
                task_info.append((task_id, "HIGH", time.time()))

            # 等待所有任务完成
            await asyncio.sleep(2.0)

            # 分析完成顺序
            completed_tasks = []
            for task_id, priority, submit_time in task_info:
                status = await queue.get_request_status(task_id)
                if status and status["status"] == "completed":
                    completed_tasks.append(
                        (priority, submit_time, status.get("completed_at"))
                    )

            # 验证高优先级任务优先完成
            high_priority_completed = [t for t in completed_tasks if t[0] == "HIGH"]
            low_priority_completed = [t for t in completed_tasks if t[0] == "LOW"]

            print(f"高优先级任务完成: {len(high_priority_completed)}/5")
            print(f"低优先级任务完成: {len(low_priority_completed)}/5")
            print(f"总完成任务: {len(completed_tasks)}/15")

            # 性能要求：高优先级任务应该优先完成
            assert len(high_priority_completed) >= 4  # 至少4个高优先级任务完成
            assert len(completed_tasks) >= 12  # 至少80%的任务完成

        finally:
            await queue.stop_workers()

    def test_resource_monitoring_accuracy(self):
        """测试资源监控准确性"""
        manager = ResourceManager()

        # 获取基线内存使用
        baseline_usage = manager.get_memory_usage()
        baseline_rss = baseline_usage.get("rss_mb", 0)

        # 分配已知大小的内存
        allocated_data = []
        allocation_size_mb = 50  # 分配50MB

        try:
            # 分配内存
            for i in range(10):
                # 每次分配5MB
                data = bytearray(5 * 1024 * 1024)
                allocated_data.append(data)

            # 检查内存使用变化
            after_usage = manager.get_memory_usage()
            after_rss = after_usage.get("rss_mb", 0)

            memory_increase = after_rss - baseline_rss

            print(f"基线内存: {baseline_rss:.1f}MB")
            print(f"分配后内存: {after_rss:.1f}MB")
            print(f"内存增长: {memory_increase:.1f}MB")
            print(f"预期增长: {allocation_size_mb}MB")

            # 验证内存监控准确性（允许一定误差）
            assert memory_increase > allocation_size_mb * 0.5  # 至少检测到50%的增长
            assert (
                memory_increase < allocation_size_mb * 3
            )  # 不超过3倍（考虑内存碎片等）

        finally:
            # 清理内存
            del allocated_data

    @pytest.mark.asyncio
    async def test_integrated_performance_scenario(self):
        """集成性能场景测试"""
        # 创建性能优化组件
        cache = MemoryCache(max_size_mb=20, ttl_seconds=300)
        queue = RequestQueue(max_concurrent=4, max_queue_size=20)
        manager = ResourceManager()

        await queue.start_workers(4)

        try:

            async def complex_task(task_id: str) -> str:
                """复杂任务，包含缓存和计算"""
                # 检查缓存
                cache_key = f"result_{task_id}"
                cached_result = cache.get(cache_key)

                if cached_result:
                    return f"cached_{cached_result}"

                # 模拟复杂计算
                await asyncio.sleep(0.1)
                result = f"computed_{task_id}"

                # 缓存结果
                cache.set(cache_key, result)

                return result

            # 提交任务
            num_tasks = 15
            task_ids = []

            start_time = time.time()

            for i in range(num_tasks):
                task_id = await queue.enqueue(complex_task, f"task_{i}")
                task_ids.append(task_id)

            # 等待完成
            await asyncio.sleep(3.0)

            # 再次提交相同任务（应该命中缓存）
            for i in range(num_tasks):
                task_id = await queue.enqueue(complex_task, f"task_{i}")
                task_ids.append(task_id)

            # 等待所有任务完成
            await asyncio.sleep(2.0)

            end_time = time.time()

            # 统计结果
            completed_count = 0
            for task_id in task_ids:
                status = await queue.get_request_status(task_id)
                if status and status["status"] == "completed":
                    completed_count += 1

            # 获取统计信息
            queue_stats = queue.get_queue_stats()
            cache_stats = cache.get_stats()
            memory_usage = manager.get_memory_usage()

            duration = end_time - start_time
            throughput = completed_count / duration

            print(f"集成性能测试结果:")
            print(f"  任务完成: {completed_count}/{len(task_ids)}")
            print(f"  总耗时: {duration:.3f}秒")
            print(f"  吞吐量: {throughput:.1f} tasks/sec")
            print(f"  队列成功率: {queue_stats['success_rate']:.2%}")
            print(f"  缓存命中率: {cache_stats['hit_rate']:.2%}")
            print(f"  内存使用: {memory_usage.get('rss_mb', 0):.1f}MB")

            # 性能要求
            assert completed_count >= len(task_ids) * 0.9  # 90%成功率
            assert cache_stats["hit_rate"] > 0.3  # 30%缓存命中率
            assert throughput > 5  # 至少5 tasks/sec

        finally:
            await queue.stop_workers()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
