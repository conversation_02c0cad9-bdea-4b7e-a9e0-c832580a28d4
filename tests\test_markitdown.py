#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 markitdown 是否可用
"""

try:
    import markitdown
    print("✅ markitdown 已安装")
    print(f"版本: {markitdown.__version__ if hasattr(markitdown, '__version__') else '未知'}")
    
    # 测试创建实例
    md = markitdown.MarkItDown()
    print("✅ MarkItDown 实例创建成功")
    
except ImportError as e:
    print("❌ markitdown 未安装")
    print(f"错误: {e}")
    
except Exception as e:
    print("❌ markitdown 导入时出现其他错误")
    print(f"错误: {e}")
