# -*- coding: utf-8 -*-
"""
AI模型服务测试
"""

import json
import pytest
from unittest.mock import patch, MagicMock

from app.services.ai_model_service import AIModelService, AIModelError, ai_model_service
from app.models.schemas import ProjectInfo, CheckResultItem, ComplianceCheckResponse
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    QuestionType,
)


class TestAIModelError:
    """AI模型异常测试"""

    def test_ai_model_error_basic(self):
        """测试基础AI模型异常"""
        error = AIModelError("模型调用失败")
        assert str(error) == "模型调用失败"
        assert error.error_type == "AI_MODEL_ERROR"

    def test_ai_model_error_with_details(self):
        """测试带详细信息的AI模型异常"""
        original_error = ValueError("原始错误")
        error = AIModelError(
            "模型调用失败",
            model_name="deepseek-v3",
            error_code="rate_limit",
            original_error=original_error,
        )

        error_str = str(error)
        assert "模型调用失败" in error_str
        assert "deepseek-v3" in error_str
        assert "rate_limit" in error_str
        assert "原始错误" in error_str


class TestAIModelService:
    """AI模型服务测试"""

    @pytest.fixture
    def service(self):
        """AI模型服务实例"""
        with patch("app.services.ai_model_service.OpenAI"):
            return AIModelService()

    @pytest.fixture
    def project_info(self):
        """项目信息"""
        return ProjectInfo(
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

    def test_initialization_success(self):
        """测试初始化成功"""
        with patch("app.services.ai_model_service.OpenAI") as mock_openai:
            mock_client = MagicMock()
            mock_openai.return_value = mock_client

            service = AIModelService()

            assert service.client == mock_client
            mock_openai.assert_called_once()

    def test_initialization_failure(self):
        """测试初始化失败"""
        with patch("app.services.ai_model_service.OpenAI") as mock_openai:
            mock_openai.side_effect = Exception("连接失败")

            with pytest.raises(AIModelError) as exc_info:
                AIModelService()

            error = exc_info.value
            assert "初始化失败" in str(error)

    def test_build_system_prompt_service_type(self, service, project_info):
        """测试构建系统提示词 - 服务类项目"""
        prompt = service.build_system_prompt(project_info)

        assert "招标文件审查与风险管控专家" in prompt
        assert "服务类项目特殊要求" in prompt
        assert "政府采购特殊要求" in prompt
        assert "公开招标特殊要求" in prompt
        assert "JSON格式" in prompt

    def test_build_system_prompt_goods_type(self, service):
        """测试构建系统提示词 - 货物类项目"""
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.LEGAL_BIDDING,
            bidding_procurement_method=BiddingProcurementMethod.INVITATION_BIDDING,
        )

        prompt = service.build_system_prompt(project_info)

        assert "货物类项目特殊要求" in prompt
        assert "依法招标特殊要求" in prompt
        assert "邀请招标特殊要求" in prompt

    def test_build_system_prompt_engineering_type(self, service):
        """测试构建系统提示词 - 工程类项目"""
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.ENGINEERING,
            project_category=ProjectCategory.NON_LEGAL_BIDDING,
            bidding_procurement_method=BiddingProcurementMethod.COMPETITIVE_CONSULTATION,
        )

        prompt = service.build_system_prompt(project_info)

        assert "工程类项目特殊要求" in prompt
        assert "竞争性磋商特殊要求" in prompt

    def test_clean_json_data_valid_json(self, service):
        """测试清理有效JSON数据"""
        raw_data = '{"checkResultArr": [{"quesType": "合规性", "quesDesc": "测试"}]}'
        cleaned = service.clean_json_data(raw_data)

        # 验证可以解析
        parsed = json.loads(cleaned)
        assert "checkResultArr" in parsed

    def test_clean_json_data_with_markdown(self, service):
        """测试清理带markdown标记的JSON"""
        raw_data = '```json\n{"checkResultArr": []}\n```'
        cleaned = service.clean_json_data(raw_data)

        parsed = json.loads(cleaned)
        assert "checkResultArr" in parsed

    def test_clean_json_data_with_comments(self, service):
        """测试清理带注释的JSON"""
        raw_data = """
        {
            // 这是注释
            "checkResultArr": [
                /* 多行注释 */
                {"quesType": "合规性"}
            ]
        }
        """
        cleaned = service.clean_json_data(raw_data)

        parsed = json.loads(cleaned)
        assert "checkResultArr" in parsed

    def test_clean_json_data_with_trailing_comma(self, service):
        """测试清理尾随逗号的JSON"""
        raw_data = '{"checkResultArr": [{"quesType": "合规性",}],}'
        cleaned = service.clean_json_data(raw_data)

        parsed = json.loads(cleaned)
        assert "checkResultArr" in parsed

    def test_clean_json_data_invalid_json(self, service):
        """测试清理无效JSON"""
        raw_data = "invalid json data"
        cleaned = service.clean_json_data(raw_data)

        # 应该返回默认空结果JSON
        assert cleaned == '{"checkResultArr": []}'

    def test_estimate_tokens(self, service):
        """测试token数量估算"""
        messages = [
            {"role": "system", "content": "你是专家"},
            {"role": "user", "content": "请分析这个文档"},
        ]

        tokens = service._estimate_tokens(messages)

        assert tokens > 0
        assert isinstance(tokens, int)

    def test_truncate_messages_normal(self, service):
        """测试正常消息截断"""
        messages = [
            {"role": "system", "content": "系统消息"},
            {"role": "user", "content": "用户消息"},
        ]

        truncated = service._truncate_messages(messages)

        assert len(truncated) == 2
        assert truncated[0]["role"] == "system"
        assert truncated[1]["role"] == "user"

    @patch("app.services.ai_model_service.settings")
    def test_truncate_messages_long_content(self, mock_settings, service):
        """测试长内容截断"""
        mock_settings.max_context_length = 100

        long_content = "很长的内容 " * 1000
        messages = [
            {"role": "system", "content": "系统消息"},
            {"role": "user", "content": long_content},
        ]

        truncated = service._truncate_messages(messages)

        assert len(truncated) == 2
        assert len(truncated[1]["content"]) < len(long_content)
        assert "...(内容已截取)" in truncated[1]["content"]

    def test_call_model_success(self, service):
        """测试模型调用成功"""
        # 模拟OpenAI响应
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = '{"checkResultArr": []}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        service.client.chat.completions.create.return_value = mock_response

        messages = [{"role": "user", "content": "测试消息"}]
        result = service.call_model(messages, "test-123")

        assert result == '{"checkResultArr": []}'
        service.client.chat.completions.create.assert_called_once()

    def test_call_model_no_client(self, service):
        """测试模型调用无客户端"""
        service.client = None

        messages = [{"role": "user", "content": "测试消息"}]

        with pytest.raises(AIModelError) as exc_info:
            service.call_model(messages)

        assert "未初始化" in str(exc_info.value)

    def test_call_model_api_error(self, service):
        """测试模型调用API错误"""
        service.client.chat.completions.create.side_effect = Exception("API错误")

        messages = [{"role": "user", "content": "测试消息"}]

        with pytest.raises(AIModelError) as exc_info:
            service.call_model(messages)

        assert "AI模型调用异常" in str(exc_info.value)

    @patch.object(AIModelService, "call_model")
    @patch.object(AIModelService, "build_system_prompt")
    def test_check_compliance_success(
        self, mock_build_prompt, mock_call_model, service, project_info
    ):
        """测试合规性检查成功"""
        mock_build_prompt.return_value = "系统提示词"
        mock_call_model.return_value = """
        {
            "checkResultArr": [
                {
                    "quesType": "合规性",
                    "quesDesc": "测试问题描述",
                    "originalArr": ["原文1", "原文2"],
                    "point": "质量控制要点",
                    "advice": "处理建议"
                }
            ]
        }
        """

        content = "招标文件内容"
        result = service.check_compliance(content, project_info, "test-123")

        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.checkResultArr) == 1

        check_item = result.checkResultArr[0]
        assert check_item.quesType == "合规性"
        assert check_item.quesDesc == "测试问题描述"
        assert check_item.originalArr == ["原文1", "原文2"]
        assert check_item.point == "质量控制要点"
        assert check_item.advice == "处理建议"

    @patch.object(AIModelService, "call_model")
    @patch.object(AIModelService, "build_system_prompt")
    def test_check_compliance_invalid_question_type(
        self, mock_build_prompt, mock_call_model, service, project_info
    ):
        """测试合规性检查无效问题类型"""
        mock_build_prompt.return_value = "系统提示词"
        mock_call_model.return_value = """
        {
            "checkResultArr": [
                {
                    "quesType": "无效类型",
                    "quesDesc": "测试问题描述",
                    "originalArr": ["原文1"],
                    "point": "质量控制要点",
                    "advice": "处理建议"
                }
            ]
        }
        """

        content = "招标文件内容"
        result = service.check_compliance(content, project_info)

        assert len(result.checkResultArr) == 1
        # 应该使用默认的问题类型
        assert result.checkResultArr[0].quesType == QuestionType.STANDARDIZATION.value

    @patch.object(AIModelService, "call_model")
    @patch.object(AIModelService, "build_system_prompt")
    def test_check_compliance_invalid_json(
        self, mock_build_prompt, mock_call_model, service, project_info
    ):
        """测试合规性检查无效JSON响应"""
        mock_build_prompt.return_value = "系统提示词"
        mock_call_model.return_value = "invalid json response"

        content = "招标文件内容"
        result = service.check_compliance(content, project_info)

        # 应该返回空结果
        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.checkResultArr) == 0

    @patch.object(AIModelService, "call_model")
    @patch.object(AIModelService, "build_system_prompt")
    def test_check_compliance_missing_field(
        self, mock_build_prompt, mock_call_model, service, project_info
    ):
        """测试合规性检查缺少字段"""
        mock_build_prompt.return_value = "系统提示词"
        mock_call_model.return_value = '{"other_field": "value"}'

        content = "招标文件内容"
        result = service.check_compliance(content, project_info)

        # 应该返回空结果
        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.checkResultArr) == 0

    @patch.object(AIModelService, "call_model")
    def test_check_compliance_model_error(self, mock_call_model, service, project_info):
        """测试合规性检查模型错误"""
        mock_call_model.side_effect = AIModelError("模型调用失败")

        content = "招标文件内容"

        with pytest.raises(AIModelError):
            service.check_compliance(content, project_info)

    def test_get_model_info(self, service):
        """测试获取模型信息"""
        info = service.get_model_info()

        assert "model_name" in info
        assert "base_url" in info
        assert "max_context_length" in info
        assert "max_output_tokens" in info
        assert "temperature" in info
        assert "top_p" in info
        assert "seed" in info
        assert "client_initialized" in info

        assert isinstance(info["client_initialized"], bool)


class TestGlobalService:
    """全局服务实例测试"""

    def test_global_service_exists(self):
        """测试全局服务实例存在"""
        assert ai_model_service is not None
        assert isinstance(ai_model_service, AIModelService)
