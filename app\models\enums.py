# -*- coding: utf-8 -*-
"""
枚举类型定义
"""

from enum import Enum


class ProcurementProjectType(str, Enum):
    """采购项目类型枚举"""

    SERVICE = "服务类"
    GOODS = "货物类"
    ENGINEERING = "工程类"


class ProjectCategory(str, Enum):
    """项目类别枚举"""

    GOVERNMENT_PROCUREMENT = "政府采购"
    LEGAL_BIDDING = "依法招标"
    NON_LEGAL_BIDDING = "非依法招标"


class BiddingProcurementMethod(str, Enum):
    """招标采购方式枚举"""

    PUBLIC_BIDDING = "公开招标"
    SINGLE_SOURCE = "单一来源"
    COMPETITIVE_CONSULTATION = "竞争性磋商"
    COMPETITIVE_CONSULTATION_INVITATION = "竞争性磋商邀请"
    INVITATION_BIDDING = "邀请招标"
    COMPETITIVE_NEGOTIATION = "竞争性谈判"
    OPEN_OUTCRY = "公开竞价"
    INVITATION_OUTCRY = "邀请竞价"
    INQUIRY = "询价"
    OTHER = "其他"
    COMPARISON_SELECTION = "比选"


class FileExtension(str, Enum):
    """支持的文件扩展名枚举"""

    DOCX = ".docx"
    PDF = ".pdf"


class MimeType(str, Enum):
    """支持的MIME类型枚举"""

    DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    PDF = "application/pdf"


class QuestionType(str, Enum):
    """问题类型枚举"""

    COMPLIANCE = "合规性"
    LOGIC = "逻辑性"
    RISK_MANAGEMENT = "风险管理"
    STANDARDIZATION = "规范性"
    FAIRNESS = "公平性"
    OPERABILITY = "可操作性"


class SensitiveWordType(str, Enum):
    """敏感词类型枚举"""

    FINANCE_CURRENCY = "财务与货币"
    OMITTED_WORDS = "错漏词汇"
    COMPUTER_SYSTEM = "电脑操作系统"
    LEGAL_REGULATIONS = "法律法规"
    INSTITUTION_NAME = "机构名称"
    MONITORING_BRAND = "监控品牌"
    AIR_CONDITIONING_BRAND = "空调品牌"
    PHONE_BRAND = "手机品牌"
    SUBJECTIVE_EVALUATION = "主观评价"
