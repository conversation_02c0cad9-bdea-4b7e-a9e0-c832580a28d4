# -*- coding: utf-8 -*-
"""
合规性检查服务集成测试
端到端测试完整的业务流程
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from io import BytesIO
import json

from app.services.compliance_service import compliance_service, ComplianceCheckPipeline
from app.models.schemas import (
    ComplianceCheckRequest,
    FileInfo,
    ProjectInfo,
    SensitiveWordItem,
    CheckResultItem,
)
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)
from app.core.exceptions import (
    FileProcessingError,
    AIModelError,
    ExternalServiceError,
)


class TestComplianceServiceIntegration:
    """合规性检查服务集成测试"""

    @pytest.fixture
    def sample_request(self):
        """创建示例请求"""
        file_info = FileInfo(
            filename="test_document.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024 * 1024,  # 1MB
            url="http://example.com/test_document.docx",
        )

        return ComplianceCheckRequest(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            bidding_doc=file_info,
        )

    @pytest.fixture
    def mock_file_content(self):
        """模拟文件内容"""
        return """
        # 招标文件

        ## 项目概述
        本项目为政府采购项目，采购医疗设备。

        ## 技术要求
        1. 设备必须符合国家标准
        2. 提供三年质保服务
        3. 具备完整的技术文档

        ## 商务要求
        1. 投标保证金：10万元
        2. 履约保证金：合同金额的5%
        3. 付款方式：验收合格后30天内付款
        """

    @pytest.fixture
    def mock_ai_response(self):
        """模拟AI检查响应"""
        return [
            CheckResultItem(
                quesType="合规性",
                quesDesc="设备技术参数不够详细",
                originalArr=["第三章 技术要求"],
                point="技术参数完整性",
                advice="建议补充详细的技术参数说明",
            ),
            CheckResultItem(
                quesType="规范性",
                quesDesc="付款条件过于宽松",
                originalArr=["第五章 商务条款"],
                point="付款条件合理性",
                advice="建议调整付款期限",
            ),
        ]

    @pytest.fixture
    def mock_sensitive_words(self):
        """模拟敏感词响应"""
        return [
            SensitiveWordItem(type="商业敏感", content="独家供应", num=1),
            SensitiveWordItem(type="地域限制", content="本地企业", num=2),
        ]

    @patch("app.services.compliance_service.file_processor")
    @patch("app.services.compliance_service.ai_model_service")
    @patch("app.services.compliance_service.sensitive_word_service")
    @patch("app.services.compliance_service.result_processor")
    def test_complete_pipeline_success(
        self,
        mock_result_processor,
        mock_sensitive_service,
        mock_ai_service,
        mock_file_processor,
        sample_request,
        mock_file_content,
        mock_ai_response,
        mock_sensitive_words,
    ):
        """测试完整流水线成功执行"""
        # 设置模拟返回值
        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": True,
            "warnings": [],
        }
        mock_file_processor.process_file.return_value = mock_file_content

        mock_ai_service.get_model_info.return_value = {"client_initialized": True}
        mock_ai_service.check_compliance.return_value = MagicMock(
            checkResultArr=mock_ai_response
        )

        mock_sensitive_service.check_health.return_value = True
        mock_sensitive_service.detect_with_fallback.return_value = mock_sensitive_words

        from app.models.schemas import ComplianceCheckResponse

        expected_response = ComplianceCheckResponse(
            sensitiveWordsArr=mock_sensitive_words,
            checkResultArr=mock_ai_response,
        )
        mock_result_processor.process_with_fallback.return_value = expected_response

        # 执行测试
        result = compliance_service.check_compliance(sample_request, "test-001")

        # 验证结果
        assert result is not None
        assert len(result.sensitiveWordsArr) == 2
        assert len(result.checkResultArr) == 2

        # 验证各服务被正确调用
        mock_file_processor.process_file.assert_called_once()
        mock_ai_service.check_compliance.assert_called_once()
        mock_sensitive_service.detect_with_fallback.assert_called_once()
        mock_result_processor.process_with_fallback.assert_called_once()

    @patch("app.services.compliance_service.file_processor")
    def test_file_processing_failure(self, mock_file_processor, sample_request):
        """测试文件处理失败的情况"""
        # 设置文件处理失败
        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": False,
            "warnings": ["不支持的文件格式"],
        }

        # 执行测试并验证异常
        with pytest.raises(FileProcessingError):
            compliance_service.check_compliance(sample_request, "test-002")

    @patch("app.services.compliance_service.file_processor")
    @patch("app.services.compliance_service.ai_model_service")
    @patch("app.services.compliance_service.sensitive_word_service")
    @patch("app.services.compliance_service.result_processor")
    def test_ai_service_fallback(
        self,
        mock_result_processor,
        mock_sensitive_service,
        mock_ai_service,
        mock_file_processor,
        sample_request,
        mock_file_content,
        mock_sensitive_words,
    ):
        """测试AI服务失败时的降级处理"""
        # 设置文件处理成功
        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": True,
            "warnings": [],
        }
        mock_file_processor.process_file.return_value = mock_file_content

        # 设置AI服务失败
        mock_ai_service.get_model_info.return_value = {"client_initialized": False}

        # 设置敏感词服务成功
        mock_sensitive_service.check_health.return_value = True
        mock_sensitive_service.detect_with_fallback.return_value = mock_sensitive_words

        from app.models.schemas import ComplianceCheckResponse

        expected_response = ComplianceCheckResponse(
            sensitiveWordsArr=mock_sensitive_words,
            checkResultArr=[],  # AI服务不可用时为空
        )
        mock_result_processor.process_with_fallback.return_value = expected_response

        # 执行测试
        result = compliance_service.check_compliance(sample_request, "test-003")

        # 验证结果
        assert result is not None
        assert len(result.sensitiveWordsArr) == 2
        assert len(result.checkResultArr) == 0  # AI检查被跳过

        # 验证AI服务没有被调用
        mock_ai_service.check_compliance.assert_not_called()

    @patch("app.services.compliance_service.file_processor")
    @patch("app.services.compliance_service.ai_model_service")
    @patch("app.services.compliance_service.sensitive_word_service")
    @patch("app.services.compliance_service.result_processor")
    def test_sensitive_word_service_fallback(
        self,
        mock_result_processor,
        mock_sensitive_service,
        mock_ai_service,
        mock_file_processor,
        sample_request,
        mock_file_content,
        mock_ai_response,
    ):
        """测试敏感词服务失败时的降级处理"""
        # 设置文件处理成功
        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": True,
            "warnings": [],
        }
        mock_file_processor.process_file.return_value = mock_file_content

        # 设置AI服务成功
        mock_ai_service.get_model_info.return_value = {"client_initialized": True}
        mock_ai_service.check_compliance.return_value = MagicMock(
            checkResultArr=mock_ai_response
        )

        # 设置敏感词服务失败
        mock_sensitive_service.check_health.return_value = False

        from app.models.schemas import ComplianceCheckResponse

        expected_response = ComplianceCheckResponse(
            sensitiveWordsArr=[],  # 敏感词服务不可用时为空
            checkResultArr=mock_ai_response,
        )
        mock_result_processor.process_with_fallback.return_value = expected_response

        # 执行测试
        result = compliance_service.check_compliance(sample_request, "test-004")

        # 验证结果
        assert result is not None
        assert len(result.sensitiveWordsArr) == 0  # 敏感词检测被跳过
        assert len(result.checkResultArr) == 2

        # 验证敏感词服务没有被调用
        mock_sensitive_service.detect_with_fallback.assert_not_called()

    def test_pipeline_stats_tracking(self, sample_request):
        """测试流水线统计信息跟踪"""
        pipeline = ComplianceCheckPipeline()

        # 获取初始统计
        initial_stats = pipeline.get_pipeline_stats()
        assert initial_stats["total_requests"] == 0
        assert initial_stats["successful_requests"] == 0

        # 重置统计
        pipeline.reset_stats()
        reset_stats = pipeline.get_pipeline_stats()
        assert reset_stats["total_requests"] == 0

    def test_service_status_info(self):
        """测试服务状态信息"""
        status = compliance_service.get_service_status()

        assert "service_info" in status
        assert "health_status" in status
        assert "pipeline_stats" in status
        assert "timestamp" in status

        # 验证服务信息
        service_info = status["service_info"]
        assert service_info["service_name"] == "bidding-document-compliance-checker"
        assert service_info["version"] == "1.0.0"
        assert ".docx" in service_info["supported_formats"]
        assert ".pdf" in service_info["supported_formats"]

    def test_processing_metrics(self):
        """测试处理指标获取"""
        metrics = compliance_service.get_processing_metrics()

        assert "pipeline_metrics" in metrics
        assert "service_health" in metrics
        assert "component_info" in metrics

        # 验证组件信息
        component_info = metrics["component_info"]
        assert "file_processor" in component_info
        assert "ai_model" in component_info
        assert "sensitive_word" in component_info
        assert "result_processor" in component_info

    @patch("app.services.compliance_service.file_processor")
    @patch("app.services.compliance_service.ai_model_service")
    @patch("app.services.compliance_service.sensitive_word_service")
    def test_prerequisites_validation(
        self, mock_sensitive_service, mock_ai_service, mock_file_processor
    ):
        """测试服务前置条件验证"""
        pipeline = ComplianceCheckPipeline()

        # 设置模拟返回值
        mock_ai_service.get_model_info.return_value = {"client_initialized": True}
        mock_sensitive_service.check_health.return_value = True

        # 执行验证
        health_status = pipeline.validate_prerequisites("test-005")

        # 验证结果
        assert health_status["file_processor"] is True
        assert health_status["ai_model"] is True
        assert health_status["sensitive_word"] is True
        assert health_status["result_processor"] is True


class TestComplianceCheckPipeline:
    """合规性检查流水线单独测试"""

    @pytest.fixture
    def pipeline(self):
        """创建流水线实例"""
        return ComplianceCheckPipeline()

    @pytest.fixture
    def sample_project_info(self):
        """创建示例项目信息"""
        return ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

    def test_pipeline_initialization(self, pipeline):
        """测试流水线初始化"""
        assert pipeline.fallback_manager is not None
        assert pipeline.pipeline_stats["total_requests"] == 0

    @patch("app.services.compliance_service.file_processor")
    def test_file_processing_stage(self, mock_file_processor, pipeline):
        """测试文件处理阶段"""
        # 创建测试文件信息
        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        # 设置模拟返回值
        mock_file_processor.validate_processing_capability.return_value = {
            "can_process": True,
            "warnings": [],
        }
        mock_file_processor.process_file.return_value = "测试内容"

        # 执行测试
        result = pipeline.process_file_stage(file_info, "test-006")

        # 验证结果
        assert result == "测试内容"
        mock_file_processor.process_file.assert_called_once_with(file_info, "test-006")

    @patch("app.services.compliance_service.ai_model_service")
    def test_ai_compliance_check_stage(
        self, mock_ai_service, pipeline, sample_project_info
    ):
        """测试AI合规性检查阶段"""
        # 设置模拟返回值
        mock_check_results = [
            CheckResultItem(
                quesType="合规性",
                quesDesc="测试内容",
                originalArr=["测试原文"],
                point="测试要点",
                advice="测试建议",
            )
        ]
        mock_ai_service.check_compliance.return_value = MagicMock(
            checkResultArr=mock_check_results
        )

        # 执行测试
        result = pipeline.ai_compliance_check_stage(
            "测试内容", sample_project_info, "test-007"
        )

        # 验证结果
        assert len(result) == 1
        assert result[0].quesType == "合规性"

    @patch("app.services.compliance_service.sensitive_word_service")
    def test_sensitive_word_check_stage(
        self, mock_sensitive_service, pipeline, sample_project_info
    ):
        """测试敏感词检测阶段"""
        # 设置模拟返回值
        mock_sensitive_words = [
            SensitiveWordItem(type="测试敏感", content="测试词", num=1)
        ]
        mock_sensitive_service.detect_with_fallback.return_value = mock_sensitive_words

        # 执行测试
        result = pipeline.sensitive_word_check_stage(
            "测试内容", sample_project_info, "test-008"
        )

        # 验证结果
        assert len(result) == 1
        assert result[0].type == "测试敏感"

    @patch("app.services.compliance_service.result_processor")
    def test_result_aggregation_stage(self, mock_result_processor, pipeline):
        """测试结果聚合阶段"""
        # 准备测试数据
        sensitive_words = [SensitiveWordItem(type="测试", content="测试", num=1)]
        check_results = [
            CheckResultItem(
                quesType="合规性",
                quesDesc="测试问题描述",
                originalArr=["测试原文"],
                point="测试控制要点",
                advice="测试处理建议",
            )
        ]

        # 设置模拟返回值
        from app.models.schemas import ComplianceCheckResponse

        expected_response = ComplianceCheckResponse(
            sensitiveWordsArr=sensitive_words, checkResultArr=check_results
        )
        mock_result_processor.process_with_fallback.return_value = expected_response

        # 执行测试
        result = pipeline.result_aggregation_stage(
            sensitive_words, check_results, "test-009"
        )

        # 验证结果
        assert result is not None
        assert len(result.sensitiveWordsArr) == 1
        assert len(result.checkResultArr) == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
