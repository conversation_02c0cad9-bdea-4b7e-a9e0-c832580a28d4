#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试脚本 - 运行所有测试
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from typing import List, Tuple


class TestRunner:
    """测试运行器"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tests_dir = self.project_root / "tests"
        self.results = []

    def get_test_files(self) -> List[Path]:
        """获取所有测试文件"""
        test_files = []

        # 按优先级排序测试文件
        priority_tests = [
            "test_config.py",
            "test_validators.py",
            "test_logger.py",
            "test_exceptions.py",
            "test_file_utils.py",
            "test_file_processor_v2.py",
            "test_ai_model_service.py",
            "test_sensitive_word_service.py",
            "test_result_processor.py",
            "test_middleware.py",
            "test_validation_middleware.py",
            "test_api_routes.py",
            "test_compliance_service_integration.py",
            "test_simple_compliance_check.py",
            "test_simple_api_only.py",
        ]

        # 添加优先级测试
        for test_name in priority_tests:
            test_file = self.tests_dir / test_name
            if test_file.exists():
                test_files.append(test_file)

        # 添加其他测试文件
        for test_file in self.tests_dir.glob("test_*.py"):
            if test_file.name not in priority_tests:
                # 跳过性能测试和需要特殊环境的测试
                if test_file.name not in [
                    "test_load_performance.py",
                    "test_performance_optimization.py",
                    "test_performance_reliability.py",
                    "test_with_real_file.py",
                ]:
                    test_files.append(test_file)

        return test_files

    def run_single_test(
        self, test_file: Path, timeout: int = 60
    ) -> Tuple[bool, str, float]:
        """运行单个测试文件"""

        start_time = time.time()

        try:
            # 切换到项目根目录
            os.chdir(self.project_root)

            # 运行测试
            result = subprocess.run(
                [sys.executable, str(test_file)],
                capture_output=True,
                text=True,
                timeout=timeout,
            )

            duration = time.time() - start_time

            if result.returncode == 0:
                return True, result.stdout, duration
            else:
                error_msg = f"STDOUT:\n{result.stdout}\n\nSTDERR:\n{result.stderr}"
                return False, error_msg, duration

        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            return False, f"测试超时 ({timeout}秒)", duration
        except Exception as e:
            duration = time.time() - start_time
            return False, f"测试异常: {str(e)}", duration

    def run_all_tests(self):
        """运行所有测试"""

        print("🚀 开始运行自动化测试套件")
        print("=" * 60)

        test_files = self.get_test_files()

        print(f"📋 发现 {len(test_files)} 个测试文件")
        print("=" * 60)

        total_start_time = time.time()

        for i, test_file in enumerate(test_files, 1):
            test_name = test_file.name

            print(f"\n🧪 [{i}/{len(test_files)}] 运行测试: {test_name}")
            print("-" * 40)

            success, output, duration = self.run_single_test(test_file)

            self.results.append(
                {
                    "name": test_name,
                    "success": success,
                    "duration": duration,
                    "output": output,
                }
            )

            if success:
                print(f"✅ 测试通过 ({duration:.2f}秒)")
                # 显示简化的输出
                if "通过" in output or "成功" in output:
                    lines = output.split("\n")
                    for line in lines[-10:]:  # 显示最后10行
                        if line.strip() and (
                            "✅" in line or "通过" in line or "成功" in line
                        ):
                            print(f"   {line.strip()}")
            else:
                print(f"❌ 测试失败 ({duration:.2f}秒)")
                # 显示错误信息的前几行
                lines = output.split("\n")
                for line in lines[:5]:
                    if line.strip():
                        print(f"   {line.strip()}")
                if len(lines) > 5:
                    print("   ...")

        total_duration = time.time() - total_start_time

        # 输出测试结果汇总
        self.print_summary(total_duration)

    def print_summary(self, total_duration: float):
        """打印测试结果汇总"""

        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)

        passed = sum(1 for r in self.results if r["success"])
        failed = len(self.results) - passed

        print(f"📊 总体统计:")
        print(f"   总测试数: {len(self.results)}")
        print(f"   通过: {passed}")
        print(f"   失败: {failed}")
        print(f"   成功率: {passed/len(self.results)*100:.1f}%")
        print(f"   总耗时: {total_duration:.2f}秒")

        if failed > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.results:
                if not result["success"]:
                    print(f"   • {result['name']} ({result['duration']:.2f}秒)")

        print(f"\n⏱️  测试耗时排行:")
        sorted_results = sorted(self.results, key=lambda x: x["duration"], reverse=True)
        for result in sorted_results[:5]:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['name']}: {result['duration']:.2f}秒")

        if failed == 0:
            print(f"\n🎉 所有测试通过!")
            print(f"✅ 测试套件运行成功")
        else:
            print(f"\n⚠️  有 {failed} 个测试失败")
            print(f"💡 建议检查失败的测试并修复问题")

    def generate_test_report(self):
        """生成测试报告"""

        report_file = self.project_root / "test_report.md"

        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# 自动化测试报告\n\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            passed = sum(1 for r in self.results if r["success"])
            failed = len(self.results) - passed

            f.write("## 测试概览\n\n")
            f.write(f"- 总测试数: {len(self.results)}\n")
            f.write(f"- 通过: {passed}\n")
            f.write(f"- 失败: {failed}\n")
            f.write(f"- 成功率: {passed/len(self.results)*100:.1f}%\n\n")

            f.write("## 详细结果\n\n")

            for result in self.results:
                status = "✅ 通过" if result["success"] else "❌ 失败"
                f.write(f"### {result['name']} - {status}\n\n")
                f.write(f"- 耗时: {result['duration']:.2f}秒\n")

                if not result["success"]:
                    f.write(f"- 错误信息:\n```\n{result['output'][:500]}...\n```\n")

                f.write("\n")

        print(f"📄 测试报告已生成: {report_file}")


def main():
    """主函数"""

    runner = TestRunner()

    try:
        runner.run_all_tests()
        runner.generate_test_report()

        # 检查是否所有测试都通过
        failed_count = sum(1 for r in runner.results if not r["success"])
        return failed_count == 0

    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
