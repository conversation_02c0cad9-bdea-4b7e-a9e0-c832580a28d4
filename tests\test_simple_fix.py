#!/usr/bin/env python3
"""
简单测试属性访问修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_response_format():
    """测试响应格式"""
    print("🔍 测试响应格式...")
    
    try:
        from app.models.schemas import ComplianceCheckResponse, ComplianceCheckData, SensitiveWordItem, CheckResultItem
        from app.models.enums import QuestionType
        
        # 创建测试数据
        sensitive_words = [
            SensitiveWordItem(type="错漏词汇", content="测试词", num=1)
        ]
        
        check_results = [
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE,
                quesDesc="测试问题描述",
                originalArr=["测试原文"],
                point="测试要点",
                advice="测试建议"
            )
        ]
        
        # 创建新格式的响应
        response = ComplianceCheckResponse(
            code=200,
            message="测试成功",
            data=ComplianceCheckData(
                sensitiveWordsArr=sensitive_words,
                checkResultArr=check_results
            )
        )
        
        # 测试正确的属性访问
        sensitive_count = len(response.data.sensitiveWordsArr)
        check_count = len(response.data.checkResultArr)
        
        print(f"✅ 正确访问方式:")
        print(f"   敏感词数量: {sensitive_count}")
        print(f"   检查结果数量: {check_count}")
        
        # 测试错误的属性访问（应该失败）
        try:
            len(response.sensitiveWordsArr)
            print("❌ 错误：旧的属性访问方式仍然有效！")
            return False
        except AttributeError:
            print("✅ 正确：旧的sensitiveWordsArr属性访问已被阻止")
        
        try:
            len(response.checkResultArr)
            print("❌ 错误：旧的属性访问方式仍然有效！")
            return False
        except AttributeError:
            print("✅ 正确：旧的checkResultArr属性访问已被阻止")
            
        return True
        
    except Exception as e:
        print(f"❌ 响应格式测试失败: {e}")
        return False

def test_ai_service_import():
    """测试AI服务导入"""
    print("\n🔍 测试AI服务导入...")
    
    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ComplianceCheckResponse, ComplianceCheckData
        
        # 测试空响应创建
        empty_response = ComplianceCheckResponse(
            code=200,
            message="AI检查完成，未发现问题",
            data=ComplianceCheckData(checkResultArr=[])
        )
        
        # 测试属性访问
        check_count = len(empty_response.data.checkResultArr)
        print(f"✅ AI服务响应格式正确，检查结果数量: {check_count}")
        
        return True
    except Exception as e:
        print(f"❌ AI服务导入测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试...")
    
    success = True
    success &= test_response_format()
    success &= test_ai_service_import()
    
    if success:
        print("\n🎉 所有测试通过！属性访问修复成功！")
        print("现在可以重新测试API了。")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
        sys.exit(1)
