#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接端点
"""

import requests
import json
import time


def test_direct_validate():
    """测试直接验证端点"""
    print("测试直接验证端点（绕过中间件）")
    print("=" * 50)

    data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 100,
        "url": "http://example.com/test.docx",
    }

    try:
        print("发送直接验证请求...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:8088/api/v1/debug/validate-file-direct",
            json=data,
            timeout=5,
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 直接验证成功")
            print(f"状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")

            capability = result.get("capability", {})
            print(f"可处理: {capability.get('can_process')}")
            print(f"估算时间: {capability.get('estimated_time')}秒")

            return True
        else:
            print(f"❌ 直接验证失败: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ 直接验证超时")
        return False
    except Exception as e:
        print(f"❌ 直接验证异常: {str(e)}")
        return False


def test_original_validate():
    """测试原始验证端点"""
    print("\n测试原始验证端点（通过中间件）")
    print("=" * 50)

    data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 100,
        "url": "http://example.com/test.docx",
    }

    try:
        print("发送原始验证请求...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:8088/api/v1/validate-file", json=data, timeout=5
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code in [200, 400]:
            print("✅ 原始验证有响应")
            return True
        else:
            print(f"❌ 原始验证失败: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ 原始验证超时")
        return False
    except Exception as e:
        print(f"❌ 原始验证异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("直接端点 vs 原始端点对比测试")
    print("=" * 60)

    # 测试直接端点
    direct_ok = test_direct_validate()

    # 测试原始端点
    original_ok = test_original_validate()

    print("\n" + "=" * 60)
    print("对比结果")
    print("=" * 60)

    if direct_ok and not original_ok:
        print("🔍 问题确定在中间件或API处理流程中")
        print("   直接调用正常，但通过API流程就超时")
        print("   建议检查：")
        print("   1. 请求验证中间件")
        print("   2. 日志中间件")
        print("   3. 限流中间件")
        print("   4. API错误处理装饰器")
    elif direct_ok and original_ok:
        print("✅ 两个端点都正常，问题可能已解决")
    elif not direct_ok and not original_ok:
        print("❌ 两个端点都有问题，问题在更深层")
    else:
        print("🤔 结果不一致，需要进一步调试")


if __name__ == "__main__":
    main()
