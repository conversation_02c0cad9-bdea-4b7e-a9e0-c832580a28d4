#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接验证端点（绕过中间件）
"""

import requests
import json
import time


def test_direct_validate():
    """测试直接验证端点"""
    print("测试直接验证端点（绕过中间件）")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        print("发送请求到 /debug/validate-file-direct...")
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/debug/validate-file-direct",
            json=file_info_data,
            timeout=10,
        )
        elapsed = time.time() - start_time

        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 直接验证端点正常工作")
            print(f"返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 直接验证端点失败")
            return False

    except requests.exceptions.Timeout:
        print("❌ 直接验证端点超时")
        return False
    except Exception as e:
        print(f"❌ 直接验证端点异常: {str(e)}")
        return False


if __name__ == "__main__":
    test_direct_validate()
