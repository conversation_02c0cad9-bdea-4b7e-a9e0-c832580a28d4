# -*- coding: utf-8 -*-
"""
招标信息处理集成测试
"""

import pytest
from unittest.mock import patch, MagicMock
from io import BytesIO

from app.services.bidding_info_processor import (
    BiddingInfoProcessor,
    BiddingInfoError,
    bidding_info_processor,
)
from app.services.file_processor_v2 import OptimizedFileProcessor
from app.services.async_file_processor import AsyncFileProcessor
from app.models.enums import FileExtension
from app.models.schemas import FileInfo


class TestBiddingInfoProcessor:
    """招标信息处理器测试"""

    def test_replacement_rules_configuration(self):
        """测试替换规则配置"""
        processor = BiddingInfoProcessor()

        expected_rules = {
            "招标单位名称": "安徽省一二三四科技有限公司",
            "招标单位联系人": "张三",
            "招标单位联系电话": "13333333333",
            "招标单位地址": "安徽省合肥市庐阳区长江路123号",
            "招标单位邮箱": "<EMAIL>",
        }

        assert processor.replacement_rules == expected_rules

    @patch("app.services.bidding_info_processor.OpenAI")
    def test_local_model_initialization(self, mock_openai):
        """测试本地大模型初始化"""
        processor = BiddingInfoProcessor()

        mock_openai.assert_called_with(
            api_key="dummy-key", base_url="http://172.18.10.23:8000/v1"
        )

    def test_manual_parse_response(self):
        """测试手动解析响应"""
        processor = BiddingInfoProcessor()

        response = """
        "招标单位名称": "测试公司",
        "招标单位联系人": "测试联系人",
        "招标单位联系电话": "12345678901"
        """

        result = processor._manual_parse_response(response)

        assert result["招标单位名称"] == "测试公司"
        assert result["招标单位联系人"] == "测试联系人"
        assert result["招标单位联系电话"] == "12345678901"

    def test_replace_bidding_info(self):
        """测试招标信息替换"""
        processor = BiddingInfoProcessor()

        content = """
        招标人名称：原始公司名称
        联系人：原始联系人
        联系电话：原始电话
        """

        bidding_info = {
            "招标单位名称": "原始公司名称",
            "招标单位联系人": "原始联系人",
            "招标单位联系电话": "原始电话",
            "招标单位地址": "",
            "招标单位邮箱": "",
        }

        result = processor.replace_bidding_info(content, bidding_info, "test-001")

        assert "安徽省一二三四科技有限公司" in result
        assert "张三" in result
        assert "13333333333" in result

    @patch("pdfplumber.open")
    def test_extract_pdf_first_10_pages(self, mock_pdf_open):
        """测试PDF前10页提取"""
        # 模拟PDF页面
        mock_page = MagicMock()
        mock_page.extract_text.return_value = "测试页面内容"

        mock_pdf = MagicMock()
        mock_pdf.pages = [mock_page] * 15  # 15页PDF
        mock_pdf.__enter__.return_value = mock_pdf

        mock_pdf_open.return_value = mock_pdf

        processor = BiddingInfoProcessor()
        file_content = b"fake pdf content"

        result = processor.extract_first_10_pages(
            file_content, FileExtension.PDF, "test-001"
        )

        # 应该只处理前10页
        assert result.count("=== 第") == 10
        assert "=== 第1页 ===" in result
        assert "=== 第10页 ===" in result
        assert "=== 第11页 ===" not in result


class TestFileProcessorIntegration:
    """文件处理器集成测试"""

    @patch("app.services.file_processor_v2.bidding_info_processor")
    @patch("app.services.file_processor_v2.OptimizedFileProcessor.download_file")
    @patch(
        "app.services.file_processor_v2.OptimizedFileProcessor.convert_with_markitdown"
    )
    def test_sync_processor_with_bidding_info(
        self, mock_convert, mock_download, mock_bidding_processor
    ):
        """测试同步处理器集成招标信息处理"""
        # 模拟文件信息
        file_info = FileInfo(
            filename="test.pdf",
            url="http://example.com/test.pdf",
            extension=FileExtension.PDF,
            mime_type=MimeType.PDF,
            size=1024,
        )

        # 模拟下载内容
        mock_download.return_value = b"fake file content"

        # 模拟招标信息分析
        mock_bidding_processor.extract_first_10_pages.return_value = "前10页内容"
        mock_bidding_processor.analyze_bidding_info.return_value = {
            "招标单位名称": "原始公司",
            "招标单位联系人": "原始联系人",
            "招标单位联系电话": "原始电话",
            "招标单位地址": "原始地址",
            "招标单位邮箱": "原始邮箱",
        }
        mock_bidding_processor.replace_bidding_info.return_value = (
            "替换后的markdown内容"
        )

        # 模拟markdown转换
        mock_convert.return_value = "原始markdown内容"

        processor = OptimizedFileProcessor()
        result = processor.process_file(file_info, "test-001")

        # 验证调用顺序
        mock_download.assert_called_once()
        mock_bidding_processor.extract_first_10_pages.assert_called_once()
        mock_bidding_processor.analyze_bidding_info.assert_called_once()
        mock_convert.assert_called_once()
        mock_bidding_processor.replace_bidding_info.assert_called_once()

        assert result == "替换后的markdown内容"

    @pytest.mark.asyncio
    @patch("app.services.async_file_processor.bidding_info_processor")
    async def test_async_processor_with_bidding_info(self, mock_bidding_processor):
        """测试异步处理器集成招标信息处理"""
        # 模拟招标信息分析
        mock_bidding_processor.extract_first_10_pages.return_value = "前10页内容"
        mock_bidding_processor.analyze_bidding_info.return_value = {
            "招标单位名称": "原始公司",
            "招标单位联系人": "原始联系人",
            "招标单位联系电话": "原始电话",
            "招标单位地址": "",
            "招标单位邮箱": "",
        }
        mock_bidding_processor.replace_bidding_info.return_value = "替换后的内容"

        # 由于异步处理器需要真实的网络请求，这里只测试方法存在性
        processor = AsyncFileProcessor()

        # 验证方法存在
        assert hasattr(processor, "process_file")
        assert callable(getattr(processor, "process_file"))


class TestBiddingInfoErrorHandling:
    """招标信息错误处理测试"""

    def test_bidding_info_error_creation(self):
        """测试招标信息错误创建"""
        original_error = ValueError("原始错误")
        error = BiddingInfoError(
            "处理失败", stage="测试阶段", original_error=original_error
        )

        assert str(error) == "处理失败"
        assert error.stage == "测试阶段"
        assert error.original_error == original_error
        assert error.error_type == "BIDDING_INFO_ERROR"

    @patch("app.services.bidding_info_processor.OpenAI")
    def test_model_call_failure_handling(self, mock_openai):
        """测试模型调用失败处理"""
        # 模拟OpenAI客户端初始化失败
        mock_openai.side_effect = Exception("连接失败")

        with pytest.raises(BiddingInfoError) as exc_info:
            BiddingInfoProcessor()

        assert "本地大模型客户端初始化失败" in str(exc_info.value)
        assert exc_info.value.stage == "客户端初始化"


class TestConfigurationValidation:
    """配置验证测试"""

    def test_replacement_rules_completeness(self):
        """测试替换规则完整性"""
        processor = BiddingInfoProcessor()

        required_fields = [
            "招标单位名称",
            "招标单位联系人",
            "招标单位联系电话",
            "招标单位地址",
            "招标单位邮箱",
        ]

        for field in required_fields:
            assert field in processor.replacement_rules
            assert processor.replacement_rules[field]  # 确保不为空

    def test_model_configuration(self):
        """测试模型配置"""
        processor = BiddingInfoProcessor()

        # 验证模型URL配置
        assert processor.client is not None
        # 注意：由于是模拟环境，这里主要验证初始化不报错


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
