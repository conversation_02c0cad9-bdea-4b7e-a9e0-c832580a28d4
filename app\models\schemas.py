# -*- coding: utf-8 -*-
"""
数据模型和Schema定义
"""

from typing import List, Optional
from pydantic import BaseModel, Field, validator, HttpUrl
from datetime import datetime

from .enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
    QuestionType,
    SensitiveWordType,
)


class FileInfo(BaseModel):
    """文件信息模型"""

    filename: str = Field(..., description="文件名")
    extension: FileExtension = Field(..., description="文件扩展名")
    mime_type: MimeType = Field(..., description="MIME类型")
    size: int = Field(..., gt=0, description="文件大小（字节）")
    url: HttpUrl = Field(..., description="文件下载URL")

    @validator("extension", "mime_type")
    def validate_file_format(cls, v, values):
        """验证文件格式一致性"""
        if "extension" in values and "mime_type" in values:
            extension = values.get("extension")
            mime_type = values.get("mime_type")

            # 检查扩展名和MIME类型是否匹配
            valid_combinations = {
                FileExtension.DOCX: MimeType.DOCX,
                FileExtension.PDF: MimeType.PDF,
            }

            if extension and mime_type:
                if valid_combinations.get(extension) != mime_type:
                    raise ValueError(
                        f"文件扩展名 {extension} 与MIME类型 {mime_type} 不匹配"
                    )

        return v


class ProjectInfo(BaseModel):
    """项目信息模型"""

    procurement_project_type: ProcurementProjectType = Field(
        ..., description="采购项目类型"
    )
    project_category: ProjectCategory = Field(..., description="项目类别")
    bidding_procurement_method: BiddingProcurementMethod = Field(
        ..., description="招标采购方式"
    )

    def is_government_procurement(self) -> bool:
        """判断是否为政府采购"""
        return self.project_category == ProjectCategory.GOVERNMENT_PROCUREMENT


class ComplianceCheckRequest(BaseModel):
    """合规性检查请求模型"""

    bidding_doc: FileInfo = Field(..., description="招标文件信息")
    procurement_project_type: ProcurementProjectType = Field(
        ..., description="采购项目类型"
    )
    project_category: ProjectCategory = Field(..., description="项目类别")
    bidding_procurement_method: BiddingProcurementMethod = Field(
        ..., description="招标采购方式"
    )

    @validator("bidding_doc")
    def validate_file_size(cls, v):
        """验证文件大小"""
        max_size = 300 * 1024 * 1024  # 50MB
        if v.size > max_size:
            raise ValueError(f"文件大小不能超过 {max_size / 1024 / 1024:.0f}MB")
        return v

    def get_project_info(self) -> ProjectInfo:
        """获取项目信息"""
        return ProjectInfo(
            procurement_project_type=self.procurement_project_type,
            project_category=self.project_category,
            bidding_procurement_method=self.bidding_procurement_method,
        )


class SimpleComplianceCheckRequest(BaseModel):
    """简化的合规性检查请求模型"""

    procurement_project_type: str = Field(..., description="采购项目类型")
    project_category: str = Field(..., description="项目类别")
    bidding_procurement_method: str = Field(..., description="招标采购方式")
    file_url: str = Field(..., description="文件下载URL")

    @validator("procurement_project_type")
    def validate_procurement_project_type(cls, v):
        """验证采购项目类型"""
        valid_types = ["工程类", "服务类", "货物类"]
        if v not in valid_types:
            raise ValueError(f"无效的采购项目类型: {v}，有效值: {valid_types}")
        return v

    @validator("project_category")
    def validate_project_category(cls, v):
        """验证项目类别"""
        valid_categories = ["依法招标", "非依法招标", "政府采购"]
        if v not in valid_categories:
            raise ValueError(f"无效的项目类别: {v}，有效值: {valid_categories}")
        return v

    @validator("bidding_procurement_method")
    def validate_bidding_procurement_method(cls, v):
        """验证招标采购方式"""
        valid_methods = [
            "公开招标",
            "单一来源",
            "竞争性磋商",
            "竞争性磋商邀请",
            "邀请招标",
            "竞争性谈判",
            "公开竞价",
            "邀请竞价",
            "询价",
            "其他",
            "比选",
        ]
        if v not in valid_methods:
            raise ValueError(f"无效的招标采购方式: {v}，有效值: {valid_methods}")
        return v

    @validator("file_url")
    def validate_file_url(cls, v):
        """验证文件URL"""
        if not v.startswith(("http://", "https://")):
            raise ValueError("文件URL必须以http://或https://开头")
        return v

    def get_project_info(self) -> ProjectInfo:
        """获取项目信息"""
        return ProjectInfo(
            procurement_project_type=ProcurementProjectType(
                self.procurement_project_type
            ),
            project_category=ProjectCategory(self.project_category),
            bidding_procurement_method=BiddingProcurementMethod(
                self.bidding_procurement_method
            ),
        )


class SensitiveWordItem(BaseModel):
    """敏感词项模型"""

    type: str = Field(..., description="敏感词类型")
    content: str = Field(..., description="敏感词内容")
    num: int = Field(..., ge=1, description="出现次数")


class CheckResultItem(BaseModel):
    """检查结果项模型"""

    quesType: QuestionType = Field(..., description="问题类型")
    quesDesc: str = Field(..., description="问题描述")
    originalArr: List[str] = Field(..., description="原文内容")
    point: str = Field(..., description="质量控制要点")
    advice: str = Field(..., description="处理建议")


class ComplianceCheckData(BaseModel):
    """合规性检查数据模型"""

    sensitiveWordsArr: List[SensitiveWordItem] = Field(
        default=[], description="敏感词列表"
    )
    checkResultArr: List[CheckResultItem] = Field(
        default=[], description="检查结果列表"
    )


class ComplianceCheckResponse(BaseModel):
    """合规性检查响应模型（标准化格式）"""

    code: int = Field(default=200, description="HTTP状态码")
    message: str = Field(default="请求成功", description="响应消息")
    data: ComplianceCheckData = Field(
        default_factory=ComplianceCheckData, description="响应数据"
    )


class ErrorResponse(BaseModel):
    """错误响应模型（标准化格式）"""

    code: int = Field(..., description="HTTP状态码")
    message: str = Field(..., description="错误消息")
    data: Optional[dict] = Field(default=None, description="错误详情数据")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本号")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")


# 内部使用的模型


class ProcessingContext(BaseModel):
    """处理上下文模型"""

    request_id: str = Field(..., description="请求ID")
    file_content: str = Field(..., description="文件内容")
    project_info: ProjectInfo = Field(..., description="项目信息")
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")


class ModelConfig(BaseModel):
    """模型配置"""

    api_key: str = Field(..., description="API密钥")
    model_name: str = Field(..., description="模型名称")
    base_url: str = Field(..., description="基础URL")
    top_p: float = Field(default=0.7, ge=0.0, le=1.0, description="Top-p参数")
    seed: int = Field(default=42, description="随机种子")
    temperature: float = Field(default=0.0, ge=0.0, le=2.0, description="温度参数")
    max_context_length: int = Field(default=65536, gt=0, description="最大上下文长度")
    max_output_tokens: int = Field(default=8192, gt=0, description="最大输出token数")


class SensitiveWordRequest(BaseModel):
    """敏感词检测请求模型"""

    content: str = Field(..., description="要检测的markdown内容")
    is_government_procurement: Optional[bool] = Field(
        None, description="是否是政府采购类"
    )


class SensitiveWordResult(BaseModel):
    """敏感词检测结果模型（API返回格式）"""

    序号: int = Field(..., description="序号")
    敏感词类型: str = Field(..., description="敏感词类型")
    敏感词内容: str = Field(..., description="敏感词内容")
    出现次数: int = Field(..., description="出现次数")


class SensitiveWordDetectionResponse(BaseModel):
    """敏感词检测API响应模型"""

    success: bool = Field(..., description="检测是否成功")
    message: str = Field(..., description="响应消息")
    total_words: int = Field(..., description="检测到的敏感词总数")
    markdown_table: str = Field(..., description="markdown格式的检测结果表格")
    results: List[SensitiveWordResult] = Field(..., description="详细检测结果")
