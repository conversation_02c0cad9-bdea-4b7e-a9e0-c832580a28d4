# -*- coding: utf-8 -*-
"""
敏感词检测服务测试
"""

import pytest
from unittest.mock import patch, MagicMock
import requests

from app.services.sensitive_word_service import (
    SensitiveWordService,
    SensitiveWordError,
    create_robust_session,
    sensitive_word_service,
)
from app.models.schemas import SensitiveWordItem, ProjectInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
)


class TestSensitiveWordError:
    """敏感词检测异常测试"""

    def test_sensitive_word_error_basic(self):
        """测试基础敏感词检测异常"""
        error = SensitiveWordError("检测失败")
        assert str(error) == "检测失败"
        assert error.error_type == "SENSITIVE_WORD_ERROR"

    def test_sensitive_word_error_with_details(self):
        """测试带详细信息的敏感词检测异常"""
        original_error = requests.exceptions.RequestException("网络错误")
        error = SensitiveWordError(
            "检测失败",
            api_url="http://example.com/detect",
            status_code=500,
            response_data='{"error": "server error"}',
            original_error=original_error,
        )

        error_str = str(error)
        assert "检测失败" in error_str
        assert "http://example.com/detect" in error_str
        assert "500" in error_str
        assert "网络错误" in error_str


class TestCreateRobustSession:
    """健壮会话创建测试"""

    def test_create_robust_session(self):
        """测试创建健壮会话"""
        session = create_robust_session()

        assert isinstance(session, requests.Session)
        assert session.adapters["http://"]
        assert session.adapters["https://"]


class TestSensitiveWordService:
    """敏感词检测服务测试"""

    @pytest.fixture
    def service(self):
        """敏感词检测服务实例"""
        return SensitiveWordService()

    @pytest.fixture
    def project_info_government(self):
        """政府采购项目信息"""
        return ProjectInfo(
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

    @pytest.fixture
    def project_info_non_government(self):
        """依法招标项目信息"""
        return ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.LEGAL_BIDDING,
            bidding_procurement_method=BiddingProcurementMethod.INVITATION_BIDDING,
        )

    def test_initialization(self, service):
        """测试初始化"""
        assert service.base_url
        assert service.detect_endpoint.endswith("/detect")
        assert service.health_endpoint.endswith("/health")
        assert service.stats_endpoint.endswith("/stats")
        assert service.reload_endpoint.endswith("/reload")
        assert service.session is not None

    @patch("app.services.sensitive_word_service.requests.Session.get")
    def test_check_health_success(self, mock_get, service):
        """测试健康检查成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "healthy"}
        mock_get.return_value = mock_response

        result = service.check_health("test-123")

        assert result is True
        mock_get.assert_called_once()

    @patch("app.services.sensitive_word_service.requests.Session.get")
    def test_check_health_unhealthy(self, mock_get, service):
        """测试健康检查不健康"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "unhealthy"}
        mock_get.return_value = mock_response

        result = service.check_health("test-123")

        assert result is False

    @patch("app.services.sensitive_word_service.requests.Session.get")
    def test_check_health_exception(self, mock_get, service):
        """测试健康检查异常"""
        mock_get.side_effect = requests.exceptions.RequestException("网络错误")

        result = service.check_health("test-123")

        assert result is False

    @patch("app.services.sensitive_word_service.requests.Session.get")
    def test_get_stats_success(self, mock_get, service):
        """测试获取统计信息成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "total_words": 1000,
            "categories": ["政治敏感", "商业敏感"],
        }
        mock_get.return_value = mock_response

        result = service.get_stats("test-123")

        assert result is not None
        assert "total_words" in result
        assert "categories" in result

    @patch("app.services.sensitive_word_service.requests.Session.get")
    def test_get_stats_exception(self, mock_get, service):
        """测试获取统计信息异常"""
        mock_get.side_effect = requests.exceptions.RequestException("网络错误")

        result = service.get_stats("test-123")

        assert result is None

    def test_determine_government_procurement_true(
        self, service, project_info_government
    ):
        """测试判断政府采购为真"""
        result = service.determine_government_procurement(project_info_government)
        assert result is True

    def test_determine_government_procurement_false(
        self, service, project_info_non_government
    ):
        """测试判断政府采购为假"""
        result = service.determine_government_procurement(project_info_non_government)
        assert result is False

    def test_convert_to_target_format_success(self, service):
        """测试转换为目标格式成功"""
        api_results = [
            {
                "序号": 1,
                "敏感词类型": "政治敏感",
                "敏感词内容": "测试敏感词1",
                "出现次数": 2,
            },
            {
                "序号": 2,
                "敏感词类型": "商业敏感",
                "敏感词内容": "测试敏感词2",
                "出现次数": 1,
            },
        ]

        result = service._convert_to_target_format(api_results)

        assert len(result) == 2

        assert result[0].type == "政治敏感"
        assert result[0].content == "测试敏感词1"
        assert result[0].num == 2

        assert result[1].type == "商业敏感"
        assert result[1].content == "测试敏感词2"
        assert result[1].num == 1

    def test_convert_to_target_format_invalid_data(self, service):
        """测试转换无效数据"""
        api_results = [
            {
                "序号": 1,
                "敏感词类型": "政治敏感",
                "敏感词内容": "",  # 空内容
                "出现次数": 2,
            },
            {
                "序号": 2,
                "敏感词类型": "商业敏感",
                "敏感词内容": "测试敏感词",
                "出现次数": 0,  # 出现次数为0
            },
            {
                "序号": 3,
                "敏感词类型": "技术敏感",
                "敏感词内容": "有效敏感词",
                "出现次数": 1,
            },
        ]

        result = service._convert_to_target_format(api_results)

        # 只有最后一个有效
        assert len(result) == 1
        assert result[0].content == "有效敏感词"

    def test_convert_to_target_format_exception(self, service, caplog):
        """测试转换异常数据"""
        api_results = [
            {
                "序号": 1,
                "敏感词类型": "政治敏感",
                # 缺少必要字段
            },
            {
                "序号": 2,
                "敏感词类型": "商业敏感",
                "敏感词内容": "正常敏感词",
                "出现次数": 1,
            },
        ]

        result = service._convert_to_target_format(api_results)

        # 只有第二个成功转换
        assert len(result) == 1
        assert result[0].content == "正常敏感词"

        # 检查警告日志
        assert "转换敏感词结果失败" in caplog.text

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_detect_sensitive_words_success(
        self, mock_post, service, project_info_government
    ):
        """测试敏感词检测成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "success": True,
            "message": "检测完成",
            "total_words": 1,
            "results": [
                {
                    "序号": 1,
                    "敏感词类型": "政治敏感",
                    "敏感词内容": "测试敏感词",
                    "出现次数": 1,
                }
            ],
        }
        mock_post.return_value = mock_response

        content = "这是包含测试敏感词的内容"
        result = service.detect_sensitive_words(
            content, project_info_government, "test-123"
        )

        assert len(result) == 1
        assert result[0].type == "政治敏感"
        assert result[0].content == "测试敏感词"
        assert result[0].num == 1

        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        request_data = call_args[1]["json"]
        assert request_data["content"] == content
        assert request_data["is_government_procurement"] is True

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_detect_sensitive_words_non_government(
        self, mock_post, service, project_info_non_government
    ):
        """测试依法招标敏感词检测"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "success": True,
            "message": "检测完成",
            "total_words": 0,
            "results": [],
        }
        mock_post.return_value = mock_response

        content = "正常内容"
        result = service.detect_sensitive_words(
            content, project_info_non_government, "test-123"
        )

        assert len(result) == 0

        # 验证请求参数
        call_args = mock_post.call_args
        request_data = call_args[1]["json"]
        assert request_data["is_government_procurement"] is False

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_detect_sensitive_words_api_failure(
        self, mock_post, service, project_info_government
    ):
        """测试API返回失败"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"success": False, "message": "API内部错误"}
        mock_response.status_code = 500
        mock_post.return_value = mock_response

        content = "测试内容"

        with pytest.raises(SensitiveWordError) as exc_info:
            service.detect_sensitive_words(content, project_info_government)

        error = exc_info.value
        assert "API内部错误" in str(error)
        assert error.status_code == 500

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_detect_sensitive_words_invalid_response(
        self, mock_post, service, project_info_government
    ):
        """测试无效响应格式"""
        mock_response = MagicMock()
        mock_response.json.return_value = "invalid response"  # 不是dict
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        content = "测试内容"

        with pytest.raises(SensitiveWordError) as exc_info:
            service.detect_sensitive_words(content, project_info_government)

        error = exc_info.value
        assert "响应格式错误" in str(error)

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_detect_sensitive_words_request_exception(
        self, mock_post, service, project_info_government
    ):
        """测试请求异常"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.text = "Not Found"

        request_error = requests.exceptions.RequestException("网络错误")
        request_error.response = mock_response
        mock_post.side_effect = request_error

        content = "测试内容"

        with pytest.raises(SensitiveWordError) as exc_info:
            service.detect_sensitive_words(content, project_info_government)

        error = exc_info.value
        assert "请求失败" in str(error)
        assert error.status_code == 404

    @patch.object(SensitiveWordService, "check_health")
    @patch.object(SensitiveWordService, "detect_sensitive_words")
    def test_detect_with_fallback_success(
        self, mock_detect, mock_health, service, project_info_government
    ):
        """测试带降级机制的检测成功"""
        mock_health.return_value = True
        mock_detect.return_value = [
            SensitiveWordItem(type="政治敏感", content="测试词", num=1)
        ]

        content = "测试内容"
        result = service.detect_with_fallback(
            content, project_info_government, "test-123"
        )

        assert len(result) == 1
        assert result[0].content == "测试词"

    @patch.object(SensitiveWordService, "check_health")
    def test_detect_with_fallback_unhealthy(
        self, mock_health, service, project_info_government
    ):
        """测试服务不健康时的降级"""
        mock_health.return_value = False

        content = "测试内容"
        result = service.detect_with_fallback(
            content, project_info_government, "test-123"
        )

        assert len(result) == 0

    @patch.object(SensitiveWordService, "check_health")
    @patch.object(SensitiveWordService, "detect_sensitive_words")
    def test_detect_with_fallback_exception(
        self, mock_detect, mock_health, service, project_info_government
    ):
        """测试检测异常时的降级"""
        mock_health.return_value = True
        mock_detect.side_effect = SensitiveWordError("检测失败")

        content = "测试内容"
        result = service.detect_with_fallback(
            content, project_info_government, "test-123"
        )

        assert len(result) == 0

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_reload_sensitive_words_success(self, mock_post, service):
        """测试重载敏感词库成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"success": True, "message": "重载成功"}
        mock_post.return_value = mock_response

        result = service.reload_sensitive_words("test-123")

        assert result is True
        mock_post.assert_called_once()

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_reload_sensitive_words_failure(self, mock_post, service):
        """测试重载敏感词库失败"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"success": False, "message": "重载失败"}
        mock_post.return_value = mock_response

        result = service.reload_sensitive_words("test-123")

        assert result is False

    @patch("app.services.sensitive_word_service.requests.Session.post")
    def test_reload_sensitive_words_exception(self, mock_post, service):
        """测试重载敏感词库异常"""
        mock_post.side_effect = requests.exceptions.RequestException("网络错误")

        result = service.reload_sensitive_words("test-123")

        assert result is False

    def test_get_service_info(self, service):
        """测试获取服务信息"""
        info = service.get_service_info()

        assert "base_url" in info
        assert "detect_endpoint" in info
        assert "health_endpoint" in info
        assert "stats_endpoint" in info
        assert "reload_endpoint" in info
        assert "timeout" in info
        assert "max_retries" in info

        assert info["detect_endpoint"].endswith("/detect")
        assert info["health_endpoint"].endswith("/health")


class TestGlobalService:
    """全局服务实例测试"""

    def test_global_service_exists(self):
        """测试全局服务实例存在"""
        assert sensitive_word_service is not None
        assert isinstance(sensitive_word_service, SensitiveWordService)
