#!/usr/bin/env python3
"""
测试生产环境复杂JSON修复
"""

import json
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_production_json():
    """测试生产环境复杂JSON修复"""

    # 模拟生产环境中的复杂JSON响应
    complex_response = """{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "招标文件目录页码与后文实际章节页码不对应，例如"第二章	投标人须知前附表	4"实际页码为8，存在排版错误。",
      "originalArr": [
        "第二章	投标人须知前附表	4",
        "第三章	投标人须知	22",
        "第四章	评标办法	35",
        "第五章	合同条款及格式	48"
      ],
      "point": "文档格式规范性检查，确保目录页码与实际页码一致。",
      "advice": "重新核对并更新招标文件目录中的页码，确保与实际章节页码一致。"
    },
    {
      "quesType": "可操作性",
      "quesDesc": "投标文件格式要求中提到"投标文件应采用A4纸张"，但未明确说明是否允许双面打印。",
      "originalArr": [
        "投标文件应采用A4纸张"
      ],
      "point": "投标文件格式要求的完整性和明确性。",
      "advice": "在投标文件格式要求中明确说明是否允许双面打印，以及装订方式等具体要求。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "评标方法中"技术标评分"权重为60%，"商务标评分"权重为40%，但在评分细则中技术标细项权重总和为65%，存在不一致。",
      "originalArr": [
        "技术标评分权重为60%",
        "商务标评分权重为40%",
        "技术标细项权重总和为65%"
      ],
      "point": "评标方法的一致性和准确性检查。",
      "advice": "重新核对评标方法中的权重分配，确保总权重为100%且各细项权重与总权重一致。"
    }
  ]
}"""

    print("🔍 测试生产环境复杂JSON修复...")
    print(f"复杂响应长度: {len(complex_response)} 字符")

    # 统计问题字符
    issues = []
    left_quote_count = complex_response.count('"')
    right_quote_count = complex_response.count('"')
    if left_quote_count > 0 or right_quote_count > 0:
        issues.append(f"中文引号: {left_quote_count + right_quote_count}个")
    if "\t" in complex_response:
        issues.append(f"制表符: {complex_response.count(chr(9))}个")
    if "\n" in complex_response:
        issues.append(f"换行符: {complex_response.count(chr(10))}个")

    print(f"包含问题: {', '.join(issues) if issues else '无'}")

    try:
        from app.services.ai_model_service import ai_model_service

        # 测试JSON清理
        print("\n1. 测试JSON清理...")
        cleaned = ai_model_service.clean_json_data(complex_response)
        print(f"清理后长度: {len(cleaned)} 字符")

        # 测试JSON解析
        print("\n2. 测试JSON解析...")
        parsed = json.loads(cleaned)
        print(f"解析成功，结果数量: {len(parsed['checkResultArr'])}")

        # 验证每个结果的完整性
        print("\n3. 验证数据完整性...")
        all_valid = True
        for i, item in enumerate(parsed["checkResultArr"]):
            print(f"  结果 {i+1}:")
            print(f"    quesType: '{item.get('quesType', '')}'")
            print(f"    quesDesc: '{item.get('quesDesc', '')[:80]}...'")
            print(f"    point: '{item.get('point', '')[:60]}...'")
            print(f"    advice: '{item.get('advice', '')[:60]}...'")
            print(f"    originalArr: {len(item.get('originalArr', []))}个元素")

            # 检查空字段
            empty_fields = []
            for field in ["quesType", "quesDesc", "point", "advice"]:
                if not item.get(field, "").strip():
                    empty_fields.append(field)

            if empty_fields:
                print(f"    ❌ 空字段: {empty_fields}")
                all_valid = False
            else:
                print(f"    ✅ 所有字段完整")

        if all_valid:
            print("\n✅ 所有数据完整性验证通过")
        else:
            print("\n❌ 发现数据完整性问题")
            return False

        # 测试CheckResultItem创建
        print("\n4. 测试CheckResultItem创建...")
        from app.models.schemas import CheckResultItem, QuestionType

        check_results = []
        for i, item in enumerate(parsed["checkResultArr"]):
            ques_type = item.get("quesType", "规范性")

            # 处理组合类型
            if "/" in ques_type:
                ques_type = ques_type.split("/")[0].strip()

            # 验证问题类型
            valid_types = [e.value for e in QuestionType]
            if ques_type not in valid_types:
                ques_type = QuestionType.STANDARDIZATION.value

            check_result = CheckResultItem(
                quesType=ques_type,
                quesDesc=item.get("quesDesc", ""),
                originalArr=item.get("originalArr", []),
                point=item.get("point", ""),
                advice=item.get("advice", ""),
            )

            check_results.append(check_result)
            print(f"  ✅ CheckResultItem {i+1} 创建成功: {check_result.quesType}")

        print(
            f"\n🎉 生产环境复杂JSON修复测试成功！处理了 {len(check_results)} 个检查结果"
        )
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_production_json()
    if success:
        print("\n✅ 生产环境复杂JSON修复功能正常工作")
    else:
        print("\n❌ 生产环境复杂JSON修复功能仍有问题")
    sys.exit(0 if success else 1)
