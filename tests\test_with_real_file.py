#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实文件测试简化API
"""

import os
import sys
import time
import requests
import json
import threading
from create_test_docx import create_test_docx
from simple_file_server import start_server_in_background


def test_with_real_docx_file():
    """使用真实的docx文件测试"""

    print("=" * 60)
    print("使用真实docx文件测试简化API")
    print("=" * 60)

    try:
        # 1. 创建测试docx文件
        print("📝 创建测试docx文件...")
        docx_path = create_test_docx()

        if not os.path.exists(docx_path):
            print("❌ 测试文件创建失败")
            return False

        # 2. 启动文件服务器
        print("🚀 启动测试文件服务器...")
        server_thread = start_server_in_background(port=9999)

        # 3. 构建文件URL
        filename = os.path.basename(docx_path)
        file_url = f"http://localhost:9999/{filename}"

        print(f"📁 测试文件: {docx_path}")
        print(f"🌐 文件URL: {file_url}")

        # 4. 验证文件可以下载
        print("🔍 验证文件可下载性...")
        try:
            response = requests.head(file_url, timeout=10)
            if response.status_code != 200:
                print(f"❌ 文件无法访问，状态码: {response.status_code}")
                return False
            print(
                f"✅ 文件可访问，大小: {response.headers.get('content-length', 'unknown')} 字节"
            )
        except Exception as e:
            print(f"❌ 文件访问测试失败: {str(e)}")
            return False

        # 5. 测试简化API
        print("🧪 测试简化合规性检查API...")

        test_data = {
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
            "file_url": file_url,
        }

        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

        api_url = "http://localhost:8088/api/v1/check-compliance-simple"

        response = requests.post(api_url, json=test_data, timeout=120)  # 增加超时时间

        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 简化API测试成功!")
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")

            # 显示部分结果
            if result.get("sensitiveWordsArr"):
                print("\n敏感词示例:")
                for i, item in enumerate(result["sensitiveWordsArr"][:3]):
                    print(
                        f"  {i+1}. {item.get('type', '')}: {item.get('content', '')} (出现{item.get('num', 0)}次)"
                    )

            if result.get("checkResultArr"):
                print("\n检查结果示例:")
                for i, item in enumerate(result["checkResultArr"][:2]):
                    print(
                        f"  {i+1}. {item.get('quesType', '')}: {item.get('quesDesc', '')}"
                    )

            return True
        else:
            print(f"❌ 简化API测试失败!")
            print(f"错误响应: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保主服务正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


def test_file_info_inference():
    """测试文件信息推断功能"""

    print("\n" + "=" * 60)
    print("测试文件信息推断功能")
    print("=" * 60)

    try:
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        from app.utils.file_info_utils import infer_file_info_from_url

        test_urls = [
            "http://example.com/招标文件.docx",
            "http://example.com/project.pdf",
            "http://localhost:9999/test_bidding_document.docx",
        ]

        for url in test_urls:
            print(f"\n🔍 测试URL: {url}")
            try:
                file_info = infer_file_info_from_url(url)

                print(f"  文件名: {file_info.filename}")
                print(f"  扩展名: {file_info.extension}")
                print(f"  MIME类型: {file_info.mime_type}")
                print(f"  文件大小: {file_info.size} 字节")
                print(f"  ✅ 推断成功")

            except Exception as e:
                print(f"  ❌ 推断失败: {str(e)}")

        return True

    except Exception as e:
        print(f"❌ 文件信息推断测试异常: {str(e)}")
        return False


def main():
    """主测试函数"""

    print("🚀 开始完整的简化API测试")

    tests = [
        ("文件信息推断", test_file_info_inference),
        ("真实文件API测试", test_with_real_docx_file),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n总计: {len(results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    print(f"成功率: {passed/len(results)*100:.1f}%")

    if failed == 0:
        print("\n🎉 所有测试通过!")
        print("✅ 简化API功能正常工作")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")

    return failed == 0


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
