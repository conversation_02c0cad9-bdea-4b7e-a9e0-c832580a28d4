#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除装饰器后的端点
"""

import requests
import json
import time


def test_validate_file_without_decorator():
    """测试移除装饰器后的文件验证端点"""
    print("测试移除装饰器后的文件验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    print("发送文件验证请求...")

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file",
            json=file_info_data,
            timeout=10,  # 短超时测试
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 200:
            print("✅ 文件验证成功")
            return True
        else:
            print("❌ 文件验证失败，但至少有响应")
            return True  # 有响应就说明没有超时问题

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 文件验证仍然超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 文件验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_direct_component_call():
    """直接测试组件调用"""
    print("\n直接测试组件调用")
    print("=" * 50)

    try:
        import sys

        sys.path.append(".")

        from app.models.schemas import FileInfo
        from app.models.enums import FileExtension, MimeType
        from app.services.file_processor_v2 import optimized_file_processor

        print("创建FileInfo对象...")
        start_time = time.time()

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        elapsed = time.time() - start_time
        print(f"FileInfo创建耗时: {elapsed:.3f}秒")

        print("调用validate_processing_capability...")
        start_time = time.time()

        capability = optimized_file_processor.validate_processing_capability(file_info)

        elapsed = time.time() - start_time
        print(f"处理能力验证耗时: {elapsed:.3f}秒")
        print(f"结果: {json.dumps(capability, indent=2, ensure_ascii=False)}")

        return True

    except Exception as e:
        print(f"❌ 直接组件调用失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def test_pydantic_model_creation():
    """测试Pydantic模型创建"""
    print("\n测试Pydantic模型创建")
    print("=" * 50)

    try:
        import sys

        sys.path.append(".")

        from app.models.schemas import FileInfo
        from app.models.enums import FileExtension, MimeType

        # 测试数据
        file_data = {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://example.com/test.docx",
        }

        print("创建FileInfo对象...")
        start_time = time.time()

        file_info = FileInfo(**file_data)

        elapsed = time.time() - start_time
        print(f"Pydantic模型创建耗时: {elapsed:.3f}秒")
        print(f"对象: {file_info}")

        return True

    except Exception as e:
        print(f"❌ Pydantic模型创建失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("测试移除装饰器后的功能")
    print("=" * 60)

    print("请确保服务已重启")
    print("=" * 60)

    tests = [
        ("Pydantic模型创建", test_pydantic_model_creation),
        ("直接组件调用", test_direct_component_call),
        ("文件验证端点", test_validate_file_without_decorator),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行: {test_name}")
        print("=" * 60)

        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
            results[test_name] = False

    # 分析结果
    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if results.get("Pydantic模型创建", False):
        print("✅ Pydantic模型创建正常")
    else:
        print("❌ Pydantic模型创建有问题")

    if results.get("直接组件调用", False):
        print("✅ 文件处理器组件正常")
    else:
        print("❌ 文件处理器组件有问题")

    if results.get("文件验证端点", False):
        print("✅ API端点响应正常")
    else:
        print("❌ API端点仍然超时")
        print("🔍 问题可能在于:")
        print("   1. FastAPI的异步处理机制")
        print("   2. 中间件的处理逻辑")
        print("   3. Request对象的处理")
        print("   4. 某个同步调用阻塞了事件循环")


if __name__ == "__main__":
    main()
