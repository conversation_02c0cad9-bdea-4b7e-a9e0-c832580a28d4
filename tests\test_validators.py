# -*- coding: utf-8 -*-
"""
参数验证器测试
"""

import pytest
from fastapi import HTTPException

from app.core.validators import ParameterValidator, get_enum_values
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


class TestParameterValidator:
    """参数验证器测试类"""

    def test_validate_procurement_project_type_valid(self):
        """测试有效的采购项目类型"""
        result = ParameterValidator.validate_procurement_project_type("服务类")
        assert result == ProcurementProjectType.SERVICE

        result = ParameterValidator.validate_procurement_project_type("货物类")
        assert result == ProcurementProjectType.GOODS

        result = ParameterValidator.validate_procurement_project_type("工程类")
        assert result == ProcurementProjectType.ENGINEERING

    def test_validate_procurement_project_type_invalid(self):
        """测试无效的采购项目类型"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_procurement_project_type("无效类型")

        assert exc_info.value.status_code == 400
        assert "无效的采购项目类型" in str(exc_info.value.detail)

    def test_validate_project_category_valid(self):
        """测试有效的项目类别"""
        result = ParameterValidator.validate_project_category("政府采购")
        assert result == ProjectCategory.GOVERNMENT_PROCUREMENT

        result = ParameterValidator.validate_project_category("依法招标")
        assert result == ProjectCategory.LEGAL_BIDDING

        result = ParameterValidator.validate_project_category("非依法招标")
        assert result == ProjectCategory.NON_LEGAL_BIDDING

    def test_validate_project_category_invalid(self):
        """测试无效的项目类别"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_project_category("无效类别")

        assert exc_info.value.status_code == 400
        assert "无效的项目类别" in str(exc_info.value.detail)

    def test_validate_bidding_procurement_method_valid(self):
        """测试有效的招标采购方式"""
        result = ParameterValidator.validate_bidding_procurement_method("公开招标")
        assert result == BiddingProcurementMethod.PUBLIC_BIDDING

        result = ParameterValidator.validate_bidding_procurement_method("邀请招标")
        assert result == BiddingProcurementMethod.INVITATION_BIDDING

    def test_validate_bidding_procurement_method_invalid(self):
        """测试无效的招标采购方式"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_bidding_procurement_method("无效方式")

        assert exc_info.value.status_code == 400
        assert "无效的招标采购方式" in str(exc_info.value.detail)

    def test_validate_file_extension_valid(self):
        """测试有效的文件扩展名"""
        result = ParameterValidator.validate_file_extension(".docx")
        assert result == FileExtension.DOCX

        result = ParameterValidator.validate_file_extension(".pdf")
        assert result == FileExtension.PDF

    def test_validate_file_extension_invalid(self):
        """测试无效的文件扩展名"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_file_extension(".txt")

        assert exc_info.value.status_code == 400
        assert "不支持的文件格式" in str(exc_info.value.detail)

    def test_validate_mime_type_valid(self):
        """测试有效的MIME类型"""
        result = ParameterValidator.validate_mime_type(
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        assert result == MimeType.DOCX

        result = ParameterValidator.validate_mime_type("application/pdf")
        assert result == MimeType.PDF

    def test_validate_mime_type_invalid(self):
        """测试无效的MIME类型"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_mime_type("text/plain")

        assert exc_info.value.status_code == 400
        assert "不支持的MIME类型" in str(exc_info.value.detail)

    def test_validate_file_format_consistency_valid(self):
        """测试文件格式一致性验证 - 有效"""
        result = ParameterValidator.validate_file_format_consistency(
            ".docx",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
        assert result is True

        result = ParameterValidator.validate_file_format_consistency(
            ".pdf", "application/pdf"
        )
        assert result is True

    def test_validate_file_format_consistency_invalid(self):
        """测试文件格式一致性验证 - 无效"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_file_format_consistency(
                ".docx", "application/pdf"
            )

        assert exc_info.value.status_code == 400
        assert "不匹配" in str(exc_info.value.detail)

    def test_validate_file_size_valid(self):
        """测试文件大小验证 - 有效"""
        result = ParameterValidator.validate_file_size(1024)  # 1KB
        assert result is True

        result = ParameterValidator.validate_file_size(10 * 1024 * 1024)  # 10MB
        assert result is True

    def test_validate_file_size_invalid_zero(self):
        """测试文件大小验证 - 大小为0"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_file_size(0)

        assert exc_info.value.status_code == 400
        assert "文件大小必须大于0" in str(exc_info.value.detail)

    def test_validate_file_size_invalid_too_large(self):
        """测试文件大小验证 - 文件过大"""
        with pytest.raises(HTTPException) as exc_info:
            ParameterValidator.validate_file_size(100 * 1024 * 1024)  # 100MB

        assert exc_info.value.status_code == 400
        assert "不能超过" in str(exc_info.value.detail)


def test_get_enum_values():
    """测试获取枚举值"""
    result = get_enum_values()

    assert "procurement_project_types" in result
    assert "project_categories" in result
    assert "bidding_procurement_methods" in result
    assert "file_extensions" in result
    assert "mime_types" in result

    assert "服务类" in result["procurement_project_types"]
    assert "政府采购" in result["project_categories"]
    assert "公开招标" in result["bidding_procurement_methods"]
    assert ".docx" in result["file_extensions"]
    assert "application/pdf" in result["mime_types"]
