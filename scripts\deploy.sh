#!/bin/bash
# 招标文件合规性检查助手 - 部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="compliance-checker"
DOCKER_IMAGE_NAME="compliance-checker"
DOCKER_CONTAINER_NAME="compliance-checker"
BACKUP_DIR="./backups"
DEPLOY_ENV=${1:-production}

# 显示帮助信息
show_help() {
    echo "招标文件合规性检查助手 - 部署脚本"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  production    生产环境部署（默认）"
    echo "  development   开发环境部署"
    echo "  staging       测试环境部署"
    echo ""
    echo "选项:"
    echo "  --help        显示此帮助信息"
    echo "  --backup      部署前创建备份"
    echo "  --no-build    跳过镜像构建"
    echo "  --no-test     跳过测试"
    echo "  --force       强制部署（跳过确认）"
    echo ""
    echo "示例:"
    echo "  $0 production --backup"
    echo "  $0 development --no-test"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f ".env" ]; then
        log_error ".env配置文件不存在，请复制.env.example并配置"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建备份
create_backup() {
    log_info "创建部署备份..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="${BACKUP_DIR}/${TIMESTAMP}"
    
    mkdir -p "$BACKUP_PATH"
    
    # 备份配置文件
    cp .env "$BACKUP_PATH/"
    
    # 备份日志文件（如果存在）
    if [ -d "logs" ]; then
        cp -r logs "$BACKUP_PATH/"
    fi
    
    # 备份数据库（如果存在）
    if [ -d "data" ]; then
        cp -r data "$BACKUP_PATH/"
    fi
    
    # 导出当前运行的容器（如果存在）
    if docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
        log_info "导出当前容器镜像..."
        docker commit "$DOCKER_CONTAINER_NAME" "${DOCKER_IMAGE_NAME}:backup-${TIMESTAMP}"
    fi
    
    log_success "备份创建完成: $BACKUP_PATH"
}

# 运行测试
run_tests() {
    log_info "运行测试套件..."
    
    # 检查是否有测试脚本
    if [ -f "scripts/run_tests.py" ]; then
        python scripts/run_tests.py --skip-slow
        if [ $? -ne 0 ]; then
            log_error "测试失败，部署中止"
            exit 1
        fi
    else
        log_warning "未找到测试脚本，跳过测试"
    fi
    
    log_success "测试通过"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    # 构建镜像
    docker build -t "${DOCKER_IMAGE_NAME}:latest" .
    
    if [ $? -ne 0 ]; then
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 标记版本
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    docker tag "${DOCKER_IMAGE_NAME}:latest" "${DOCKER_IMAGE_NAME}:${TIMESTAMP}"
    
    log_success "镜像构建完成"
}

# 停止现有服务
stop_services() {
    log_info "停止现有服务..."
    
    # 使用docker-compose停止服务
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
    else
        # 手动停止容器
        if docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
            docker stop "$DOCKER_CONTAINER_NAME"
            docker rm "$DOCKER_CONTAINER_NAME"
        fi
    fi
    
    log_success "服务已停止"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 根据环境选择配置文件
    COMPOSE_FILE="docker-compose.yml"
    if [ "$DEPLOY_ENV" = "development" ]; then
        COMPOSE_FILE="docker-compose.dev.yml"
    elif [ "$DEPLOY_ENV" = "staging" ]; then
        COMPOSE_FILE="docker-compose.staging.yml"
    fi
    
    # 启动服务
    if [ -f "$COMPOSE_FILE" ]; then
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        # 使用默认配置文件
        docker-compose up -d
    fi
    
    if [ $? -ne 0 ]; then
        log_error "服务启动失败"
        exit 1
    fi
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查容器状态
    if ! docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
        log_error "容器未运行"
        return 1
    fi
    
    # 检查健康端点
    MAX_ATTEMPTS=30
    ATTEMPT=1
    
    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        log_info "健康检查尝试 $ATTEMPT/$MAX_ATTEMPTS..."
        
        if curl -f -s http://localhost:8088/health > /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        sleep 5
        ATTEMPT=$((ATTEMPT + 1))
    done
    
    log_error "健康检查失败"
    return 1
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    # 检查服务状态
    docker-compose ps
    
    # 检查日志
    log_info "最近的服务日志:"
    docker-compose logs --tail=20 compliance-checker
    
    # 检查资源使用
    log_info "容器资源使用情况:"
    docker stats --no-stream "$DOCKER_CONTAINER_NAME"
    
    log_success "部署后验证完成"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."
    
    # 删除无标签的镜像
    docker image prune -f
    
    # 保留最近5个版本的镜像
    OLD_IMAGES=$(docker images "${DOCKER_IMAGE_NAME}" --format "table {{.Repository}}:{{.Tag}}" | grep -v "latest" | tail -n +6)
    
    if [ ! -z "$OLD_IMAGES" ]; then
        echo "$OLD_IMAGES" | xargs docker rmi
        log_success "旧镜像清理完成"
    else
        log_info "没有需要清理的旧镜像"
    fi
}

# 发送部署通知（可选）
send_notification() {
    log_info "发送部署通知..."
    
    # 这里可以添加发送邮件、Slack通知等逻辑
    # 示例：发送到Webhook
    # curl -X POST -H 'Content-type: application/json' \
    #   --data '{"text":"Compliance Checker deployed successfully"}' \
    #   YOUR_WEBHOOK_URL
    
    log_info "部署通知已发送"
}

# 主部署流程
main() {
    log_info "开始部署 $PROJECT_NAME ($DEPLOY_ENV 环境)"
    
    # 解析命令行参数
    BACKUP=false
    NO_BUILD=false
    NO_TEST=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help)
                show_help
                exit 0
                ;;
            --backup)
                BACKUP=true
                shift
                ;;
            --no-build)
                NO_BUILD=true
                shift
                ;;
            --no-test)
                NO_TEST=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # 确认部署
    if [ "$FORCE" = false ]; then
        echo -n "确认部署到 $DEPLOY_ENV 环境? (y/N): "
        read -r CONFIRM
        if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$BACKUP" = true ]; then
        create_backup
    fi
    
    if [ "$NO_TEST" = false ]; then
        run_tests
    fi
    
    if [ "$NO_BUILD" = false ]; then
        build_image
    fi
    
    stop_services
    start_services
    
    if health_check; then
        post_deploy_verification
        cleanup_old_images
        send_notification
        log_success "部署完成！"
        log_info "服务地址: http://localhost:8088"
        log_info "健康检查: http://localhost:8088/health"
    else
        log_error "部署失败，请检查日志"
        docker-compose logs compliance-checker
        exit 1
    fi
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"