#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招标文件合规性检查助手 - 主入口文件
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

from app.core.config import settings
from app.core.logger import log, request_logger
from app.api.routes import router
from app.middleware.logging import LoggingMiddleware, RequestSizeMiddleware

# 导入性能优化组件
from app.core.cache_manager import memory_cache, resource_manager
from app.core.queue_manager import request_queue


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    log.info("招标文件合规性检查助手启动中...")
    log.info(f"配置信息: 模型={settings.model_name}, 环境={settings.environment}")

    # 启动性能优化组件
    if settings.enable_async_processing:
        log.info("启动异步请求队列...")
        await request_queue.start_workers(settings.async_worker_count)
        log.info(f"请求队列已启动，工作线程数: {settings.async_worker_count}")

    # 初始化缓存
    if settings.enable_document_cache:
        log.info(f"文档缓存已启用，最大大小: {settings.cache_max_size_mb}MB")

    # 记录资源管理配置
    log.info(
        f"内存阈值: {settings.memory_threshold_mb}MB, 自动GC: {settings.enable_auto_gc}"
    )

    yield

    # 关闭时执行
    log.info("招标文件合规性检查助手关闭中...")

    # 停止性能优化组件
    if settings.enable_async_processing:
        log.info("停止异步请求队列...")
        await request_queue.stop_workers()
        log.info("请求队列已停止")

    # 清理缓存
    if settings.enable_document_cache:
        memory_cache.clear()
        log.info("缓存已清理")

    log.info("招标文件合规性检查助手已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="招标文件合规性检查助手",
    description="基于AI大模型的招标文件合规性检查API服务",
    version="1.0.0",
    lifespan=lifespan,
)

# 导入中间件
from app.middleware.validation import (
    RequestValidationMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware,
)

# 添加中间件（注意顺序很重要）
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(
    RateLimitMiddleware,
    max_requests=settings.rate_limit_requests,
    window_seconds=settings.rate_limit_window_seconds,
)
app.add_middleware(RequestValidationMiddleware, validate_enum_params=True)
app.add_middleware(LoggingMiddleware)
app.add_middleware(RequestSizeMiddleware, max_request_size=settings.max_file_size)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(router, prefix="/api/v1")

# 添加临时调试路由
from app.api.routes_temp import router_temp

app.include_router(router_temp, prefix="/api/v1")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "招标文件合规性检查助手",
        "version": "1.0.0",
        "status": "running",
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "bidding-document-compliance-checker",
        "version": "1.0.0",
    }


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8088, reload=True, log_level="info")
