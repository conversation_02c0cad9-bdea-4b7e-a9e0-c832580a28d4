# -*- coding: utf-8 -*-
"""
结果处理器测试
"""

import pytest
from unittest.mock import patch

from app.services.result_processor import (
    ResultProcessor,
    ResultProcessingError,
    result_processor,
)
from app.models.schemas import (
    SensitiveWordItem,
    CheckResultItem,
    ComplianceCheckResponse,
)
from app.models.enums import QuestionType


class TestResultProcessingError:
    """结果处理异常测试"""

    def test_result_processing_error_basic(self):
        """测试基础结果处理异常"""
        error = ResultProcessingError("处理失败")
        assert str(error) == "处理失败"
        assert error.error_type == "RESULT_PROCESSING_ERROR"

    def test_result_processing_error_with_details(self):
        """测试带详细信息的结果处理异常"""
        original_error = ValueError("原始错误")
        error = ResultProcessingError(
            "处理失败",
            stage="数据验证",
            data={"test": "data"},
            original_error=original_error,
        )

        error_str = str(error)
        assert "处理失败" in error_str
        assert "数据验证" in error_str
        assert "原始错误" in error_str


class TestResultProcessor:
    """结果处理器测试"""

    @pytest.fixture
    def processor(self):
        """结果处理器实例"""
        return ResultProcessor()

    @pytest.fixture
    def valid_sensitive_words(self):
        """有效的敏感词列表"""
        return [
            SensitiveWordItem(type="政治敏感", content="敏感词1", num=2),
            SensitiveWordItem(type="商业敏感", content="敏感词2", num=1),
            SensitiveWordItem(type="技术敏感", content="敏感词3", num=3),
        ]

    @pytest.fixture
    def invalid_sensitive_words(self):
        """无效的敏感词列表"""
        return [
            SensitiveWordItem(type="政治敏感", content="", num=2),  # 空内容
            SensitiveWordItem(type="商业敏感", content="有效词", num=0),  # 出现次数为0
            SensitiveWordItem(type="", content="无类型词", num=1),  # 空类型
            SensitiveWordItem(type="技术敏感", content="正常词", num=1),  # 正常
        ]

    @pytest.fixture
    def valid_check_results(self):
        """有效的检查结果列表"""
        return [
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE.value,
                quesDesc="合规性问题描述",
                originalArr=["原文1", "原文2"],
                point="质量控制要点1",
                advice="处理建议1",
            ),
            CheckResultItem(
                quesType=QuestionType.LOGIC.value,
                quesDesc="逻辑性问题描述",
                originalArr=["原文3"],
                point="质量控制要点2",
                advice="处理建议2",
            ),
        ]

    @pytest.fixture
    def invalid_check_results(self):
        """无效的检查结果列表"""
        return [
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE.value,
                quesDesc="",  # 空描述
                originalArr=["原文1"],
                point="质量控制要点1",
                advice="处理建议1",
            ),
            CheckResultItem(
                quesType="无效类型",  # 无效类型
                quesDesc="问题描述",
                originalArr=["原文2"],
                point="质量控制要点2",
                advice="处理建议2",
            ),
            CheckResultItem(
                quesType=QuestionType.LOGIC.value,
                quesDesc="正常问题描述",
                originalArr=[],  # 空原文内容
                point="质量控制要点3",
                advice="处理建议3",
            ),
        ]

    def test_validate_sensitive_words_valid(self, processor, valid_sensitive_words):
        """测试验证有效敏感词"""
        result = processor.validate_sensitive_words(valid_sensitive_words)

        assert len(result) == 3
        assert all(isinstance(word, SensitiveWordItem) for word in result)
        assert all(word.content and word.num > 0 for word in result)

    def test_validate_sensitive_words_invalid(self, processor, invalid_sensitive_words):
        """测试验证无效敏感词"""
        result = processor.validate_sensitive_words(invalid_sensitive_words)

        # 只有最后一个是有效的
        assert len(result) == 2  # 无类型词会被修正，正常词保留

        # 检查类型修正
        type_corrected = next(
            (word for word in result if word.content == "无类型词"), None
        )
        assert type_corrected is not None
        assert type_corrected.type == "其他"

    def test_validate_sensitive_words_exception(self, processor):
        """测试敏感词验证异常"""
        # 传入非SensitiveWordItem对象
        invalid_data = ["not a sensitive word item"]

        with pytest.raises(ResultProcessingError) as exc_info:
            processor.validate_sensitive_words(invalid_data)

        error = exc_info.value
        assert error.stage == "敏感词验证"

    def test_validate_check_results_valid(self, processor, valid_check_results):
        """测试验证有效检查结果"""
        result = processor.validate_check_results(valid_check_results)

        assert len(result) == 2
        assert all(isinstance(item, CheckResultItem) for item in result)
        assert all(item.quesDesc and item.point and item.advice for item in result)

    def test_validate_check_results_invalid(self, processor, invalid_check_results):
        """测试验证无效检查结果"""
        result = processor.validate_check_results(invalid_check_results)

        # 第二个结果会被修正（无效类型改为默认值），第三个结果会被修正（添加默认原文内容）
        assert len(result) == 2

        # 检查类型修正
        type_corrected = next(
            (item for item in result if item.quesDesc == "问题描述"), None
        )
        assert type_corrected is not None
        assert type_corrected.quesType == QuestionType.STANDARDIZATION.value

        # 检查原文内容修正
        pos_corrected = next(
            (item for item in result if item.quesDesc == "正常问题描述"), None
        )
        assert pos_corrected is not None
        assert pos_corrected.originalArr == ["原文内容未指定"]

    def test_validate_check_results_exception(self, processor):
        """测试检查结果验证异常"""
        # 传入非CheckResultItem对象
        invalid_data = ["not a check result item"]

        with pytest.raises(ResultProcessingError) as exc_info:
            processor.validate_check_results(invalid_data)

        error = exc_info.value
        assert error.stage == "检查结果验证"

    def test_deduplicate_sensitive_words(self, processor):
        """测试敏感词去重"""
        # 创建包含重复的敏感词列表
        sensitive_words = [
            SensitiveWordItem(type="政治敏感", content="重复词", num=2),
            SensitiveWordItem(
                type="政治敏感", content="重复词", num=3
            ),  # 相同内容和类型
            SensitiveWordItem(
                type="商业敏感", content="重复词", num=1
            ),  # 相同内容，不同类型
            SensitiveWordItem(type="技术敏感", content="唯一词", num=1),
        ]

        result = processor.deduplicate_sensitive_words(sensitive_words)

        assert len(result) == 3  # 去重后应该有3个

        # 检查合并结果
        political_word = next(
            (
                word
                for word in result
                if word.type == "政治敏感" and word.content == "重复词"
            ),
            None,
        )
        assert political_word is not None
        assert political_word.num == 5  # 2 + 3 = 5

        # 检查排序（按出现次数降序）
        assert result[0].num >= result[1].num >= result[2].num

    def test_deduplicate_sensitive_words_exception(self, processor, caplog):
        """测试敏感词去重异常"""
        # 创建会导致异常的数据
        invalid_data = [None]

        result = processor.deduplicate_sensitive_words(invalid_data)

        # 异常时应该返回原始数据
        assert result == invalid_data
        assert "敏感词去重失败" in caplog.text

    def test_prioritize_check_results(self, processor):
        """测试检查结果优先级排序"""
        # 创建不同优先级的检查结果
        check_results = [
            CheckResultItem(
                quesType=QuestionType.OPERABILITY.value,  # 优先级最低
                quesDesc="可操作性问题",
                originalArr=["原文1"],
                point="要点1",
                advice="建议1",
            ),
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE.value,  # 优先级最高
                quesDesc="合规性问题",
                originalArr=["原文2"],
                point="要点2",
                advice="建议2",
            ),
            CheckResultItem(
                quesType=QuestionType.LOGIC.value,  # 中等优先级
                quesDesc="逻辑性问题",
                originalArr=["原文3"],
                point="要点3",
                advice="建议3",
            ),
        ]

        result = processor.prioritize_check_results(check_results)

        assert len(result) == 3
        # 检查排序：合规性 > 逻辑性 > 可操作性
        assert result[0].quesType == QuestionType.COMPLIANCE.value
        assert result[1].quesType == QuestionType.LOGIC.value
        assert result[2].quesType == QuestionType.OPERABILITY.value

    def test_prioritize_check_results_exception(self, processor, caplog):
        """测试检查结果排序异常"""
        # 创建会导致异常的数据
        invalid_data = [None]

        result = processor.prioritize_check_results(invalid_data)

        # 异常时应该返回原始数据
        assert result == invalid_data
        assert "检查结果排序失败" in caplog.text

    def test_limit_results_normal(self, processor, valid_check_results):
        """测试正常结果数量限制"""
        result = processor.limit_results(valid_check_results, max_results=5)

        # 数量未超限，应该返回原始数据
        assert len(result) == len(valid_check_results)
        assert result == valid_check_results

    def test_limit_results_exceed(self, processor, caplog):
        """测试超限结果数量限制"""
        # 创建超过限制的检查结果
        check_results = [
            CheckResultItem(
                quesType=QuestionType.COMPLIANCE.value,
                quesDesc=f"问题{i}",
                originalArr=[f"原文{i}"],
                point=f"要点{i}",
                advice=f"建议{i}",
            )
            for i in range(20)  # 创建20个结果
        ]

        result = processor.limit_results(check_results, max_results=15)

        assert len(result) == 15
        assert "检查结果数量超限" in caplog.text

    def test_aggregate_results_success(
        self, processor, valid_sensitive_words, valid_check_results
    ):
        """测试成功聚合结果"""
        result = processor.aggregate_results(
            valid_sensitive_words, valid_check_results, "test-123"
        )

        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.sensitiveWordsArr) == 3
        assert len(result.checkResultArr) == 2

        # 检查排序（敏感词按出现次数降序）
        assert result.sensitiveWordsArr[0].num >= result.sensitiveWordsArr[1].num

        # 检查排序（检查结果按优先级）
        assert result.checkResultArr[0].quesType == QuestionType.COMPLIANCE.value

    def test_aggregate_results_exception(self, processor):
        """测试聚合结果异常"""
        # 传入会导致异常的数据
        invalid_sensitive_words = [None]
        invalid_check_results = [None]

        with pytest.raises(ResultProcessingError) as exc_info:
            processor.aggregate_results(invalid_sensitive_words, invalid_check_results)

        error = exc_info.value
        assert error.stage == "结果聚合"

    def test_create_empty_response(self, processor):
        """测试创建空响应"""
        result = processor.create_empty_response("测试原因")

        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.sensitiveWordsArr) == 0
        assert len(result.checkResultArr) == 0

    def test_validate_final_response_valid(self, processor):
        """测试验证有效的最终响应"""
        response = ComplianceCheckResponse(
            sensitiveWordsArr=[
                SensitiveWordItem(type="政治敏感", content="测试词", num=1)
            ],
            checkResultArr=[
                CheckResultItem(
                    quesType=QuestionType.COMPLIANCE.value,
                    quesDesc="测试问题",
                    originalArr=["原文"],
                    point="要点",
                    advice="建议",
                )
            ],
        )

        result = processor.validate_final_response(response)

        assert len(result.sensitiveWordsArr) == 1
        assert len(result.checkResultArr) == 1

    def test_validate_final_response_invalid(self, processor):
        """测试验证无效的最终响应"""
        response = ComplianceCheckResponse(
            sensitiveWordsArr=[
                SensitiveWordItem(type="政治敏感", content="", num=1),  # 空内容
                SensitiveWordItem(type="商业敏感", content="有效词", num=1),  # 有效
            ],
            checkResultArr=[
                CheckResultItem(
                    quesType=QuestionType.COMPLIANCE.value,
                    quesDesc="",  # 空描述
                    originalArr=["原文"],
                    point="要点",
                    advice="建议",
                ),
                CheckResultItem(
                    quesType=QuestionType.LOGIC.value,
                    quesDesc="有效问题",
                    originalArr=["原文"],
                    point="要点",
                    advice="建议",
                ),
            ],
        )

        result = processor.validate_final_response(response)

        # 只保留有效的项
        assert len(result.sensitiveWordsArr) == 1
        assert len(result.checkResultArr) == 1
        assert result.sensitiveWordsArr[0].content == "有效词"
        assert result.checkResultArr[0].quesDesc == "有效问题"

    def test_validate_final_response_none_fields(self, processor):
        """测试验证包含None字段的响应"""
        response = ComplianceCheckResponse(sensitiveWordsArr=None, checkResultArr=None)

        result = processor.validate_final_response(response)

        assert len(result.sensitiveWordsArr) == 0
        assert len(result.checkResultArr) == 0

    def test_validate_final_response_exception(self, processor, caplog):
        """测试响应验证异常"""
        # 创建会导致异常的响应
        invalid_response = "not a response object"

        result = processor.validate_final_response(invalid_response)

        # 异常时应该返回空响应
        assert len(result.sensitiveWordsArr) == 0
        assert len(result.checkResultArr) == 0
        assert "响应验证失败" in caplog.text

    def test_process_with_fallback_success(
        self, processor, valid_sensitive_words, valid_check_results
    ):
        """测试带降级机制的成功处理"""
        result = processor.process_with_fallback(
            valid_sensitive_words, valid_check_results, "test-123"
        )

        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.sensitiveWordsArr) > 0
        assert len(result.checkResultArr) > 0

    @patch.object(ResultProcessor, "aggregate_results")
    def test_process_with_fallback_aggregate_error(
        self, mock_aggregate, processor, valid_sensitive_words, valid_check_results
    ):
        """测试聚合失败时的降级处理"""
        mock_aggregate.side_effect = ResultProcessingError("聚合失败", stage="聚合")

        result = processor.process_with_fallback(
            valid_sensitive_words, valid_check_results, "test-123"
        )

        # 应该使用降级策略
        assert isinstance(result, ComplianceCheckResponse)
        # 降级策略应该能处理部分数据
        assert len(result.sensitiveWordsArr) > 0 or len(result.checkResultArr) > 0

    @patch.object(ResultProcessor, "aggregate_results")
    @patch.object(ResultProcessor, "validate_sensitive_words")
    def test_process_with_fallback_all_fail(
        self,
        mock_validate_sensitive,
        mock_aggregate,
        processor,
        valid_sensitive_words,
        valid_check_results,
    ):
        """测试所有处理都失败时的最终降级"""
        mock_aggregate.side_effect = ResultProcessingError("聚合失败")
        mock_validate_sensitive.side_effect = Exception("验证失败")

        result = processor.process_with_fallback(
            valid_sensitive_words, valid_check_results, "test-123"
        )

        # 应该返回空响应
        assert isinstance(result, ComplianceCheckResponse)
        assert len(result.sensitiveWordsArr) == 0
        assert len(result.checkResultArr) == 0

    def test_get_processing_stats(self, processor):
        """测试获取处理统计信息"""
        stats = processor.get_processing_stats()

        assert "max_check_results" in stats
        assert "supported_question_types" in stats
        assert "question_type_priorities" in stats
        assert "default_response_format" in stats

        assert stats["max_check_results"] == 15
        assert len(stats["supported_question_types"]) == 6
        assert QuestionType.COMPLIANCE.value in stats["supported_question_types"]


class TestGlobalProcessor:
    """全局处理器实例测试"""

    def test_global_processor_exists(self):
        """测试全局处理器实例存在"""
        assert result_processor is not None
        assert isinstance(result_processor, ResultProcessor)
