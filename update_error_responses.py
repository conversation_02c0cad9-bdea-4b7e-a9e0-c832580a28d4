#!/usr/bin/env python3
"""
批量更新错误响应格式的脚本
"""

import re

def update_error_responses():
    """更新错误响应格式"""
    file_path = "app/core/exceptions.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换模式
    patterns = [
        # AI模型错误
        (
            r'return JSONResponse\(\s*status_code=error\.status_code,\s*content=\{\s*"success": False,\s*"message": "AI模型服务暂时不可用，请稍后重试",\s*"error_code": error\.error_code,\s*"details": \{"model_name": error\.model_name, "operation": error\.operation\},\s*"timestamp": error\.timestamp\.isoformat\(\),\s*\},\s*\)',
            '''return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "AI模型服务暂时不可用，请稍后重试",
                "data": {
                    "error_code": error.error_code,
                    "details": {"model_name": error.model_name, "operation": error.operation},
                    "timestamp": error.timestamp.isoformat(),
                }
            },
        )'''
        ),
        # 外部服务错误
        (
            r'return JSONResponse\(\s*status_code=error\.status_code,\s*content=\{\s*"success": False,\s*"message": "外部服务暂时不可用，请稍后重试",\s*"error_code": error\.error_code,\s*"details": \{"service_name": error\.service_name, "endpoint": error\.endpoint\},\s*"timestamp": error\.timestamp\.isoformat\(\),\s*\},\s*\)',
            '''return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "外部服务暂时不可用，请稍后重试",
                "data": {
                    "error_code": error.error_code,
                    "details": {"service_name": error.service_name, "endpoint": error.endpoint},
                    "timestamp": error.timestamp.isoformat(),
                }
            },
        )'''
        ),
        # 业务逻辑错误
        (
            r'return JSONResponse\(\s*status_code=error\.status_code,\s*content=\{\s*"success": False,\s*"message": error\.message,\s*"error_code": error\.error_code,\s*"details": \{"operation": error\.operation\},\s*"timestamp": error\.timestamp\.isoformat\(\),\s*\},\s*\)',
            '''return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": error.message,
                "data": {
                    "error_code": error.error_code,
                    "details": {"operation": error.operation},
                    "timestamp": error.timestamp.isoformat(),
                }
            },
        )'''
        ),
        # 配置错误
        (
            r'return JSONResponse\(\s*status_code=error\.status_code,\s*content=\{\s*"success": False,\s*"message": "系统配置错误，请联系管理员",\s*"error_code": error\.error_code,\s*"details": \{"config_key": error\.config_key\},\s*"timestamp": error\.timestamp\.isoformat\(\),\s*\},\s*\)',
            '''return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "系统配置错误，请联系管理员",
                "data": {
                    "error_code": error.error_code,
                    "details": {"config_key": error.config_key},
                    "timestamp": error.timestamp.isoformat(),
                }
            },
        )'''
        ),
        # 限流错误
        (
            r'return JSONResponse\(\s*status_code=error\.status_code,\s*content=\{\s*"success": False,\s*"message": "请求过于频繁，请稍后重试",\s*"error_code": error\.error_code,\s*"details": \{"limit": error\.limit, "window": error\.window\},\s*"timestamp": error\.timestamp\.isoformat\(\),\s*\},\s*headers=headers,\s*\)',
            '''return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "请求过于频繁，请稍后重试",
                "data": {
                    "error_code": error.error_code,
                    "details": {"limit": error.limit, "window": error.window},
                    "timestamp": error.timestamp.isoformat(),
                }
            },
            headers=headers,
        )'''
        ),
        # 通用错误
        (
            r'return JSONResponse\(\s*status_code=500,\s*content=\{\s*"success": False,\s*"message": "系统内部错误，请稍后重试",\s*"error_code": "INTERNAL_SERVER_ERROR",\s*"timestamp": "2025-08-06",\s*\},\s*\)',
            '''return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": "系统内部错误，请稍后重试",
                "data": {
                    "error_code": "INTERNAL_SERVER_ERROR",
                    "timestamp": "2025-08-06",
                }
            },
        )'''
        )
    ]
    
    # 应用替换
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("错误响应格式更新完成")

if __name__ == "__main__":
    update_error_responses()
