# -*- coding: utf-8 -*-
"""
日志配置模块
基于loguru库实现结构化日志记录
"""

import os
import sys
import json
import traceback
import time
from functools import wraps
from typing import Any, Dict, Optional, Callable
from datetime import datetime
from loguru import logger

from app.core.config import settings


class LoggerConfig:
    """日志配置类"""

    def __init__(self):
        self.setup_logger()

    def setup_logger(self):
        """设置日志配置"""
        # 移除默认的控制台处理器
        logger.remove()

        # 添加控制台输出（开发环境）
        if settings.debug:
            logger.add(
                sys.stdout,
                level=settings.log_level,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                "<level>{message}</level>",
                colorize=True,
                enqueue=True,
                filter=lambda record: record["extra"].get("name")
                == "compliance_checker",
            )

        # 确保日志目录存在
        log_dir = settings.log_file_path
        os.makedirs(log_dir, exist_ok=True)

        # 添加文件输出
        log_path = os.path.join(log_dir, "{time:YYYY_MM}/{time:YYYY-MM-DD}.log")
        logger.add(
            log_path,
            level=settings.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="00:00",  # 每天00:00自动生成新的日志文件
            retention="30 days",  # 保留30天的日志文件
            compression="zip",  # 压缩旧日志文件
            mode="a+",
            backtrace=True,  # 记录完整的错误堆栈
            diagnose=True,  # 记录完整的错误信息
            encoding="utf-8",
            enqueue=True,  # 日志调用非阻塞
            filter=lambda record: record["extra"].get("name") == "compliance_checker",
        )

        # 添加错误日志单独文件
        error_log_path = os.path.join(
            log_dir, "{time:YYYY_MM}/{time:YYYY-MM-DD}_error.log"
        )
        logger.add(
            error_log_path,
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="00:00",
            retention="60 days",  # 错误日志保留更长时间
            compression="zip",
            mode="a+",
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: record["extra"].get("name") == "compliance_checker",
        )


# 初始化日志配置
logger_config = LoggerConfig()

# 创建绑定了上下文的日志记录器
log = logger.bind(name="compliance_checker")


class RequestLogger:
    """请求日志记录器"""

    @staticmethod
    def log_request(
        request_id: str, method: str, url: str, request_data: Dict[str, Any]
    ):
        """记录请求日志"""
        log.info(
            f"请求开始 | ID: {request_id} | 方法: {method} | URL: {url} | "
            f"数据大小: {len(str(request_data))} 字符"
        )

    @staticmethod
    def log_response(
        request_id: str, status_code: int, response_size: int, duration: float
    ):
        """记录响应日志"""
        log.info(
            f"请求完成 | ID: {request_id} | 状态码: {status_code} | "
            f"响应大小: {response_size} 字节 | 耗时: {duration:.3f}秒"
        )

    @staticmethod
    def log_error(
        request_id: str, error: Exception, context: Optional[Dict[str, Any]] = None
    ):
        """记录错误日志"""
        error_info = {
            "request_id": request_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {},
        }
        log.error(f"请求错误 | {json.dumps(error_info, ensure_ascii=False)}")


class PerformanceLogger:
    """性能日志记录器"""

    @staticmethod
    def log_operation_start(
        operation: str, request_id: str, details: Optional[Dict[str, Any]] = None
    ):
        """记录操作开始"""
        log.info(
            f"操作开始 | {operation} | ID: {request_id} | "
            f"详情: {json.dumps(details or {}, ensure_ascii=False)}"
        )

    @staticmethod
    def log_operation_end(
        operation: str, request_id: str, duration: float, success: bool = True
    ):
        """记录操作结束"""
        status = "成功" if success else "失败"
        log.info(
            f"操作结束 | {operation} | ID: {request_id} | 状态: {status} | 耗时: {duration:.3f}秒"
        )

    @staticmethod
    def log_file_processing(
        request_id: str, filename: str, file_size: int, processing_time: float
    ):
        """记录文件处理性能"""
        log.info(
            f"文件处理 | ID: {request_id} | 文件: {filename} | "
            f"大小: {file_size} 字节 | 处理时间: {processing_time:.3f}秒"
        )

    @staticmethod
    def log_api_call(
        request_id: str, api_name: str, duration: float, success: bool = True
    ):
        """记录API调用性能"""
        status = "成功" if success else "失败"
        log.info(
            f"API调用 | {api_name} | ID: {request_id} | 状态: {status} | 耗时: {duration:.3f}秒"
        )


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {}
        self.thresholds = {
            "file_processing": 60.0,  # 文件处理60秒阈值
            "ai_model_call": 180.0,  # AI模型调用180秒阈值
            "sensitive_word": 10.0,  # 敏感词检测10秒阈值
            "result_aggregation": 5.0,  # 结果聚合5秒阈值
            "total_pipeline": 300.0,  # 总流水线300秒阈值
        }

    def record_stage_performance(
        self, request_id: str, stage: str, duration: float, success: bool = True
    ):
        """记录阶段性能"""
        if request_id not in self.metrics:
            self.metrics[request_id] = {}

        self.metrics[request_id][stage] = {
            "duration": duration,
            "success": success,
            "timestamp": datetime.now().isoformat(),
        }

        # 检查是否超过阈值
        threshold = self.thresholds.get(stage, 180.0)
        if duration > threshold:
            log.warning(
                f"性能警告 | 阶段: {stage} | ID: {request_id} | "
                f"耗时: {duration:.3f}秒 > 阈值: {threshold}秒"
            )

        # 记录性能日志
        status = "成功" if success else "失败"
        log.info(
            f"阶段性能 | {stage} | ID: {request_id} | "
            f"状态: {status} | 耗时: {duration:.3f}秒"
        )

    def get_request_metrics(self, request_id: str) -> dict:
        """获取请求的性能指标"""
        return self.metrics.get(request_id, {})

    def analyze_performance(self, request_id: str) -> dict:
        """分析请求性能"""
        metrics = self.get_request_metrics(request_id)
        if not metrics:
            return {"error": "未找到性能数据"}

        # 分离总体指标和阶段指标
        stage_metrics = {
            k: v for k, v in metrics.items() if not k.endswith("_pipeline")
        }
        pipeline_metrics = {k: v for k, v in metrics.items() if k.endswith("_pipeline")}

        analysis = {
            "request_id": request_id,
            "total_stages": len(stage_metrics),  # 只计算实际阶段数
            "total_duration": pipeline_metrics.get("total_pipeline", {}).get(
                "duration", 0
            ),
            "successful_stages": sum(1 for m in stage_metrics.values() if m["success"]),
            "failed_stages": sum(1 for m in stage_metrics.values() if not m["success"]),
            "slowest_stage": None,
            "fastest_stage": None,
            "bottlenecks": [],
            "recommendations": [],
        }

        if stage_metrics:
            # 找出最慢和最快的阶段（只考虑实际处理阶段）
            sorted_stages = sorted(
                stage_metrics.items(), key=lambda x: x[1]["duration"]
            )
            analysis["fastest_stage"] = {
                "name": sorted_stages[0][0],
                "duration": sorted_stages[0][1]["duration"],
            }
            analysis["slowest_stage"] = {
                "name": sorted_stages[-1][0],
                "duration": sorted_stages[-1][1]["duration"],
            }

            # 识别瓶颈（只考虑实际处理阶段）
            for stage, data in stage_metrics.items():
                threshold = self.thresholds.get(stage, 180.0)
                if data["duration"] > threshold:
                    analysis["bottlenecks"].append(
                        {
                            "stage": stage,
                            "duration": data["duration"],
                            "threshold": threshold,
                            "excess": data["duration"] - threshold,
                        }
                    )

            # 生成优化建议
            if analysis["bottlenecks"]:
                analysis["recommendations"].append("发现性能瓶颈，建议优化慢速阶段")

            if analysis["failed_stages"] > 0:
                analysis["recommendations"].append("存在失败阶段，建议检查错误处理")

            if analysis["total_duration"] > self.thresholds["total_pipeline"]:
                analysis["recommendations"].append("总处理时间过长，建议并行化处理")

        return analysis

    def cleanup_old_metrics(self, max_age_hours: int = 24):
        """清理旧的性能指标"""
        from datetime import timedelta

        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        to_remove = []
        for request_id, stages in self.metrics.items():
            # 检查最新的时间戳
            latest_time = max(
                datetime.fromisoformat(stage["timestamp"]) for stage in stages.values()
            )

            if latest_time < cutoff_time:
                to_remove.append(request_id)

        for request_id in to_remove:
            del self.metrics[request_id]

        if to_remove:
            log.info(f"清理了 {len(to_remove)} 个过期的性能指标记录")

    def get_global_stats(self) -> dict:
        """获取全局性能统计"""
        if not self.metrics:
            return {"message": "暂无性能数据"}

        all_durations = []
        stage_stats = {}
        success_count = 0
        total_requests = len(self.metrics)

        for request_metrics in self.metrics.values():
            request_total = sum(m["duration"] for m in request_metrics.values())
            all_durations.append(request_total)

            request_success = all(m["success"] for m in request_metrics.values())
            if request_success:
                success_count += 1

            for stage, data in request_metrics.items():
                if stage not in stage_stats:
                    stage_stats[stage] = []
                stage_stats[stage].append(data["duration"])

        return {
            "total_requests": total_requests,
            "success_rate": success_count / total_requests if total_requests > 0 else 0,
            "average_duration": (
                sum(all_durations) / len(all_durations) if all_durations else 0
            ),
            "min_duration": min(all_durations) if all_durations else 0,
            "max_duration": max(all_durations) if all_durations else 0,
            "stage_averages": {
                stage: sum(durations) / len(durations)
                for stage, durations in stage_stats.items()
            },
        }


def error_handler(func: Callable) -> Callable:
    """
    错误处理装饰器
    基于参考代码的error_handler模式
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 记录完整的错误堆栈
            error_msg = traceback.format_exc()
            log.error(f"函数 {func.__name__} 执行失败: {error_msg}")

            # 返回标准化的错误响应
            error_response = {
                "status": "failure",
                "message": str(e),
                "function": func.__name__,
                "timestamp": datetime.now().isoformat(),
            }
            return error_response

    return wrapper


def async_error_handler(func: Callable) -> Callable:
    """
    异步函数错误处理装饰器
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # 记录完整的错误堆栈
            error_msg = traceback.format_exc()
            log.error(f"异步函数 {func.__name__} 执行失败: {error_msg}")

            # 返回标准化的错误响应
            error_response = {
                "status": "failure",
                "message": str(e),
                "function": func.__name__,
                "timestamp": datetime.now().isoformat(),
            }
            return error_response

    return wrapper


class TimingContext:
    """计时上下文管理器"""

    def __init__(self, operation: str, request_id: str = ""):
        self.operation = operation
        self.request_id = request_id
        self.start_time = None
        self.end_time = None

    def __enter__(self):
        self.start_time = time.time()
        PerformanceLogger.log_operation_start(self.operation, self.request_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        success = exc_type is None
        PerformanceLogger.log_operation_end(
            self.operation, self.request_id, duration, success
        )

        if exc_type is not None:
            log.error(f"操作 {self.operation} 发生异常: {exc_val}")

    @property
    def duration(self) -> float:
        """获取执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


def log_function_call(func: Callable) -> Callable:
    """
    函数调用日志装饰器
    记录函数的调用和执行时间
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = func.__name__

        # 记录函数调用开始
        log.debug(f"函数调用开始: {func_name}")

        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            log.debug(f"函数调用成功: {func_name} | 耗时: {duration:.3f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log.error(
                f"函数调用失败: {func_name} | 耗时: {duration:.3f}秒 | 错误: {str(e)}"
            )
            raise

    return wrapper


def log_async_function_call(func: Callable) -> Callable:
    """
    异步函数调用日志装饰器
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = func.__name__

        # 记录函数调用开始
        log.debug(f"异步函数调用开始: {func_name}")

        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            log.debug(f"异步函数调用成功: {func_name} | 耗时: {duration:.3f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            log.error(
                f"异步函数调用失败: {func_name} | 耗时: {duration:.3f}秒 | 错误: {str(e)}"
            )
            raise

    return wrapper


# 导出常用的日志记录器实例
request_logger = RequestLogger()
performance_logger = PerformanceLogger()

# 创建全局性能监控器实例
performance_monitor = PerformanceMonitor()
