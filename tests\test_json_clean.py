#!/usr/bin/env python3
"""
测试JSON清理功能的脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_json_cleaning():
    """测试JSON清理功能"""
    print("🔍 测试JSON清理功能...")
    print("=" * 80)

    try:
        from app.services.ai_model_service import ai_model_service

        # 测试原始响应
        raw_response = '''```json
{
"checkResultArr": []
}
```'''

        print(f"1. 原始响应:")
        print(f"   '{raw_response}'")
        print()

        # 调用清理方法
        cleaned = ai_model_service.clean_json_data(raw_response)
        
        print(f"2. 清理后响应:")
        print(f"   '{cleaned}'")
        print()

        # 尝试JSON解析
        print("3. 尝试JSON解析...")
        try:
            parsed = json.loads(cleaned)
            print(f"   ✅ JSON解析成功: {parsed}")
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")
            print(f"   清理后内容: '{cleaned}'")
            print(f"   清理后长度: {len(cleaned)}")
            print(f"   清理后字节: {[ord(c) for c in cleaned[:20]]}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始JSON清理功能测试...")
    
    success = test_json_cleaning()
    
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    if success:
        print("🎉 测试完成！")
    else:
        print("❌ 测试失败")
