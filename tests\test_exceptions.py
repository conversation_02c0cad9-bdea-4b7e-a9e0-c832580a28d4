# -*- coding: utf-8 -*-
"""
异常处理测试
"""

import json
import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

from app.core.exceptions import (
    BaseComplianceError,
    ValidationError,
    FileProcessingError,
    AIModelError,
    ExternalServiceError,
    BusinessLogicError,
    ConfigurationError,
    RateLimitError,
    ErrorHandler,
    FallbackManager,
    compliance_error_handler,
    async_compliance_error_handler,
    api_error_handler,
    error_handler,
    fallback_manager,
)
from app.models.schemas import ErrorResponse


class TestBaseComplianceError:
    """基础合规检查异常测试"""

    def test_base_compliance_error_basic(self):
        """测试基础异常"""
        error = BaseComplianceError("测试错误")

        assert str(error) == "测试错误"
        assert error.message == "测试错误"
        assert error.error_code == "BaseComplianceError"
        assert error.status_code == 500
        assert isinstance(error.timestamp, datetime)

    def test_base_compliance_error_with_details(self):
        """测试带详细信息的基础异常"""
        details = {"key": "value"}
        original_error = ValueError("原始错误")

        error = BaseComplianceError(
            "测试错误",
            error_code="TEST_ERROR",
            details=details,
            status_code=400,
            original_error=original_error,
        )

        assert error.error_code == "TEST_ERROR"
        assert error.details == details
        assert error.status_code == 400
        assert error.original_error == original_error

    def test_to_dict(self):
        """测试转换为字典"""
        error = BaseComplianceError("测试错误", error_code="TEST_ERROR")
        result = error.to_dict()

        assert result["error_type"] == "BaseComplianceError"
        assert result["error_code"] == "TEST_ERROR"
        assert result["message"] == "测试错误"
        assert "timestamp" in result

    def test_to_response(self):
        """测试转换为响应格式"""
        error = BaseComplianceError("测试错误", error_code="TEST_ERROR")
        response = error.to_response()

        assert isinstance(response, ErrorResponse)
        assert response.success is False
        assert response.message == "测试错误"
        assert response.error_code == "TEST_ERROR"


class TestSpecificErrors:
    """特定异常类测试"""

    def test_validation_error(self):
        """测试验证异常"""
        error = ValidationError("验证失败", field="username", value="invalid")

        assert error.status_code == 400
        assert error.field == "username"
        assert error.value == "invalid"
        assert "field" in error.details
        assert "value" in error.details

    def test_file_processing_error(self):
        """测试文件处理异常"""
        error = FileProcessingError(
            "文件处理失败", filename="test.pdf", file_size=1024, file_type=".pdf"
        )

        assert error.status_code == 422
        assert error.filename == "test.pdf"
        assert error.file_size == 1024
        assert error.file_type == ".pdf"
        assert "filename" in error.details

    def test_ai_model_error(self):
        """测试AI模型异常"""
        error = AIModelError(
            "模型调用失败", model_name="gpt-4", request_tokens=100, response_tokens=50
        )

        assert error.status_code == 502
        assert error.model_name == "gpt-4"
        assert error.request_tokens == 100
        assert error.response_tokens == 50
        assert "model_name" in error.details

    def test_external_service_error(self):
        """测试外部服务异常"""
        error = ExternalServiceError(
            "服务调用失败",
            service_name="sensitive_word_api",
            service_url="http://api.example.com",
            response_status=503,
        )

        assert error.status_code == 503
        assert error.service_name == "sensitive_word_api"
        assert error.service_url == "http://api.example.com"
        assert error.response_status == 503
        assert "service_name" in error.details

    def test_business_logic_error(self):
        """测试业务逻辑异常"""
        context = {"operation_id": "123"}
        error = BusinessLogicError(
            "业务逻辑错误", operation="check_compliance", context=context
        )

        assert error.status_code == 422
        assert error.operation == "check_compliance"
        assert error.context == context
        assert "operation" in error.details

    def test_configuration_error(self):
        """测试配置异常"""
        error = ConfigurationError(
            "配置错误", config_key="API_KEY", config_value="invalid_key"
        )

        assert error.status_code == 500
        assert error.config_key == "API_KEY"
        assert error.config_value == "invalid_key"
        assert "config_key" in error.details

    def test_rate_limit_error(self):
        """测试限流异常"""
        error = RateLimitError("请求过于频繁", limit=100, window=3600, retry_after=60)

        assert error.status_code == 429
        assert error.limit == 100
        assert error.window == 3600
        assert error.retry_after == 60
        assert "limit" in error.details


class TestErrorHandler:
    """错误处理器测试"""

    @pytest.fixture
    def handler(self):
        """错误处理器实例"""
        return ErrorHandler()

    def test_handle_validation_error(self, handler):
        """测试处理验证错误"""
        error = ValidationError("验证失败", field="username", value="invalid")
        response = handler.handle_validation_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 400

        # 检查响应内容
        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert content["message"] == "验证失败"
        assert "details" in content

    def test_handle_file_processing_error(self, handler):
        """测试处理文件处理错误"""
        error = FileProcessingError("文件处理失败", filename="test.pdf")
        response = handler.handle_file_processing_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 422

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "文件处理失败" in content["message"]

    def test_handle_ai_model_error(self, handler):
        """测试处理AI模型错误"""
        error = AIModelError("模型调用失败", model_name="gpt-4")
        response = handler.handle_ai_model_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 502

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "AI模型服务暂时不可用" in content["message"]

    def test_handle_external_service_error(self, handler):
        """测试处理外部服务错误"""
        error = ExternalServiceError("服务调用失败", service_name="api")
        response = handler.handle_external_service_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 503

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "外部服务暂时不可用" in content["message"]

    def test_handle_business_logic_error(self, handler):
        """测试处理业务逻辑错误"""
        error = BusinessLogicError("业务逻辑错误", operation="test")
        response = handler.handle_business_logic_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 422

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert content["message"] == "业务逻辑错误"

    def test_handle_configuration_error(self, handler):
        """测试处理配置错误"""
        error = ConfigurationError("配置错误", config_key="API_KEY")
        response = handler.handle_configuration_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 500

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "系统配置错误" in content["message"]

    def test_handle_rate_limit_error(self, handler):
        """测试处理限流错误"""
        error = RateLimitError("请求过于频繁", retry_after=60)
        response = handler.handle_rate_limit_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 429
        assert "Retry-After" in response.headers
        assert response.headers["Retry-After"] == "60"

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "请求过于频繁" in content["message"]

    def test_handle_generic_error(self, handler):
        """测试处理通用错误"""
        error = Exception("未知错误")
        response = handler.handle_generic_error(error, "test-123")

        assert isinstance(response, JSONResponse)
        assert response.status_code == 500

        content = json.loads(response.body.decode())
        assert content["success"] is False
        assert "系统内部错误" in content["message"]


class TestErrorDecorators:
    """错误装饰器测试"""

    def test_compliance_error_handler_success(self):
        """测试合规检查错误装饰器成功情况"""

        @compliance_error_handler
        def test_function():
            return "success"

        result = test_function()
        assert result == "success"

    def test_compliance_error_handler_exception(self):
        """测试合规检查错误装饰器异常情况"""

        @compliance_error_handler
        def test_function():
            raise ValueError("测试异常")

        result = test_function()

        # 结果应该是JSON字符串
        assert isinstance(result, str)
        parsed_result = json.loads(result)
        assert parsed_result["status"] == "failure"
        assert "测试异常" in parsed_result["message"]
        assert parsed_result["function"] == "test_function"

    @pytest.mark.asyncio
    async def test_async_compliance_error_handler_success(self):
        """测试异步合规检查错误装饰器成功情况"""

        @async_compliance_error_handler
        async def test_async_function():
            return "async success"

        result = await test_async_function()
        assert result == "async success"

    @pytest.mark.asyncio
    async def test_async_compliance_error_handler_exception(self):
        """测试异步合规检查错误装饰器异常情况"""

        @async_compliance_error_handler
        async def test_async_function():
            raise ValueError("异步测试异常")

        result = await test_async_function()

        # 结果应该是字典
        assert isinstance(result, dict)
        assert result["status"] == "failure"
        assert "异步测试异常" in result["message"]
        assert result["function"] == "test_async_function"

    @pytest.mark.asyncio
    async def test_api_error_handler_success(self):
        """测试API错误装饰器成功情况"""

        @api_error_handler
        async def test_api_function():
            return {"result": "success"}

        result = await test_api_function()
        assert result["result"] == "success"

    @pytest.mark.asyncio
    async def test_api_error_handler_validation_error(self):
        """测试API错误装饰器处理验证错误"""

        @api_error_handler
        async def test_api_function():
            raise ValidationError("验证失败", field="test")

        result = await test_api_function()

        assert isinstance(result, JSONResponse)
        assert result.status_code == 400

    @pytest.mark.asyncio
    async def test_api_error_handler_http_exception(self):
        """测试API错误装饰器处理HTTP异常"""

        @api_error_handler
        async def test_api_function():
            raise HTTPException(status_code=404, detail="Not Found")

        with pytest.raises(HTTPException) as exc_info:
            await test_api_function()

        assert exc_info.value.status_code == 404

    @pytest.mark.asyncio
    async def test_api_error_handler_generic_exception(self):
        """测试API错误装饰器处理通用异常"""

        @api_error_handler
        async def test_api_function():
            raise Exception("未知错误")

        result = await test_api_function()

        assert isinstance(result, JSONResponse)
        assert result.status_code == 500


class TestFallbackManager:
    """降级管理器测试"""

    @pytest.fixture
    def manager(self):
        """降级管理器实例"""
        return FallbackManager()

    def test_create_fallback_response(self, manager):
        """测试创建降级响应"""
        response = manager.create_fallback_response("合规性检查", "AI服务不可用")

        assert response["sensitiveWordsArr"] == []
        assert response["checkResultArr"] == []
        assert response["fallback"] is True
        assert response["fallback_reason"] == "AI服务不可用"
        assert "合规性检查" in response["message"]

    def test_should_fallback_configuration_error(self, manager):
        """测试配置错误不应降级"""
        error = ConfigurationError("配置错误")
        result = manager.should_fallback(error)
        assert result is False

    def test_should_fallback_validation_error(self, manager):
        """测试验证错误不应降级"""
        error = ValidationError("验证错误")
        result = manager.should_fallback(error)
        assert result is False

    def test_should_fallback_max_retries(self, manager):
        """测试达到最大重试次数应降级"""
        error = AIModelError("模型错误")
        result = manager.should_fallback(error, max_retries=3, current_retry=3)
        assert result is True

    def test_should_fallback_external_service_error(self, manager):
        """测试外部服务错误应降级"""
        error = ExternalServiceError("服务错误")
        result = manager.should_fallback(error)
        assert result is True

    def test_should_fallback_ai_model_error(self, manager):
        """测试AI模型错误应降级"""
        error = AIModelError("模型错误")
        result = manager.should_fallback(error)
        assert result is True

    def test_get_fallback_strategy_ai_model_error(self, manager):
        """测试AI模型错误的降级策略"""
        error = AIModelError("模型错误")
        strategy = manager.get_fallback_strategy(error)
        assert "AI模型服务不可用" in strategy

    def test_get_fallback_strategy_external_service_error(self, manager):
        """测试外部服务错误的降级策略"""
        error = ExternalServiceError("服务错误")
        strategy = manager.get_fallback_strategy(error)
        assert "外部服务不可用" in strategy

    def test_get_fallback_strategy_file_processing_error(self, manager):
        """测试文件处理错误的降级策略"""
        error = FileProcessingError("文件错误")
        strategy = manager.get_fallback_strategy(error)
        assert "文件处理失败" in strategy

    def test_get_fallback_strategy_generic_error(self, manager):
        """测试通用错误的降级策略"""
        error = Exception("未知错误")
        strategy = manager.get_fallback_strategy(error)
        assert "系统异常" in strategy


class TestGlobalInstances:
    """全局实例测试"""

    def test_global_error_handler_exists(self):
        """测试全局错误处理器实例存在"""
        assert error_handler is not None
        assert isinstance(error_handler, ErrorHandler)

    def test_global_fallback_manager_exists(self):
        """测试全局降级管理器实例存在"""
        assert fallback_manager is not None
        assert isinstance(fallback_manager, FallbackManager)
