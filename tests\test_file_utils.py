# -*- coding: utf-8 -*-
"""
文件工具函数测试
"""

import os
import tempfile
import pytest

from app.utils.file_utils import (
    get_file_hash,
    get_filename_from_url,
    get_file_extension_from_mime,
    sanitize_filename,
    create_temp_file,
    cleanup_temp_file,
    get_file_info_summary,
    validate_url,
    get_content_preview,
    estimate_processing_time,
)


class TestFileUtils:
    """文件工具函数测试"""

    def test_get_file_hash_md5(self):
        """测试MD5哈希计算"""
        content = b"test content"
        hash_value = get_file_hash(content, "md5")

        # MD5 of "test content" should be consistent
        assert len(hash_value) == 32
        assert hash_value == get_file_hash(content, "md5")  # 一致性检查

    def test_get_file_hash_sha256(self):
        """测试SHA256哈希计算"""
        content = b"test content"
        hash_value = get_file_hash(content, "sha256")

        assert len(hash_value) == 64
        assert hash_value == get_file_hash(content, "sha256")

    def test_get_file_hash_invalid_algorithm(self):
        """测试无效哈希算法"""
        content = b"test content"
        hash_value = get_file_hash(content, "invalid")

        assert hash_value == ""  # 应该返回空字符串

    def test_get_filename_from_url(self):
        """测试从URL提取文件名"""
        url = "http://example.com/path/to/document.pdf"
        filename = get_filename_from_url(url)

        assert filename == "document.pdf"

    def test_get_filename_from_url_no_extension(self):
        """测试从URL提取文件名 - 无扩展名"""
        url = "http://example.com/path/to/document"
        filename = get_filename_from_url(url)

        assert filename == "document"

    def test_get_filename_from_url_no_filename(self):
        """测试从URL提取文件名 - 无文件名"""
        url = "http://example.com/path/to/"
        filename = get_filename_from_url(url)

        assert filename == "document"

    def test_get_filename_from_url_invalid(self):
        """测试从URL提取文件名 - 无效URL"""
        url = "invalid-url"
        filename = get_filename_from_url(url)

        assert filename == "document"

    def test_get_file_extension_from_mime(self):
        """测试根据MIME类型获取扩展名"""
        # 测试DOCX
        ext = get_file_extension_from_mime(
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        assert ext == ".docx"

        # 测试PDF
        ext = get_file_extension_from_mime("application/pdf")
        assert ext == ".pdf"

        # 测试未知类型
        ext = get_file_extension_from_mime("unknown/type")
        assert ext is None

    def test_sanitize_filename(self):
        """测试文件名清理"""
        # 测试包含不安全字符的文件名
        unsafe_name = 'test<>:"|?*file.pdf'
        safe_name = sanitize_filename(unsafe_name)

        assert "<" not in safe_name
        assert ">" not in safe_name
        assert ":" not in safe_name
        assert '"' not in safe_name
        assert "|" not in safe_name
        assert "?" not in safe_name
        assert "*" not in safe_name
        assert safe_name == "test________file.pdf"

    def test_sanitize_filename_empty(self):
        """测试空文件名清理"""
        safe_name = sanitize_filename("")
        assert safe_name == "document"

    def test_sanitize_filename_too_long(self):
        """测试过长文件名清理"""
        long_name = "a" * 300 + ".pdf"
        safe_name = sanitize_filename(long_name)

        assert len(safe_name) <= 255
        assert safe_name.endswith(".pdf")

    def test_create_temp_file(self):
        """测试创建临时文件"""
        content = b"test content"
        temp_path = create_temp_file(content, ".txt")

        try:
            assert os.path.exists(temp_path)
            assert temp_path.endswith(".txt")

            # 验证文件内容
            with open(temp_path, "rb") as f:
                assert f.read() == content
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_cleanup_temp_file(self):
        """测试清理临时文件"""
        # 创建临时文件
        content = b"test content"
        temp_path = create_temp_file(content)

        assert os.path.exists(temp_path)

        # 清理文件
        result = cleanup_temp_file(temp_path)

        assert result is True
        assert not os.path.exists(temp_path)

    def test_cleanup_temp_file_not_exists(self):
        """测试清理不存在的文件"""
        result = cleanup_temp_file("/path/to/nonexistent/file.txt")
        assert result is False

    def test_get_file_info_summary(self):
        """测试获取文件信息摘要"""
        filename = "test.pdf"
        size = 1024 * 1024  # 1MB
        content_length = 5000

        summary = get_file_info_summary(filename, size, content_length)

        assert summary["filename"] == filename
        assert summary["size_bytes"] == size
        assert summary["size_mb"] == 1.0
        assert summary["content_length"] == content_length
        assert summary["content_kb"] == 4.88  # 5000/1024 ≈ 4.88

    def test_validate_url_valid(self):
        """测试有效URL验证"""
        valid_urls = [
            "http://example.com",
            "https://example.com/path",
            "ftp://example.com/file.txt",
            "http://example.com:8080/path?query=value",
        ]

        for url in valid_urls:
            assert validate_url(url) is True

    def test_validate_url_invalid(self):
        """测试无效URL验证"""
        invalid_urls = [
            "not-a-url",
            "example.com",  # 缺少协议
            "http://",  # 缺少域名
            "",  # 空字符串
            "file:///local/path",  # 本地文件路径（根据需求可能无效）
        ]

        for url in invalid_urls:
            if url == "file:///local/path":
                # file:// 协议可能被认为是有效的，取决于实现
                continue
            assert validate_url(url) is False

    def test_get_content_preview_short(self):
        """测试内容预览 - 短内容"""
        content = "这是一个短内容"
        preview = get_content_preview(content, 100)

        assert preview == content  # 短内容应该完整返回

    def test_get_content_preview_long(self):
        """测试内容预览 - 长内容"""
        content = "这是一个很长的内容 " * 20  # 创建长内容
        preview = get_content_preview(content, 50)

        assert len(preview) <= 53  # 50 + "..." = 53
        assert preview.endswith("...")
        assert preview.startswith("这是一个很长的内容")

    def test_get_content_preview_word_boundary(self):
        """测试内容预览 - 单词边界"""
        content = "这是 一个 测试 内容 用于 验证 单词 边界 截断 功能"
        preview = get_content_preview(content, 20)

        assert preview.endswith("...")
        # 应该在合理的位置截断，不会在单词中间
        assert len(preview) <= 23  # 20 + "..."

    def test_estimate_processing_time_pdf(self):
        """测试PDF处理时间估算"""
        file_size = 2 * 1024 * 1024  # 2MB
        time_estimate = estimate_processing_time(file_size, ".pdf")

        # PDF处理时间 = 1.0 + 2 * 2.0 = 5.0秒
        assert time_estimate == 5.0

    def test_estimate_processing_time_docx(self):
        """测试DOCX处理时间估算"""
        file_size = 1024 * 1024  # 1MB
        time_estimate = estimate_processing_time(file_size, ".docx")

        # DOCX处理时间 = 1.0 + 1 * 1.0 = 2.0秒
        assert time_estimate == 2.0

    def test_estimate_processing_time_other(self):
        """测试其他文件类型处理时间估算"""
        file_size = 1024 * 1024  # 1MB
        time_estimate = estimate_processing_time(file_size, ".txt")

        # 其他文件处理时间 = 1.0 + 1 * 1.5 = 2.5秒
        assert time_estimate == 2.5
