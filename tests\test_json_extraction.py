#!/usr/bin/env python3
"""
测试JSON提取功能
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_json_extraction():
    """测试JSON提取功能"""
    print("🔍 测试JSON提取功能...")
    
    try:
        from app.services.ai_model_service import AIModelService
        
        ai_service = AIModelService()
        
        # 模拟AI模型的完整响应（基于实际日志）
        ai_response = '''```json
{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件在"投标人须知前附表"中规定了投标人提出问题、要求澄清招标文件的截止时间及方式为"以书面方式通过安天智采招标采购电子交易平台（https://www.xinecai.com/）提交"，但在"招标公告"的联系方式中提供了电子邮件地址。虽然电子交易平台是合规的提交方式，但规则要求"邮箱应明确且与招标公告一致"，此处未明确是否接受通过邮箱提交答疑，可能引起歧义。",
      "originalArr": [
        "第一章 招标公告 -> 八、联系方式 -> 电子邮件：<EMAIL>",
        "第二章 投标人须知前附表 -> 15. 投标人提出问题、要求澄清招标文件的截止时间及方式"
      ],
      "point": "答疑机制的明确性与一致性",
      "advice": "建议在"投标人须知前附表"中明确答疑提交方式，如果只接受平台提交，则无需在答疑机制中提及邮箱；如果接受邮箱提交，则需明确邮箱地址和提交要求。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第三章"投标人须知"第2.5条中关于"近X年内"的定义使用了占位符"X"，未明确具体的年限。",
      "originalArr": [
        "第三章 投标人须知 -> 2.5 近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。"
      ],
      "point": "文本准确性与完整性",
      "advice": "将"X"替换为具体的数字，例如"近3年内"或"追溯3年"，以确保条款的明确性。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "本项目类别明确为"政府采购"，但在第三章"投标人须知"第1.2条中，主要依据的法律法规仅提及《中华人民共和国招标投标法》和《中华人民共和国招标投标法实施条例》，未明确提及《中华人民共和国政府采购法》及其相关规定。对于政府采购项目，应优先或同时明确依据《政府采购法》。",
      "originalArr": [
        "项目类别：政府采购",
        "第三章 投标人须知 -> 1.2 根据《中华人民共和国招标投标法》、《中华人民共和国招标投标法实施条例》等有关法律、法规和规章的规定，本招标项目已具备招标条件，现进行招标。"
      ],
      "point": "法律依据的准确性与完整性",
      "advice": "在法律依据中补充《中华人民共和国政府采购法》及其相关实施条例和规章，以确保项目法律依据的完整性和准确性，符合政府采购项目的性质。"
    }
  ]
}
```'''
        
        print("原始AI响应长度:", len(ai_response))
        
        # 测试JSON提取和清理
        cleaned_json = ai_service.clean_json_data(ai_response)
        
        print("清理后JSON长度:", len(cleaned_json))
        print("清理后JSON前200字符:", cleaned_json[:200])
        
        # 尝试解析
        try:
            parsed = json.loads(cleaned_json)
            print(f"\n✅ JSON解析成功!")
            print(f"   检查结果数量: {len(parsed.get('checkResultArr', []))}")
            
            if parsed.get('checkResultArr'):
                for i, item in enumerate(parsed['checkResultArr'][:3]):
                    print(f"   问题{i+1}: {item.get('quesType')} - {item.get('quesDesc', '')[:50]}...")
                
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"   错误位置: 第{e.lineno}行，第{e.colno}列")
            print(f"   错误附近内容: {cleaned_json[max(0, e.pos-50):e.pos+50]}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bracket_matching():
    """测试括号匹配逻辑"""
    print("\n🔍 测试括号匹配逻辑...")
    
    test_cases = [
        '{"a": {"b": "c"}}',  # 简单嵌套
        '{"checkResultArr": [{"quesType": "test"}]}',  # 数组嵌套
        '{"a": "b"} extra text',  # 后面有额外文本
        '{"incomplete": "json"',  # 不完整的JSON
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例{i+1}: {test_case[:30]}...")
        
        # 模拟括号匹配逻辑
        start_idx = test_case.find("{")
        if start_idx == -1:
            print("   ❌ 未找到开始括号")
            continue
            
        brace_count = 0
        end_idx = -1
        
        for j in range(start_idx, len(test_case)):
            char = test_case[j]
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = j
                    break
        
        if end_idx == -1:
            print("   ❌ 未找到匹配的结束括号")
        else:
            extracted = test_case[start_idx:end_idx+1]
            print(f"   ✅ 提取成功: {extracted}")
            
            try:
                json.loads(extracted)
                print("   ✅ JSON有效")
            except:
                print("   ❌ JSON无效")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试JSON提取功能...")
    
    success = True
    success &= test_json_extraction()
    success &= test_bracket_matching()
    
    if success:
        print("\n🎉 JSON提取功能测试通过!")
        print("现在应该能正确提取和解析AI模型返回的JSON了。")
    else:
        print("\n❌ JSON提取功能仍有问题")
        sys.exit(1)
