#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试脚本
提供完整的测试套件执行和报告生成功能
"""

import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


class TestRunner:
    """测试运行器"""

    def __init__(self, project_root: str):
        """初始化测试运行器"""
        self.project_root = Path(project_root)
        self.test_results = {}
        self.start_time = None
        self.end_time = None

    def run_command(self, command: List[str], cwd: Optional[str] = None) -> Dict:
        """运行命令并返回结果"""
        if cwd is None:
            cwd = self.project_root

        print(f"执行命令: {' '.join(command)}")
        print(f"工作目录: {cwd}")

        start_time = time.time()
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
            )
            end_time = time.time()

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": end_time - start_time,
                "command": " ".join(command),
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": "Command timed out after 300 seconds",
                "duration": 300,
                "command": " ".join(command),
            }
        except Exception as e:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "duration": 0,
                "command": " ".join(command),
            }

    def run_unit_tests(self, verbose: bool = False) -> Dict:
        """运行单元测试"""
        print("\n" + "=" * 60)
        print("运行单元测试")
        print("=" * 60)

        command = ["python", "-m", "pytest", "tests/", "--tb=short"]
        if verbose:
            command.append("-v")

        result = self.run_command(command)
        self.test_results["unit_tests"] = result

        if result["success"]:
            print("✅ 单元测试通过")
        else:
            print("❌ 单元测试失败")
            print(f"错误输出: {result['stderr']}")

        return result

    def run_integration_tests(self, verbose: bool = False) -> Dict:
        """运行集成测试"""
        print("\n" + "=" * 60)
        print("运行集成测试")
        print("=" * 60)

        command = [
            "python",
            "-m",
            "pytest",
            "tests/test_compliance_service_integration.py",
            "tests/test_performance_optimization.py",
            "--tb=short",
        ]
        if verbose:
            command.append("-v")

        result = self.run_command(command)
        self.test_results["integration_tests"] = result

        if result["success"]:
            print("✅ 集成测试通过")
        else:
            print("❌ 集成测试失败")
            print(f"错误输出: {result['stderr']}")

        return result

    def run_performance_tests(self, verbose: bool = False) -> Dict:
        """运行性能测试"""
        print("\n" + "=" * 60)
        print("运行性能测试")
        print("=" * 60)

        command = [
            "python",
            "-m",
            "pytest",
            "tests/test_load_performance.py",
            "--tb=short",
            "-s",
        ]
        if verbose:
            command.append("-v")

        result = self.run_command(command)
        self.test_results["performance_tests"] = result

        if result["success"]:
            print("✅ 性能测试通过")
        else:
            print("❌ 性能测试失败")
            print(f"错误输出: {result['stderr']}")

        return result

    def run_coverage_analysis(self) -> Dict:
        """运行测试覆盖率分析"""
        print("\n" + "=" * 60)
        print("运行测试覆盖率分析")
        print("=" * 60)

        # 检查是否安装了pytest-cov
        check_cov = self.run_command(["python", "-c", "import pytest_cov"])
        if not check_cov["success"]:
            print("⚠️  pytest-cov未安装，跳过覆盖率分析")
            return {
                "success": False,
                "message": "pytest-cov not installed",
                "coverage_percentage": 0,
            }

        command = [
            "python",
            "-m",
            "pytest",
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=json:coverage.json",
            "tests/",
            "--tb=no",
            "-q",
        ]

        result = self.run_command(command)
        self.test_results["coverage"] = result

        # 解析覆盖率结果
        coverage_file = self.project_root / "coverage.json"
        coverage_percentage = 0

        if coverage_file.exists():
            try:
                with open(coverage_file, "r") as f:
                    coverage_data = json.load(f)
                    coverage_percentage = coverage_data.get("totals", {}).get(
                        "percent_covered", 0
                    )
            except Exception as e:
                print(f"解析覆盖率文件失败: {e}")

        result["coverage_percentage"] = coverage_percentage

        if result["success"]:
            print(f"✅ 测试覆盖率: {coverage_percentage:.1f}%")
            if coverage_percentage >= 85:
                print("🎉 覆盖率达到目标要求 (≥85%)")
            else:
                print("⚠️  覆盖率未达到目标要求 (≥85%)")
        else:
            print("❌ 覆盖率分析失败")

        return result

    def run_linting(self) -> Dict:
        """运行代码质量检查"""
        print("\n" + "=" * 60)
        print("运行代码质量检查")
        print("=" * 60)

        results = {}

        # 检查是否安装了flake8
        check_flake8 = self.run_command(["python", "-c", "import flake8"])
        if check_flake8["success"]:
            print("运行 flake8 检查...")
            flake8_result = self.run_command(
                [
                    "python",
                    "-m",
                    "flake8",
                    "app/",
                    "--max-line-length=88",
                    "--ignore=E203,W503",
                ]
            )
            results["flake8"] = flake8_result

            if flake8_result["success"]:
                print("✅ flake8 检查通过")
            else:
                print("❌ flake8 检查发现问题")
                print(flake8_result["stdout"])
        else:
            print("⚠️  flake8未安装，跳过检查")

        # 检查是否安装了black
        check_black = self.run_command(["python", "-c", "import black"])
        if check_black["success"]:
            print("运行 black 格式检查...")
            black_result = self.run_command(
                ["python", "-m", "black", "--check", "--diff", "app/"]
            )
            results["black"] = black_result

            if black_result["success"]:
                print("✅ black 格式检查通过")
            else:
                print("❌ black 格式检查发现问题")
                print(black_result["stdout"])
        else:
            print("⚠️  black未安装，跳过检查")

        self.test_results["linting"] = results
        return results

    def run_security_scan(self) -> Dict:
        """运行安全扫描"""
        print("\n" + "=" * 60)
        print("运行安全扫描")
        print("=" * 60)

        # 检查是否安装了bandit
        check_bandit = self.run_command(["python", "-c", "import bandit"])
        if not check_bandit["success"]:
            print("⚠️  bandit未安装，跳过安全扫描")
            return {"success": False, "message": "bandit not installed"}

        command = [
            "python",
            "-m",
            "bandit",
            "-r",
            "app/",
            "-f",
            "json",
            "-o",
            "bandit-report.json",
        ]

        result = self.run_command(command)
        self.test_results["security"] = result

        # 解析安全扫描结果
        report_file = self.project_root / "bandit-report.json"
        if report_file.exists():
            try:
                with open(report_file, "r") as f:
                    security_data = json.load(f)
                    high_issues = len(
                        [
                            r
                            for r in security_data.get("results", [])
                            if r.get("issue_severity") == "HIGH"
                        ]
                    )
                    medium_issues = len(
                        [
                            r
                            for r in security_data.get("results", [])
                            if r.get("issue_severity") == "MEDIUM"
                        ]
                    )

                    result["high_issues"] = high_issues
                    result["medium_issues"] = medium_issues

                    if high_issues == 0:
                        print("✅ 未发现高危安全问题")
                    else:
                        print(f"❌ 发现 {high_issues} 个高危安全问题")

                    if medium_issues > 0:
                        print(f"⚠️  发现 {medium_issues} 个中危安全问题")

            except Exception as e:
                print(f"解析安全扫描报告失败: {e}")

        return result

    def run_dependency_check(self) -> Dict:
        """检查依赖包安全性"""
        print("\n" + "=" * 60)
        print("检查依赖包安全性")
        print("=" * 60)

        # 检查是否安装了safety
        check_safety = self.run_command(["python", "-c", "import safety"])
        if not check_safety["success"]:
            print("⚠️  safety未安装，跳过依赖检查")
            return {"success": False, "message": "safety not installed"}

        command = ["python", "-m", "safety", "check", "--json"]
        result = self.run_command(command)
        self.test_results["dependency_check"] = result

        if result["success"]:
            print("✅ 依赖包安全检查通过")
        else:
            print("❌ 发现不安全的依赖包")
            try:
                vulnerabilities = json.loads(result["stdout"])
                print(f"发现 {len(vulnerabilities)} 个安全漏洞")
                for vuln in vulnerabilities[:5]:  # 只显示前5个
                    print(
                        f"  - {vuln.get('package', 'Unknown')}: {vuln.get('advisory', 'No description')}"
                    )
            except:
                print(result["stdout"])

        return result

    def generate_report(self) -> str:
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("生成测试报告")
        print("=" * 60)

        report = {
            "timestamp": datetime.now().isoformat(),
            "duration": (
                self.end_time - self.start_time
                if self.end_time and self.start_time
                else 0
            ),
            "summary": {},
            "results": self.test_results,
        }

        # 计算总体统计
        total_tests = 0
        passed_tests = 0
        failed_tests = 0

        for test_type, result in self.test_results.items():
            if isinstance(result, dict) and "success" in result:
                total_tests += 1
                if result["success"]:
                    passed_tests += 1
                else:
                    failed_tests += 1

        report["summary"] = {
            "total_test_suites": total_tests,
            "passed_test_suites": passed_tests,
            "failed_test_suites": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "coverage_percentage": self.test_results.get("coverage", {}).get(
                "coverage_percentage", 0
            ),
        }

        # 保存报告
        report_file = (
            self.project_root
            / f'test-report-{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📊 测试报告已保存: {report_file}")

        # 打印摘要
        print("\n" + "=" * 60)
        print("测试摘要")
        print("=" * 60)
        print(f"总测试套件: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {report['summary']['success_rate']:.1%}")
        if "coverage" in self.test_results:
            print(f"测试覆盖率: {report['summary']['coverage_percentage']:.1f}%")
        print(f"总耗时: {report['duration']:.1f}秒")

        return str(report_file)

    def run_all_tests(self, verbose: bool = False, skip_slow: bool = False) -> bool:
        """运行所有测试"""
        self.start_time = time.time()

        print("🚀 开始运行完整测试套件")
        print(f"项目根目录: {self.project_root}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        all_passed = True

        try:
            # 1. 运行单元测试
            unit_result = self.run_unit_tests(verbose)
            if not unit_result["success"]:
                all_passed = False

            # 2. 运行集成测试
            integration_result = self.run_integration_tests(verbose)
            if not integration_result["success"]:
                all_passed = False

            # 3. 运行性能测试（可选跳过）
            if not skip_slow:
                perf_result = self.run_performance_tests(verbose)
                if not perf_result["success"]:
                    all_passed = False

            # 4. 运行覆盖率分析
            self.run_coverage_analysis()

            # 5. 运行代码质量检查
            self.run_linting()

            # 6. 运行安全扫描（可选跳过）
            if not skip_slow:
                self.run_security_scan()
                self.run_dependency_check()

        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
            all_passed = False
        except Exception as e:
            print(f"\n❌ 测试过程中发生异常: {e}")
            all_passed = False

        self.end_time = time.time()

        # 生成报告
        report_file = self.generate_report()

        if all_passed:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 部分测试失败，请查看详细报告")

        return all_passed


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自动化测试脚本")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--skip-slow", action="store_true", help="跳过耗时的测试")
    parser.add_argument("--unit-only", action="store_true", help="只运行单元测试")
    parser.add_argument(
        "--integration-only", action="store_true", help="只运行集成测试"
    )
    parser.add_argument(
        "--performance-only", action="store_true", help="只运行性能测试"
    )
    parser.add_argument("--coverage-only", action="store_true", help="只运行覆盖率分析")
    parser.add_argument("--project-root", default=".", help="项目根目录")

    args = parser.parse_args()

    # 确定项目根目录
    project_root = os.path.abspath(args.project_root)
    if not os.path.exists(os.path.join(project_root, "main.py")):
        print(f"❌ 项目根目录无效: {project_root}")
        print("请确保在项目根目录运行此脚本，或使用 --project-root 参数指定正确路径")
        sys.exit(1)

    runner = TestRunner(project_root)

    try:
        if args.unit_only:
            result = runner.run_unit_tests(args.verbose)
            sys.exit(0 if result["success"] else 1)
        elif args.integration_only:
            result = runner.run_integration_tests(args.verbose)
            sys.exit(0 if result["success"] else 1)
        elif args.performance_only:
            result = runner.run_performance_tests(args.verbose)
            sys.exit(0 if result["success"] else 1)
        elif args.coverage_only:
            result = runner.run_coverage_analysis()
            sys.exit(0 if result["success"] else 1)
        else:
            # 运行所有测试
            success = runner.run_all_tests(args.verbose, args.skip_slow)
            sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(130)


if __name__ == "__main__":
    main()
