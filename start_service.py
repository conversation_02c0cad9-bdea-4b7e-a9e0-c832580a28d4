#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的服务启动脚本
"""

import uvicorn
import sys
import os


def find_available_port(start_port=8088, max_attempts=10):
    """查找可用端口"""
    import socket

    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return port
        except OSError:
            continue

    return None


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 服务启动")
    print("=" * 50)

    try:
        # 检查主应用是否可以导入
        print("检查应用导入...")
        from main import app

        print("✅ 应用导入成功")

        # 查找可用端口
        print("查找可用端口...")
        port = find_available_port()
        if not port:
            print("❌ 无法找到可用端口")
            return 1

        print(f"✅ 使用端口: {port}")

        # 启动服务
        print(f"\n🚀 启动服务...")
        print(f"访问地址: http://localhost:{port}")
        print(f"API文档: http://localhost:{port}/docs")
        print(f"健康检查: http://localhost:{port}/health")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)

        # 启动uvicorn服务器
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=port,
            reload=False,  # 生产模式，不自动重载
            log_level="info",
        )

    except KeyboardInterrupt:
        print("\n\n服务已停止")
        return 0
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
