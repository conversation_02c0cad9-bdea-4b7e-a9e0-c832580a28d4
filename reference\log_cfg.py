# -*- coding: utf-8 -*-
from loguru import logger
import traceback
from functools import wraps
import json
import os

root_path = os.path.dirname(os.path.dirname(__file__))
# log_path = "./log/{time:YYYY_MM}/{time:YYYY-MM-DD}.log"
log_path = os.path.join(root_path, "log/{time:YYYY_MM}/{time:YYYY-MM-DD}.log")
logger.add(
    log_path,  # 日志文件路径及日志文件名格式:2023-01-30.log
    rotation="00:00",  # 每天00:00 自动生成新的日志文件
    retention="30 day",
    mode="a+",  # 保留多久的日志文件
    backtrace=True,
    diagnose=True,  # 记录完整的错误信息
    encoding="utf-8",  # 日志文件编码
    enqueue=True,  # 日志调用非阻塞
    filter=lambda record: record["extra"].get("name") == "log",
)
# 使用 loguru 库的 bind 方法为日志记录器绑定一个上下文（context），也就是给这个日志记录器实例添加一些固定的属性
log = logger.bind(name="log")


# 定义装饰器error_handler
def error_handler(func):
    @wraps(
        func
    )  # 消除装饰器对原函数造成的影响，即对原函数的相关属性进行拷贝，达到装饰器不修改原函数的目的。
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except:
            msg = traceback.format_exc()
            log.error(msg)
            dic_error = {"status": "failure", "msg": msg}
            return json.dumps(dic_error, ensure_ascii=False)

    return wrapper  # 返回包装函数
    """
    返回经过装饰的函数，保留原始函数的元数据
    
    通过functools.wraps装饰器，确保包装函数继承原函数的：
    - 名称(__name__)
    - 文档字符串(__doc__)
    - 参数列表等元信息
    """
