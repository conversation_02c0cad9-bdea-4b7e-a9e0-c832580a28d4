# -*- coding: utf-8 -*-
"""
异常处理和错误管理
基于参考代码log_cfg.py的error_handler装饰器模式
"""

import traceback
import json
from typing import Dict, Any, Optional, Union
from datetime import datetime
from functools import wraps
from fastapi import HTTP<PERSON>xception, Request, status
from fastapi.responses import JSONResponse

from app.core.logger import log
from app.models.schemas import ErrorResponse


class BaseComplianceError(Exception):
    """基础合规检查异常类"""

    def __init__(
        self,
        message: str,
        error_code: str = "",
        details: Dict[str, Any] = None,
        status_code: int = 500,
        original_error: Exception = None,
    ):
        """
        初始化基础异常

        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
            status_code: HTTP状态码
            original_error: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.status_code = status_code
        self.original_error = original_error
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "original_error": str(self.original_error) if self.original_error else None,
        }

    def to_response(self) -> ErrorResponse:
        """转换为错误响应格式"""
        return ErrorResponse(
            success=False,
            message=self.message,
            error_code=self.error_code,
            timestamp=self.timestamp,
        )


class ValidationError(BaseComplianceError):
    """参数验证异常"""

    def __init__(self, message: str, field: str = "", value: Any = None, **kwargs):
        super().__init__(message, status_code=400, **kwargs)
        self.field = field
        self.value = value
        if field:
            self.details.update(
                {"field": field, "value": str(value) if value is not None else None}
            )


class FileProcessingError(BaseComplianceError):
    """文件处理异常"""

    def __init__(
        self,
        message: str,
        filename: str = "",
        file_size: int = None,
        file_type: str = "",
        **kwargs,
    ):
        super().__init__(message, status_code=422, **kwargs)
        self.filename = filename
        self.file_size = file_size
        self.file_type = file_type
        if filename:
            self.details.update(
                {"filename": filename, "file_size": file_size, "file_type": file_type}
            )


class AIModelError(BaseComplianceError):
    """AI模型调用异常"""

    def __init__(
        self,
        message: str,
        model_name: str = "",
        request_tokens: int = None,
        response_tokens: int = None,
        **kwargs,
    ):
        super().__init__(message, status_code=502, **kwargs)
        self.model_name = model_name
        self.request_tokens = request_tokens
        self.response_tokens = response_tokens
        if model_name:
            self.details.update(
                {
                    "model_name": model_name,
                    "request_tokens": request_tokens,
                    "response_tokens": response_tokens,
                }
            )


class ExternalServiceError(BaseComplianceError):
    """外部服务调用异常"""

    def __init__(
        self,
        message: str,
        service_name: str = "",
        service_url: str = "",
        response_status: int = None,
        **kwargs,
    ):
        super().__init__(message, status_code=503, **kwargs)
        self.service_name = service_name
        self.service_url = service_url
        self.response_status = response_status
        if service_name:
            self.details.update(
                {
                    "service_name": service_name,
                    "service_url": service_url,
                    "response_status": response_status,
                }
            )


class BusinessLogicError(BaseComplianceError):
    """业务逻辑异常"""

    def __init__(
        self,
        message: str,
        operation: str = "",
        context: Dict[str, Any] = None,
        **kwargs,
    ):
        super().__init__(message, status_code=422, **kwargs)
        self.operation = operation
        self.context = context or {}
        if operation:
            self.details.update({"operation": operation, "context": self.context})


class ConfigurationError(BaseComplianceError):
    """配置异常"""

    def __init__(
        self, message: str, config_key: str = "", config_value: Any = None, **kwargs
    ):
        super().__init__(message, status_code=500, **kwargs)
        self.config_key = config_key
        self.config_value = config_value
        if config_key:
            self.details.update(
                {
                    "config_key": config_key,
                    "config_value": (
                        str(config_value) if config_value is not None else None
                    ),
                }
            )


class RateLimitError(BaseComplianceError):
    """限流异常"""

    def __init__(
        self,
        message: str,
        limit: int = None,
        window: int = None,
        retry_after: int = None,
        **kwargs,
    ):
        super().__init__(message, status_code=429, **kwargs)
        self.limit = limit
        self.window = window
        self.retry_after = retry_after
        if limit:
            self.details.update(
                {"limit": limit, "window": window, "retry_after": retry_after}
            )


class ErrorHandler:
    """错误处理器类"""

    @staticmethod
    def handle_validation_error(
        error: ValidationError, request_id: str = ""
    ) -> JSONResponse:
        """处理验证错误"""
        log.warning(
            f"参数验证失败 | ID: {request_id} | 字段: {error.field} | 值: {error.value} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": error.message,
                "data": {
                    "error_code": error.error_code,
                    "details": error.details,
                    "timestamp": error.timestamp.isoformat(),
                },
            },
        )

    @staticmethod
    def handle_file_processing_error(
        error: FileProcessingError, request_id: str = ""
    ) -> JSONResponse:
        """处理文件处理错误"""
        log.error(
            f"文件处理失败 | ID: {request_id} | 文件: {error.filename} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "文件处理失败，请检查文件格式和大小",
                "data": {
                    "error_code": error.error_code,
                    "details": {
                        "filename": error.filename,
                        "file_type": error.file_type,
                    },
                    "timestamp": error.timestamp.isoformat(),
                },
            },
        )

    @staticmethod
    def handle_ai_model_error(
        error: AIModelError, request_id: str = ""
    ) -> JSONResponse:
        """处理AI模型错误"""
        log.error(
            f"AI模型调用失败 | ID: {request_id} | 模型: {error.model_name} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "code": error.status_code,
                "message": "AI模型服务暂时不可用，请稍后重试",
                "data": {
                    "error_code": error.error_code,
                    "details": {"model_name": error.model_name},
                    "timestamp": error.timestamp.isoformat(),
                },
            },
        )

    @staticmethod
    def handle_external_service_error(
        error: ExternalServiceError, request_id: str = ""
    ) -> JSONResponse:
        """处理外部服务错误"""
        log.error(
            f"外部服务调用失败 | ID: {request_id} | 服务: {error.service_name} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "success": False,
                "message": "外部服务暂时不可用，请稍后重试",
                "error_code": error.error_code,
                "details": {"service_name": error.service_name},
                "timestamp": error.timestamp.isoformat(),
            },
        )

    @staticmethod
    def handle_business_logic_error(
        error: BusinessLogicError, request_id: str = ""
    ) -> JSONResponse:
        """处理业务逻辑错误"""
        log.warning(
            f"业务逻辑错误 | ID: {request_id} | 操作: {error.operation} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "success": False,
                "message": error.message,
                "error_code": error.error_code,
                "details": error.details,
                "timestamp": error.timestamp.isoformat(),
            },
        )

    @staticmethod
    def handle_configuration_error(
        error: ConfigurationError, request_id: str = ""
    ) -> JSONResponse:
        """处理配置错误"""
        log.error(
            f"配置错误 | ID: {request_id} | 配置项: {error.config_key} | 错误: {error.message}"
        )

        return JSONResponse(
            status_code=error.status_code,
            content={
                "success": False,
                "message": "系统配置错误，请联系管理员",
                "error_code": error.error_code,
                "timestamp": error.timestamp.isoformat(),
            },
        )

    @staticmethod
    def handle_rate_limit_error(
        error: RateLimitError, request_id: str = ""
    ) -> JSONResponse:
        """处理限流错误"""
        log.warning(
            f"请求限流 | ID: {request_id} | 限制: {error.limit} | 错误: {error.message}"
        )

        headers = {}
        if error.retry_after:
            headers["Retry-After"] = str(error.retry_after)

        return JSONResponse(
            status_code=error.status_code,
            content={
                "success": False,
                "message": "请求过于频繁，请稍后重试",
                "error_code": error.error_code,
                "details": {"retry_after": error.retry_after},
                "timestamp": error.timestamp.isoformat(),
            },
            headers=headers,
        )

    @staticmethod
    def handle_generic_error(error: Exception, request_id: str = "") -> JSONResponse:
        """处理通用错误"""
        try:
            # 简化错误日志，避免使用可能阻塞的traceback.format_exc()
            log.error(
                f"未处理异常 | ID: {request_id} | 错误: {str(error)} | 类型: {type(error).__name__}"
            )
        except Exception:
            # 如果日志记录失败，继续执行
            pass

        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": "系统内部错误，请稍后重试",
                "data": {
                    "error_code": "INTERNAL_SERVER_ERROR",
                    "timestamp": "2025-08-06",
                },
            },
        )


def compliance_error_handler(func):
    """
    合规检查错误处理装饰器
    基于参考代码log_cfg.py的error_handler模式
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 记录完整的错误堆栈
            error_msg = traceback.format_exc()
            log.error(f"函数 {func.__name__} 执行失败: {error_msg}")

            # 返回标准化的错误响应
            error_response = {
                "status": "failure",
                "message": str(e),
                "function": func.__name__,
                "timestamp": datetime.now().isoformat(),
            }
            return json.dumps(error_response, ensure_ascii=False)

    return wrapper


def async_compliance_error_handler(func):
    """
    异步合规检查错误处理装饰器
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # 记录完整的错误堆栈
            error_msg = traceback.format_exc()
            log.error(f"异步函数 {func.__name__} 执行失败: {error_msg}")

            # 返回标准化的错误响应
            error_response = {
                "status": "failure",
                "message": str(e),
                "function": func.__name__,
                "timestamp": datetime.now().isoformat(),
            }
            return error_response

    return wrapper


def api_error_handler(func):
    """
    API错误处理装饰器，用于FastAPI路由
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        request_id = ""

        # 尝试从参数中获取request对象和request_id
        try:
            for arg in args:
                if isinstance(arg, Request):
                    request_id = getattr(arg.state, "request_id", "")
                    break
        except Exception:
            # 如果获取request_id失败，继续执行
            pass

        try:
            return await func(*args, **kwargs)
        except BaseComplianceError as e:
            # 处理自定义异常
            handler = ErrorHandler()

            if isinstance(e, ValidationError):
                return handler.handle_validation_error(e, request_id)
            elif isinstance(e, FileProcessingError):
                return handler.handle_file_processing_error(e, request_id)
            elif isinstance(e, AIModelError):
                return handler.handle_ai_model_error(e, request_id)
            elif isinstance(e, ExternalServiceError):
                return handler.handle_external_service_error(e, request_id)
            elif isinstance(e, BusinessLogicError):
                return handler.handle_business_logic_error(e, request_id)
            elif isinstance(e, ConfigurationError):
                return handler.handle_configuration_error(e, request_id)
            elif isinstance(e, RateLimitError):
                return handler.handle_rate_limit_error(e, request_id)
            else:
                return handler.handle_generic_error(e, request_id)

        except HTTPException as e:
            # 处理FastAPI HTTP异常
            try:
                log.warning(
                    f"HTTP异常 | ID: {request_id} | 状态码: {e.status_code} | 详情: {e.detail}"
                )
            except Exception:
                # 如果日志记录失败，继续执行
                pass
            raise e

        except Exception as e:
            # 处理未知异常 - 简化版本，避免阻塞
            try:
                log.error(f"未处理异常 | ID: {request_id} | 错误: {str(e)}")
            except Exception:
                # 如果日志记录失败，继续执行
                pass

            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": "系统内部错误，请稍后重试",
                    "error_code": "INTERNAL_SERVER_ERROR",
                    "timestamp": "2025-08-06",
                },
            )

    return wrapper


class FallbackManager:
    """降级管理器"""

    @staticmethod
    def create_fallback_response(operation: str, reason: str = "") -> Dict[str, Any]:
        """
        创建降级响应

        Args:
            operation: 操作名称
            reason: 降级原因

        Returns:
            Dict[str, Any]: 降级响应
        """
        log.warning(f"操作降级: {operation} | 原因: {reason}")

        return {
            "sensitiveWordsArr": [],
            "checkResultArr": [],
            "fallback": True,
            "fallback_reason": reason,
            "message": f"由于{reason}，{operation}使用了降级策略",
        }

    @staticmethod
    def should_fallback(
        error: Exception, max_retries: int = 3, current_retry: int = 0
    ) -> bool:
        """
        判断是否应该降级

        Args:
            error: 异常对象
            max_retries: 最大重试次数
            current_retry: 当前重试次数

        Returns:
            bool: 是否应该降级
        """
        # 如果是配置错误或验证错误，不进行降级
        if isinstance(error, (ConfigurationError, ValidationError)):
            return False

        # 如果重试次数已达上限，进行降级
        if current_retry >= max_retries:
            return True

        # 如果是外部服务错误或AI模型错误，可以降级
        if isinstance(error, (ExternalServiceError, AIModelError)):
            return True

        return False

    @staticmethod
    def get_fallback_strategy(error: Exception) -> str:
        """
        获取降级策略

        Args:
            error: 异常对象

        Returns:
            str: 降级策略描述
        """
        if isinstance(error, AIModelError):
            return "AI模型服务不可用，跳过合规性检查"
        elif isinstance(error, ExternalServiceError):
            return "外部服务不可用，跳过相关检查"
        elif isinstance(error, FileProcessingError):
            return "文件处理失败，无法进行检查"
        else:
            return "系统异常，使用默认响应"


# 创建全局错误处理器实例
error_handler = ErrorHandler()
fallback_manager = FallbackManager()
