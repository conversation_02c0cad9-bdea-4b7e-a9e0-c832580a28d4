#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最小化端点
"""

import requests
import json
import time


def test_minimal_validate():
    """测试最小化验证端点"""
    print("测试最小化验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    test_data = {"filename": "test.docx", "size": 1024}

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/debug/minimal-validate", json=test_data, timeout=5
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 200:
            print("✅ 最小化验证成功")
            return True
        else:
            print("❌ 最小化验证失败")
            return False

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 最小化验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_original_validate():
    """测试原始验证端点"""
    print("\n测试原始验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info_data, timeout=5
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text[:200]}...")

        if response.status_code in [200, 400, 500]:  # 任何响应都说明没有超时
            print("✅ 原始验证有响应（没有超时）")
            return True
        else:
            print("❌ 原始验证失败")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 原始验证超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 原始验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def main():
    """主函数"""
    print("最小化端点对比测试")
    print("=" * 60)

    # 测试最小化端点
    minimal_ok = test_minimal_validate()

    # 测试原始端点
    original_ok = test_original_validate()

    print(f"\n{'='*60}")
    print("对比结果")
    print("=" * 60)

    if minimal_ok and original_ok:
        print("✅ 两个端点都正常，装饰器问题已解决")
    elif minimal_ok and not original_ok:
        print("🔍 最小化端点正常，原始端点有问题")
        print("   问题可能在于:")
        print("   1. FileInfo模型的创建或验证")
        print("   2. file_processor组件的调用")
        print("   3. 某个导入或初始化过程")
    elif not minimal_ok:
        print("❌ 连最小化端点都有问题")
        print("   问题可能在于:")
        print("   1. FastAPI的基础配置")
        print("   2. 中间件的处理")
        print("   3. 异步处理机制")


if __name__ == "__main__":
    main()
