#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化API测试 - 仅测试API接口，不依赖内部模块
"""

import requests
import json


def test_simple_api_with_mock_url():
    """使用模拟URL测试简化API"""

    BASE_URL = "http://localhost:8088"

    # 使用一个简单的测试URL
    test_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "https://httpbin.org/status/200",  # 使用httpbin作为测试URL
    }

    print("=" * 60)
    print("测试简化合规性检查API")
    print("=" * 60)

    try:
        print(f"发送请求到: {BASE_URL}/api/v1/check-compliance-simple")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

        response = requests.post(
            f"{BASE_URL}/api/v1/check-compliance-simple", json=test_data, timeout=60
        )

        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ 请求成功!")
            print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"\n❌ 请求失败!")
            print(f"错误响应: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


def test_health_check():
    """测试健康检查接口"""

    BASE_URL = "http://localhost:8088"

    print("\n" + "=" * 60)
    print("测试健康检查接口")
    print("=" * 60)

    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 健康检查通过!")
            print(f"服务状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ 健康检查失败!")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False


def test_enums_endpoint():
    """测试枚举值接口"""

    BASE_URL = "http://localhost:8088"

    print("\n" + "=" * 60)
    print("测试枚举值接口")
    print("=" * 60)

    try:
        response = requests.get(f"{BASE_URL}/api/v1/enums", timeout=10)

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 枚举值获取成功!")
            print(f"枚举值: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ 枚举值获取失败!")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 枚举值测试异常: {str(e)}")
        return False


def test_parameter_validation():
    """测试参数验证"""

    BASE_URL = "http://localhost:8088"

    print("\n" + "=" * 60)
    print("测试参数验证")
    print("=" * 60)

    # 测试无效参数
    invalid_data = {
        "procurement_project_type": "无效类型",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "http://example.com/test.docx",
    }

    try:
        print(f"发送无效参数: {json.dumps(invalid_data, ensure_ascii=False, indent=2)}")

        response = requests.post(
            f"{BASE_URL}/api/v1/check-compliance-simple", json=invalid_data, timeout=30
        )

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 422:
            print(f"✅ 参数验证正常工作!")
            error_detail = response.json()
            print(
                f"验证错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}"
            )
            return True
        else:
            print(f"❌ 参数验证异常!")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 参数验证测试异常: {str(e)}")
        return False


def main():
    """运行所有测试"""

    print("🚀 开始运行简化API测试")

    tests = [
        ("健康检查", test_health_check),
        ("枚举值接口", test_enums_endpoint),
        ("参数验证", test_parameter_validation),
        ("简化API接口", test_simple_api_with_mock_url),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n总计: {len(results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    print(f"成功率: {passed/len(results)*100:.1f}%")

    if failed == 0:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查服务状态")

    return failed == 0


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
