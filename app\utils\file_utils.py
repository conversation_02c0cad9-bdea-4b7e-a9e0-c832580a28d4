# -*- coding: utf-8 -*-
"""
文件处理工具函数
"""

import os
import tempfile
import hashlib
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from app.core.logger import log


def get_file_hash(content: bytes, algorithm: str = "md5") -> str:
    """
    计算文件内容的哈希值

    Args:
        content: 文件内容
        algorithm: 哈希算法 (md5, sha1, sha256)

    Returns:
        str: 哈希值
    """
    try:
        if algorithm == "md5":
            hash_obj = hashlib.md5()
        elif algorithm == "sha1":
            hash_obj = hashlib.sha1()
        elif algorithm == "sha256":
            hash_obj = hashlib.sha256()
        else:
            raise ValueError(f"不支持的哈希算法: {algorithm}")

        hash_obj.update(content)
        return hash_obj.hexdigest()

    except Exception as e:
        log.error(f"计算文件哈希失败: {str(e)}")
        return ""


def get_filename_from_url(url: str) -> str:
    """
    从URL中提取文件名

    Args:
        url: 文件URL

    Returns:
        str: 文件名
    """
    try:
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)

        # 如果URL路径中没有文件名，使用默认名称
        if not filename or "." not in filename:
            return "document"

        return filename

    except Exception as e:
        log.error(f"从URL提取文件名失败: {str(e)}")
        return "document"


def get_file_extension_from_mime(mime_type: str) -> Optional[str]:
    """
    根据MIME类型获取文件扩展名

    Args:
        mime_type: MIME类型

    Returns:
        Optional[str]: 文件扩展名
    """
    mime_to_ext = {
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
        "application/pdf": ".pdf",
        "application/msword": ".doc",
        "text/plain": ".txt",
        "application/zip": ".zip",
        "application/x-rar-compressed": ".rar",
    }

    return mime_to_ext.get(mime_type.lower())


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符

    Args:
        filename: 原始文件名

    Returns:
        str: 清理后的文件名
    """
    # 移除或替换不安全字符
    unsafe_chars = ["<", ">", ":", '"', "|", "?", "*", "/", "\\"]
    sanitized = filename

    for char in unsafe_chars:
        sanitized = sanitized.replace(char, "_")

    # 移除前后空格和点
    sanitized = sanitized.strip(" .")

    # 确保文件名不为空
    if not sanitized:
        sanitized = "document"

    # 限制文件名长度
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        max_name_len = 255 - len(ext)
        sanitized = name[:max_name_len] + ext

    return sanitized


def create_temp_file(content: bytes, suffix: str = "") -> str:
    """
    创建临时文件

    Args:
        content: 文件内容
        suffix: 文件后缀

    Returns:
        str: 临时文件路径
    """
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            temp_file.write(content)
            temp_path = temp_file.name

        log.debug(f"创建临时文件: {temp_path}")
        return temp_path

    except Exception as e:
        log.error(f"创建临时文件失败: {str(e)}")
        raise


def cleanup_temp_file(file_path: str) -> bool:
    """
    清理临时文件

    Args:
        file_path: 文件路径

    Returns:
        bool: 是否成功删除
    """
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
            log.debug(f"删除临时文件: {file_path}")
            return True
        return False

    except Exception as e:
        log.error(f"删除临时文件失败: {str(e)}")
        return False


def get_file_info_summary(
    filename: str, size: int, content_length: int
) -> Dict[str, Any]:
    """
    获取文件信息摘要

    Args:
        filename: 文件名
        size: 文件大小（字节）
        content_length: 内容长度（字符数）

    Returns:
        Dict[str, Any]: 文件信息摘要
    """
    return {
        "filename": filename,
        "size_bytes": size,
        "size_mb": round(size / 1024 / 1024, 2),
        "content_length": content_length,
        "content_kb": round(content_length / 1024, 2),
    }


def validate_url(url: str) -> bool:
    """
    验证URL格式

    Args:
        url: URL字符串

    Returns:
        bool: URL是否有效
    """
    try:
        parsed = urlparse(url)
        return bool(parsed.scheme and parsed.netloc)
    except Exception:
        return False


def get_content_preview(content: str, max_length: int = 200) -> str:
    """
    获取内容预览

    Args:
        content: 内容文本
        max_length: 最大长度

    Returns:
        str: 内容预览
    """
    if len(content) <= max_length:
        return content

    # 尝试在单词边界截断
    truncated = content[:max_length]
    last_space = truncated.rfind(" ")

    if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
        truncated = truncated[:last_space]

    return truncated + "..."


def estimate_processing_time(file_size: int, file_type: str) -> float:
    """
    估算文件处理时间

    Args:
        file_size: 文件大小（字节）
        file_type: 文件类型

    Returns:
        float: 估算的处理时间（秒）
    """
    # 基于文件大小和类型的简单估算
    base_time = 1.0  # 基础处理时间
    size_factor = file_size / (1024 * 1024)  # MB

    if file_type == ".pdf":
        # PDF处理通常更耗时
        return base_time + size_factor * 2.0
    elif file_type == ".docx":
        # DOCX处理相对较快
        return base_time + size_factor * 1.0
    else:
        return base_time + size_factor * 1.5
