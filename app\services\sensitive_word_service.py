# -*- coding: utf-8 -*-
"""
敏感词检测服务
调用外部敏感词检测API
"""

import json
from typing import List, Optional, Dict, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.core.error_logger import error_logger
from app.models.schemas import (
    SensitiveWordItem,
    SensitiveWordRequest,
    SensitiveWordDetectionResponse,
    ProjectInfo,
)


class SensitiveWordError(Exception):
    """敏感词检测异常"""

    def __init__(
        self,
        message: str,
        api_url: str = "",
        status_code: int = None,
        response_data: str = "",
        original_error: Exception = None,
    ):
        """
        初始化敏感词检测异常

        Args:
            message: 错误消息
            api_url: API URL
            status_code: HTTP状态码
            response_data: 响应数据
            original_error: 原始异常
        """
        super().__init__(message)
        self.api_url = api_url
        self.status_code = status_code
        self.response_data = response_data
        self.original_error = original_error
        self.error_type = "SENSITIVE_WORD_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.api_url:
            details.append(f"API: {self.api_url}")
        if self.status_code:
            details.append(f"状态码: {self.status_code}")
        if self.original_error:
            details.append(f"原因: {str(self.original_error)}")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


def create_robust_session() -> requests.Session:
    """
    创建一个具有重试机制和连接池的健壮会话
    基于参考代码的create_robust_session函数
    """
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=settings.max_retries,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "POST", "OPTIONS"],
    )

    # 配置HTTP适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20,
        pool_block=False,
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


class SensitiveWordService:
    """敏感词检测服务类"""

    def __init__(self):
        """初始化敏感词检测服务"""
        self.base_url = settings.sensitive_word_api_url
        self.session = create_robust_session()
        self.detect_endpoint = f"{self.base_url}/detect"
        self.health_endpoint = f"{self.base_url}/health"
        self.stats_endpoint = f"{self.base_url}/stats"
        self.reload_endpoint = f"{self.base_url}/reload"

    @log_function_call
    def check_health(self, request_id: str = "") -> bool:
        """
        检查敏感词检测服务健康状态

        Args:
            request_id: 请求ID

        Returns:
            bool: 服务是否健康
        """
        try:
            with TimingContext("敏感词服务健康检查", request_id):
                log.info(f"检查敏感词服务健康状态: {self.health_endpoint}")

                response = self.session.get(
                    self.health_endpoint, timeout=10  # 健康检查使用较短超时
                )
                response.raise_for_status()

                result = response.json()
                is_healthy = result.get("status") == "healthy"

                log.info(f"敏感词服务健康状态: {'健康' if is_healthy else '不健康'}")
                return is_healthy

        except Exception as e:
            log.error(f"敏感词服务健康检查失败: {str(e)}")
            return False

    @log_function_call
    def get_stats(self, request_id: str = "") -> Optional[Dict[str, Any]]:
        """
        获取敏感词检测服务统计信息

        Args:
            request_id: 请求ID

        Returns:
            Optional[Dict[str, Any]]: 统计信息，失败时返回None
        """
        try:
            with TimingContext("获取敏感词服务统计", request_id):
                log.info(f"获取敏感词服务统计信息: {self.stats_endpoint}")

                response = self.session.get(self.stats_endpoint, timeout=10)
                response.raise_for_status()

                stats = response.json()
                log.info(f"获取敏感词服务统计成功: {len(str(stats))} 字符")
                return stats

        except Exception as e:
            log.error(f"获取敏感词服务统计失败: {str(e)}")
            return None

    @log_function_call
    def determine_government_procurement(self, project_info: ProjectInfo) -> bool:
        """
        根据项目信息判断是否为政府采购

        Args:
            project_info: 项目信息

        Returns:
            bool: 是否为政府采购
        """
        is_government = project_info.is_government_procurement()
        log.debug(
            f"项目类别: {project_info.project_category.value}, 是否政府采购: {is_government}"
        )
        return is_government

    @log_function_call
    def detect_sensitive_words(
        self,
        content: str,
        project_info: ProjectInfo,
        request_id: str = "",
        timeout: float = None,
    ) -> List[SensitiveWordItem]:
        """
        检测敏感词

        Args:
            content: 要检测的markdown内容
            project_info: 项目信息
            request_id: 请求ID
            timeout: 请求超时时间（秒），默认使用配置值

        Returns:
            List[SensitiveWordItem]: 敏感词列表
        """
        try:
            with TimingContext("敏感词检测", request_id):
                # 判断是否为政府采购
                is_government_procurement = self.determine_government_procurement(
                    project_info
                )

                # 构建请求数据
                request_data = SensitiveWordRequest(
                    content=content, is_government_procurement=is_government_procurement
                )

                log.info(
                    f"开始敏感词检测: 内容长度={len(content)}, 政府采购={is_government_procurement}"
                )

                # 确定超时时间
                actual_timeout = (
                    timeout if timeout is not None else settings.request_timeout
                )

                log.info(f"敏感词检测超时设置: {actual_timeout}秒 | ID: {request_id}")

                # 发送POST请求
                response = self.session.post(
                    self.detect_endpoint,
                    json=request_data.dict(),
                    headers={"Content-Type": "application/json"},
                    timeout=actual_timeout,
                )
                response.raise_for_status()

                # 解析响应
                response_data = response.json()

                # 验证响应格式
                if not isinstance(response_data, dict):
                    raise SensitiveWordError(
                        "API响应格式错误：不是有效的JSON对象",
                        api_url=self.detect_endpoint,
                        status_code=response.status_code,
                        response_data=str(response_data),
                    )

                # 检查响应状态
                if not response_data.get("success", False):
                    error_message = response_data.get("message", "未知错误")
                    raise SensitiveWordError(
                        f"敏感词检测失败: {error_message}",
                        api_url=self.detect_endpoint,
                        status_code=response.status_code,
                        response_data=str(response_data),
                    )

                # 解析检测结果
                detection_response = SensitiveWordDetectionResponse(**response_data)

                # 转换为目标格式
                sensitive_words = self._convert_to_target_format(
                    detection_response.results
                )

                # 记录API调用性能
                performance_logger.log_api_call(
                    request_id, "敏感词检测API", 0, True  # 时间由TimingContext记录
                )

                log.info(f"敏感词检测完成: 发现 {len(sensitive_words)} 个敏感词")
                return sensitive_words

        except requests.exceptions.RequestException as e:
            # 记录失败的API调用
            performance_logger.log_api_call(request_id, "敏感词检测API", 0, False)

            status_code = (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response") and e.response
                else None
            )
            response_text = (
                getattr(e.response, "text", "")
                if hasattr(e, "response") and e.response
                else ""
            )

            # 使用增强的错误日志记录
            error_logger.log_api_call_error(
                "sensitive_word_detection",
                self.detect_endpoint,
                "POST",
                e,
                request_id,
                request_data=request_data.dict(),
                response_data=response_text,
                status_code=status_code,
            )

            raise SensitiveWordError(
                f"敏感词检测API请求失败: {str(e)}",
                api_url=self.detect_endpoint,
                status_code=status_code,
                response_data=response_text,
                original_error=e,
            )
        except SensitiveWordError:
            # 重新抛出敏感词异常
            performance_logger.log_api_call(request_id, "敏感词检测API", 0, False)
            raise
        except Exception as e:
            # 包装其他异常
            performance_logger.log_api_call(request_id, "敏感词检测API", 0, False)
            raise SensitiveWordError(
                f"敏感词检测异常: {str(e)}",
                api_url=self.detect_endpoint,
                original_error=e,
            )

    def _convert_to_target_format(self, results: List[Any]) -> List[SensitiveWordItem]:
        """
        将API响应结果转换为目标格式

        Args:
            results: API返回的results数组

        Returns:
            List[SensitiveWordItem]: 转换后的敏感词列表
        """
        sensitive_words = []

        if not results:
            log.info("敏感词检测结果为空")
            return sensitive_words

        for i, result in enumerate(results):
            try:
                # 安全提取字段值
                word_type = self._safe_extract_field(
                    result, ["敏感词类型", "type", "category"], "其他"
                )
                word_content = self._safe_extract_field(
                    result, ["敏感词内容", "content", "word"], ""
                )
                word_count = self._safe_extract_field(
                    result, ["出现次数", "num", "count", "frequency"], 0
                )

                # 确保数据类型正确
                if isinstance(word_count, str):
                    try:
                        word_count = int(word_count)
                    except (ValueError, TypeError):
                        word_count = 1  # 默认出现1次

                # 创建敏感词项
                sensitive_word = SensitiveWordItem(
                    type=str(word_type).strip() if word_type else "其他",
                    content=str(word_content).strip() if word_content else "",
                    num=max(1, int(word_count)) if word_count else 1,
                )

                # 验证数据有效性
                if sensitive_word.content:
                    sensitive_words.append(sensitive_word)
                    log.debug(
                        f"成功转换敏感词 {i+1}: {sensitive_word.content} ({sensitive_word.type})"
                    )
                else:
                    log.warning(f"跳过无效的敏感词结果 {i+1}: 内容为空")

            except Exception as e:
                log.warning(f"转换敏感词结果 {i+1} 失败: {str(e)}")
                log.debug(f"原始数据: {result}")
                continue

        log.info(f"敏感词转换完成: 原始{len(results)}个，有效{len(sensitive_words)}个")
        return sensitive_words

    def _safe_extract_field(
        self, data: Any, field_names: List[str], default_value: Any = None
    ) -> Any:
        """
        安全地从数据对象中提取字段值

        Args:
            data: 数据对象（可能是字典、对象或其他类型）
            field_names: 可能的字段名列表（按优先级排序）
            default_value: 默认值

        Returns:
            Any: 提取的字段值或默认值
        """
        if not data:
            return default_value

        # 尝试不同的字段名
        for field_name in field_names:
            try:
                # 如果是字典类型
                if isinstance(data, dict):
                    if field_name in data:
                        return data[field_name]
                # 如果是对象类型
                elif hasattr(data, field_name):
                    return getattr(data, field_name)
                # 如果对象有get方法（类似字典）
                elif hasattr(data, "get"):
                    value = data.get(field_name)
                    if value is not None:
                        return value
            except (AttributeError, KeyError, TypeError):
                continue

        return default_value

    @log_function_call
    def detect_with_fallback(
        self,
        content: str,
        project_info: ProjectInfo,
        request_id: str = "",
        timeout: float = None,
    ) -> List[SensitiveWordItem]:
        """
        带降级机制的敏感词检测

        Args:
            content: 要检测的内容
            project_info: 项目信息
            request_id: 请求ID
            timeout: 请求超时时间（秒），默认使用配置值

        Returns:
            List[SensitiveWordItem]: 敏感词列表，失败时返回空列表
        """
        try:
            # 首先检查服务健康状态
            if not self.check_health(request_id):
                log.warning("敏感词服务不健康，跳过检测")
                return []

            # 执行敏感词检测
            return self.detect_sensitive_words(
                content, project_info, request_id, timeout
            )

        except SensitiveWordError as e:
            log.error(f"敏感词检测失败，使用降级策略: {str(e)}")
            return []
        except Exception as e:
            log.error(f"敏感词检测异常，使用降级策略: {str(e)}")
            return []

    @log_function_call
    def reload_sensitive_words(self, request_id: str = "") -> bool:
        """
        重载敏感词库

        Args:
            request_id: 请求ID

        Returns:
            bool: 重载是否成功
        """
        try:
            with TimingContext("重载敏感词库", request_id):
                log.info(f"重载敏感词库: {self.reload_endpoint}")

                response = self.session.post(
                    self.reload_endpoint, timeout=30  # 重载可能需要较长时间
                )
                response.raise_for_status()

                result = response.json()
                success = result.get("success", False)

                if success:
                    log.info("敏感词库重载成功")
                else:
                    log.warning(
                        f"敏感词库重载失败: {result.get('message', '未知错误')}"
                    )

                return success

        except Exception as e:
            log.error(f"敏感词库重载异常: {str(e)}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务信息

        Returns:
            Dict[str, Any]: 服务信息
        """
        return {
            "base_url": self.base_url,
            "detect_endpoint": self.detect_endpoint,
            "health_endpoint": self.health_endpoint,
            "stats_endpoint": self.stats_endpoint,
            "reload_endpoint": self.reload_endpoint,
            "timeout": settings.request_timeout,
            "max_retries": settings.max_retries,
        }


# 创建全局敏感词检测服务实例
sensitive_word_service = SensitiveWordService()
