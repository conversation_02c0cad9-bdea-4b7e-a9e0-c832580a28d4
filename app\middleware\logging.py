# -*- coding: utf-8 -*-
"""
日志中间件
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logger import log, request_logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())

        # 将请求ID添加到请求状态中，供后续使用
        request.state.request_id = request_id

        # 记录请求开始
        start_time = time.time()

        # 获取请求信息
        method = request.method
        url = str(request.url)
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # 记录请求日志
        log.info(
            f"请求开始 | ID: {request_id} | 方法: {method} | URL: {url} | "
            f"客户端IP: {client_ip} | User-Agent: {user_agent}"
        )

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 记录响应日志
            log.info(
                f"请求完成 | ID: {request_id} | 状态码: {response.status_code} | "
                f"耗时: {process_time:.3f}秒"
            )

            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}"

            return response

        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time

            # 记录错误日志
            log.error(
                f"请求异常 | ID: {request_id} | 错误: {str(e)} | "
                f"耗时: {process_time:.3f}秒"
            )

            # 重新抛出异常，让FastAPI的异常处理器处理
            raise


class RequestSizeMiddleware(BaseHTTPMiddleware):
    """请求大小监控中间件"""

    def __init__(self, app, max_request_size: int = 300 * 1024 * 1024):  # 300MB
        super().__init__(app)
        self.max_request_size = max_request_size

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查请求大小
        content_length = request.headers.get("content-length")
        if content_length:
            content_length = int(content_length)
            if content_length > self.max_request_size:
                log.warning(
                    f"请求大小超限 | ID: {getattr(request.state, 'request_id', 'unknown')} | "
                    f"大小: {content_length} 字节 | 限制: {self.max_request_size} 字节"
                )

        return await call_next(request)
